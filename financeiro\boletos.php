<?php
/**
 * ============================================================================
 * BOLETOS - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gestão completa de boletos bancários
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'gerar':
                $tipo_entidade = $_POST['tipo_entidade'];
                $entidade_id = $_POST['entidade_id'] ?? null;
                $mensalidade_id = $_POST['mensalidade_id'] ?? null;
                $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                $data_vencimento = $_POST['data_vencimento'];
                $observacoes = $_POST['observacoes'] ?? '';

                // Gerar número do boleto (simplificado)
                $numero_boleto = date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $nosso_numero = str_pad(rand(1, 99999999), 8, '0', STR_PAD_LEFT);
                
                // Dados bancários padrão (deveria vir de configuração)
                $banco = '341'; // Itaú
                $agencia = '1234';
                $conta = '12345-6';

                $sql = "INSERT INTO boletos (tipo_entidade, entidade_id, mensalidade_id, numero_boleto, 
                       codigo_barras, linha_digitavel, valor, data_vencimento, data_emissao, 
                       status, banco, agencia, conta, nosso_numero, observacoes, created_at) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURDATE(), 'pendente', ?, ?, ?, ?, ?, NOW())";

                // Gerar código de barras e linha digitável (simplificado)
                $codigo_barras = $banco . '9' . str_replace('-', '', $data_vencimento) . str_pad($valor * 100, 10, '0', STR_PAD_LEFT) . $agencia . $conta . $nosso_numero;
                $linha_digitavel = substr($codigo_barras, 0, 4) . '.' . substr($codigo_barras, 4, 5) . ' ' . 
                                  substr($codigo_barras, 9, 5) . '.' . substr($codigo_barras, 14, 6) . ' ' .
                                  substr($codigo_barras, 20, 5) . '.' . substr($codigo_barras, 25, 6) . ' ' .
                                  substr($codigo_barras, 31, 1) . ' ' . substr($codigo_barras, 32);

                $db->execute($sql, [
                    $tipo_entidade, $entidade_id, $mensalidade_id, $numero_boleto,
                    $codigo_barras, $linha_digitavel, $valor, $data_vencimento,
                    $banco, $agencia, $conta, $nosso_numero, $observacoes
                ]);

                $mensagem = "Boleto gerado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'marcar_pago':
                $data_pagamento = $_POST['data_pagamento'];
                $sql = "UPDATE boletos SET status = 'pago', data_pagamento = ?, updated_at = NOW() WHERE id = ?";
                $db->execute($sql, [$data_pagamento, $id]);

                // Se for boleto de mensalidade, atualizar a mensalidade também
                $boleto = $db->fetchOne("SELECT mensalidade_id FROM boletos WHERE id = ?", [$id]);
                if ($boleto['mensalidade_id']) {
                    $sql = "UPDATE mensalidades_alunos SET status = 'pago', data_pagamento = ?, 
                           forma_pagamento = 'boleto', updated_at = NOW() WHERE id = ?";
                    $db->execute($sql, [$data_pagamento, $boleto['mensalidade_id']]);
                }

                $mensagem = "Boleto marcado como pago com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'cancelar':
                $sql = "UPDATE boletos SET status = 'cancelado', updated_at = NOW() WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Boleto cancelado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "b.status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['tipo_entidade'])) {
        $where_clauses[] = "b.tipo_entidade = ?";
        $params[] = $_GET['tipo_entidade'];
    }
    if (!empty($_GET['data_inicio'])) {
        $where_clauses[] = "b.data_vencimento >= ?";
        $params[] = $_GET['data_inicio'];
    }
    if (!empty($_GET['data_fim'])) {
        $where_clauses[] = "b.data_vencimento <= ?";
        $params[] = $_GET['data_fim'];
    }
    if (!empty($_GET['busca'])) {
        $where_clauses[] = "(b.numero_boleto LIKE ? OR a.nome LIKE ?)";
        $busca = '%' . $_GET['busca'] . '%';
        $params = array_merge($params, [$busca, $busca]);
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    $sql = "SELECT b.*, 
                   CASE 
                       WHEN b.tipo_entidade = 'aluno' THEN a.nome
                       ELSE 'Avulso'
                   END as nome_pagador,
                   CASE 
                       WHEN b.tipo_entidade = 'aluno' THEN a.cpf
                       ELSE ''
                   END as cpf_pagador,
                   ma.descricao as mensalidade_descricao
            FROM boletos b
            LEFT JOIN alunos a ON b.tipo_entidade = 'aluno' AND b.entidade_id = a.id
            LEFT JOIN mensalidades_alunos ma ON b.mensalidade_id = ma.id
            $where_sql 
            ORDER BY b.data_vencimento DESC, b.id DESC";
    
    $boletos = $db->fetchAll($sql, $params);
}

// Busca dados para geração
if ($acao === 'gerar') {
    $alunos = $db->fetchAll("SELECT id, nome, cpf FROM alunos WHERE status = 'ativo' ORDER BY nome");
    $mensalidades = $db->fetchAll("
        SELECT ma.id, ma.descricao, ma.valor, a.nome as aluno_nome 
        FROM mensalidades_alunos ma 
        JOIN alunos a ON ma.aluno_id = a.id 
        WHERE ma.status = 'pendente' 
        ORDER BY a.nome, ma.mes_referencia DESC
    ");
}

// Busca dados para visualização
if ($acao === 'visualizar' && $id) {
    $boleto = $db->fetchOne("
        SELECT b.*, 
               CASE 
                   WHEN b.tipo_entidade = 'aluno' THEN a.nome
                   ELSE 'Avulso'
               END as nome_pagador,
               CASE 
                   WHEN b.tipo_entidade = 'aluno' THEN a.cpf
                   ELSE ''
               END as cpf_pagador,
               ma.descricao as mensalidade_descricao
        FROM boletos b
        LEFT JOIN alunos a ON b.tipo_entidade = 'aluno' AND b.entidade_id = a.id
        LEFT JOIN mensalidades_alunos ma ON b.mensalidade_id = ma.id
        WHERE b.id = ?
    ", [$id]);
    
    if (!$boleto) {
        header('Location: boletos.php?erro=boleto_nao_encontrado');
        exit;
    }
}

$titulo_pagina = "Boletos Bancários";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-barcode text-blue-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gestão de boletos bancários</p>
                    </div>
                    
                    <?php if ($acao === 'listar'): ?>
                        <a href="boletos.php?acao=gerar" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Gerar Boleto
                        </a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'gerar':
                        include 'views/boletos/gerar.php';
                        break;
                    case 'visualizar':
                        include 'views/boletos/visualizar.php';
                        break;
                    default:
                        include 'views/boletos/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script src="../secretaria/js/formatacao.js"></script>
    <script>
        function marcarComoPago(id) {
            const dataAtual = new Date().toISOString().split('T')[0];
            const data = prompt('Data do pagamento (AAAA-MM-DD):', dataAtual);
            
            if (data) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `boletos.php?acao=marcar_pago&id=${id}`;
                form.innerHTML = `<input type="hidden" name="data_pagamento" value="${data}">`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function cancelarBoleto(id, numero) {
            if (confirm(`Tem certeza que deseja cancelar o boleto ${numero}?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `boletos.php?acao=cancelar&id=${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
