<?php
/**
 * ============================================================================
 * COMPROVANTES DE PAGAMENTO - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gestão de comprovantes de pagamento
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'upload':
                $tipo_transacao = $_POST['tipo_transacao'];
                $transacao_id = $_POST['transacao_id'];
                $descricao = $_POST['descricao'] ?? '';
                
                // Verificar se foi enviado arquivo
                if (!isset($_FILES['arquivo']) || $_FILES['arquivo']['error'] !== UPLOAD_ERR_OK) {
                    throw new Exception("Erro no upload do arquivo.");
                }
                
                $arquivo = $_FILES['arquivo'];
                $extensoes_permitidas = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];
                $extensao = strtolower(pathinfo($arquivo['name'], PATHINFO_EXTENSION));
                
                if (!in_array($extensao, $extensoes_permitidas)) {
                    throw new Exception("Tipo de arquivo não permitido. Use: " . implode(', ', $extensoes_permitidas));
                }
                
                // Criar diretório se não existir
                $diretorio_upload = '../uploads/comprovantes/';
                if (!is_dir($diretorio_upload)) {
                    mkdir($diretorio_upload, 0755, true);
                }
                
                // Gerar nome único para o arquivo
                $nome_arquivo = date('Y-m-d_H-i-s') . '_' . uniqid() . '.' . $extensao;
                $caminho_arquivo = $diretorio_upload . $nome_arquivo;
                
                if (!move_uploaded_file($arquivo['tmp_name'], $caminho_arquivo)) {
                    throw new Exception("Erro ao salvar o arquivo.");
                }
                
                // Salvar no banco de dados
                $sql = "INSERT INTO comprovantes_pagamento (tipo_transacao, transacao_id, nome_arquivo, 
                       caminho_arquivo, tamanho_arquivo, tipo_arquivo, descricao, status, created_at) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente', NOW())";
                
                $db->execute($sql, [
                    $tipo_transacao,
                    $transacao_id,
                    $arquivo['name'],
                    $caminho_arquivo,
                    $arquivo['size'],
                    $arquivo['type'],
                    $descricao
                ]);
                
                $mensagem = "Comprovante enviado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
                
            case 'aprovar':
                $sql = "UPDATE comprovantes_pagamento SET status = 'aprovado', data_aprovacao = NOW(), 
                       usuario_aprovacao = ? WHERE id = ?";
                $db->execute($sql, [$_SESSION['usuario_id'], $id]);
                $mensagem = "Comprovante aprovado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
                
            case 'rejeitar':
                $motivo = $_POST['motivo'] ?? '';
                $sql = "UPDATE comprovantes_pagamento SET status = 'rejeitado', motivo_rejeicao = ?, 
                       data_rejeicao = NOW(), usuario_rejeicao = ? WHERE id = ?";
                $db->execute($sql, [$motivo, $_SESSION['usuario_id'], $id]);
                $mensagem = "Comprovante rejeitado!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
                
            case 'excluir':
                // Buscar arquivo para excluir
                $comprovante = $db->fetchOne("SELECT caminho_arquivo FROM comprovantes_pagamento WHERE id = ?", [$id]);
                if ($comprovante && file_exists($comprovante['caminho_arquivo'])) {
                    unlink($comprovante['caminho_arquivo']);
                }
                
                $sql = "DELETE FROM comprovantes_pagamento WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Comprovante excluído com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "cp.status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['tipo_transacao'])) {
        $where_clauses[] = "cp.tipo_transacao = ?";
        $params[] = $_GET['tipo_transacao'];
    }
    if (!empty($_GET['data_inicio'])) {
        $where_clauses[] = "DATE(cp.created_at) >= ?";
        $params[] = $_GET['data_inicio'];
    }
    if (!empty($_GET['data_fim'])) {
        $where_clauses[] = "DATE(cp.created_at) <= ?";
        $params[] = $_GET['data_fim'];
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    $sql = "SELECT cp.*, 
                   u.nome as usuario_nome
            FROM comprovantes_pagamento cp
            LEFT JOIN usuarios u ON cp.usuario_aprovacao = u.id OR cp.usuario_rejeicao = u.id
            $where_sql 
            ORDER BY cp.created_at DESC";
    
    $comprovantes = $db->fetchAll($sql, $params);
}

// Busca dados para upload
if ($acao === 'upload') {
    // Buscar transações recentes para seleção
    $transacoes_pagar = $db->fetchAll("
        SELECT cp.id, cp.descricao, cp.valor, f.nome as fornecedor_nome 
        FROM contas_pagar cp 
        LEFT JOIN fornecedores f ON cp.fornecedor_id = f.id 
        WHERE cp.status = 'pago' 
        ORDER BY cp.data_pagamento DESC 
        LIMIT 20
    ");
    
    $transacoes_receber = $db->fetchAll("
        SELECT cr.id, cr.descricao, cr.valor, cr.cliente_nome 
        FROM contas_receber cr 
        WHERE cr.status = 'recebido' 
        ORDER BY cr.data_recebimento DESC 
        LIMIT 20
    ");
}

$titulo_pagina = "Comprovantes de Pagamento";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-file-upload text-purple-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gestão de comprovantes e documentos financeiros</p>
                    </div>
                    
                    <?php if ($acao === 'listar'): ?>
                        <a href="comprovantes.php?acao=upload" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-upload mr-2"></i>
                            Enviar Comprovante
                        </a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'upload':
                        include 'views/comprovantes/upload.php';
                        break;
                    case 'visualizar':
                        include 'views/comprovantes/visualizar.php';
                        break;
                    default:
                        include 'views/comprovantes/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        function aprovarComprovante(id) {
            if (confirm('Tem certeza que deseja aprovar este comprovante?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `comprovantes.php?acao=aprovar&id=${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function rejeitarComprovante(id) {
            const motivo = prompt('Motivo da rejeição:');
            if (motivo) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `comprovantes.php?acao=rejeitar&id=${id}`;
                form.innerHTML = `<input type="hidden" name="motivo" value="${motivo}">`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function excluirComprovante(id) {
            if (confirm('Tem certeza que deseja excluir este comprovante?\n\nEsta ação não pode ser desfeita.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `comprovantes.php?acao=excluir&id=${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
