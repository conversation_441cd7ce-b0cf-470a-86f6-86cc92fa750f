<?php
/**
 * ============================================================================
 * CONFIGURAÇÕES - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Configurações gerais do módulo financeiro
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!verificarPermissao('financeiro', 'administrar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'geral';
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['form_tipo']) {
            case 'categoria':
                $nome = $_POST['nome'];
                $tipo = $_POST['tipo'];
                $descricao = $_POST['descricao'] ?? '';
                $id = $_POST['id'] ?? null;

                if ($id) {
                    // Atualizar categoria
                    $sql = "UPDATE categorias_financeiras SET nome = ?, tipo = ?, descricao = ? WHERE id = ?";
                    $db->execute($sql, [$nome, $tipo, $descricao, $id]);
                    $mensagem = "Categoria atualizada com sucesso!";
                } else {
                    // Inserir nova categoria
                    $sql = "INSERT INTO categorias_financeiras (nome, tipo, descricao, status, data_criacao) VALUES (?, ?, ?, 'ativo', NOW())";
                    $db->execute($sql, [$nome, $tipo, $descricao]);
                    $mensagem = "Categoria cadastrada com sucesso!";
                }
                $tipo_mensagem = 'success';
                break;

            case 'forma_pagamento':
                $nome = $_POST['nome'];
                $descricao = $_POST['descricao'] ?? '';
                $id = $_POST['id'] ?? null;

                if ($id) {
                    // Atualizar forma de pagamento
                    $sql = "UPDATE formas_pagamento SET nome = ?, descricao = ? WHERE id = ?";
                    $db->execute($sql, [$nome, $descricao, $id]);
                    $mensagem = "Forma de pagamento atualizada com sucesso!";
                } else {
                    // Inserir nova forma de pagamento
                    $sql = "INSERT INTO formas_pagamento (nome, descricao, status, data_criacao) VALUES (?, ?, 'ativo', NOW())";
                    $db->execute($sql, [$nome, $descricao]);
                    $mensagem = "Forma de pagamento cadastrada com sucesso!";
                }
                $tipo_mensagem = 'success';
                break;

            case 'excluir_categoria':
                $id = $_POST['id'];
                $sql = "DELETE FROM categorias_financeiras WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Categoria excluída com sucesso!";
                $tipo_mensagem = 'success';
                break;

            case 'excluir_forma':
                $id = $_POST['id'];
                $sql = "DELETE FROM formas_pagamento WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Forma de pagamento excluída com sucesso!";
                $tipo_mensagem = 'success';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados
$categorias = $db->fetchAll("SELECT * FROM categorias_financeiras ORDER BY nome");
$formas_pagamento = $db->fetchAll("SELECT * FROM formas_pagamento ORDER BY nome");

$titulo_pagina = "Configurações Financeiras";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-cog text-gray-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Configurações gerais do módulo financeiro</p>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <!-- Abas de Configuração -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8 px-6">
                            <a href="?acao=geral" class="<?php echo $acao === 'geral' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                <i class="fas fa-sliders-h mr-2"></i>
                                Geral
                            </a>
                            <a href="?acao=categorias" class="<?php echo $acao === 'categorias' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                <i class="fas fa-tags mr-2"></i>
                                Categorias
                            </a>
                            <a href="?acao=formas_pagamento" class="<?php echo $acao === 'formas_pagamento' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                <i class="fas fa-credit-card mr-2"></i>
                                Formas de Pagamento
                            </a>
                        </nav>
                    </div>
                </div>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'categorias':
                        include 'views/configuracoes/categorias.php';
                        break;
                    case 'formas_pagamento':
                        include 'views/configuracoes/formas_pagamento.php';
                        break;
                    default:
                        include 'views/configuracoes/geral.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        function confirmarExclusao(id, nome, tipo) {
            if (confirm(`Tem certeza que deseja excluir "${nome}"?`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="form_tipo" value="excluir_${tipo}">
                    <input type="hidden" name="id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function editarItem(id, nome, tipo, descricao, tipoCategoria) {
            if (tipo === 'categoria') {
                document.getElementById('categoria_id').value = id;
                document.getElementById('categoria_nome').value = nome;
                document.getElementById('categoria_tipo').value = tipoCategoria;
                document.getElementById('categoria_descricao').value = descricao || '';
                document.getElementById('btn_categoria').textContent = 'Atualizar Categoria';
            } else if (tipo === 'forma') {
                document.getElementById('forma_id').value = id;
                document.getElementById('forma_nome').value = nome;
                document.getElementById('forma_descricao').value = descricao || '';
                document.getElementById('btn_forma').textContent = 'Atualizar Forma';
            }
        }

        function limparFormulario(tipo) {
            if (tipo === 'categoria') {
                document.getElementById('categoria_id').value = '';
                document.getElementById('categoria_nome').value = '';
                document.getElementById('categoria_tipo').value = '';
                document.getElementById('categoria_descricao').value = '';
                document.getElementById('btn_categoria').textContent = 'Cadastrar Categoria';
            } else if (tipo === 'forma') {
                document.getElementById('forma_id').value = '';
                document.getElementById('forma_nome').value = '';
                document.getElementById('forma_descricao').value = '';
                document.getElementById('btn_forma').textContent = 'Cadastrar Forma';
            }
        }
    </script>
</body>
</html>
