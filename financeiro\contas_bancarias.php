<?php
/**
 * ============================================================================
 * CONTAS BANCÁRIAS - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gestão completa de contas bancárias
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'salvar':
                $dados = [
                    'nome' => $_POST['nome'],
                    'banco' => $_POST['banco'],
                    'agencia' => $_POST['agencia'],
                    'conta' => $_POST['conta'],
                    'tipo_conta' => $_POST['tipo_conta'],
                    'saldo_inicial' => str_replace(['.', ','], ['', '.'], $_POST['saldo_inicial']),
                    'observacoes' => $_POST['observacoes'] ?? '',
                    'status' => $_POST['status'] ?? 'ativa'
                ];

                if ($id) {
                    // Atualizar
                    $sql = "UPDATE contas_bancarias SET nome = ?, banco = ?, agencia = ?, conta = ?, 
                           tipo_conta = ?, saldo_inicial = ?, observacoes = ?, status = ?, 
                           updated_at = NOW() WHERE id = ?";
                    $params = array_values($dados);
                    $params[] = $id;
                    $db->execute($sql, $params);
                    $mensagem = "Conta bancária atualizada com sucesso!";
                } else {
                    // Inserir
                    $sql = "INSERT INTO contas_bancarias (nome, banco, agencia, conta, tipo_conta, 
                           saldo_inicial, observacoes, status, created_at) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $db->execute($sql, array_values($dados));
                    $mensagem = "Conta bancária cadastrada com sucesso!";
                }
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'excluir':
                $sql = "DELETE FROM contas_bancarias WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Conta bancária excluída com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['banco'])) {
        $where_clauses[] = "banco LIKE ?";
        $params[] = '%' . $_GET['banco'] . '%';
    }
    if (!empty($_GET['busca'])) {
        $where_clauses[] = "(nome LIKE ? OR banco LIKE ? OR agencia LIKE ? OR conta LIKE ?)";
        $busca = '%' . $_GET['busca'] . '%';
        $params = array_merge($params, [$busca, $busca, $busca, $busca]);
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    // Verificar se tabela de transferências existe
    $tabela_transferencias_existe = false;
    try {
        $db->fetchOne("SELECT 1 FROM transferencias_bancarias LIMIT 1");
        $tabela_transferencias_existe = true;
    } catch (Exception $e) {
        // Tabela não existe
    }

    if ($tabela_transferencias_existe) {
        // Incluir transferências no cálculo
        $sql = "SELECT cb.*,
                       (SELECT COUNT(*) FROM transacoes_financeiras WHERE conta_bancaria_id = cb.id) as total_transacoes,
                       (COALESCE(
                           (SELECT SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END)
                            FROM transacoes_financeiras WHERE conta_bancaria_id = cb.id), 0
                       ) +
                       COALESCE(
                           (SELECT SUM(valor) FROM transferencias_bancarias
                            WHERE conta_destino_id = cb.id AND status = 'processada'), 0
                       ) -
                       COALESCE(
                           (SELECT SUM(valor + tarifa) FROM transferencias_bancarias
                            WHERE conta_origem_id = cb.id AND status = 'processada'), 0
                       )) as saldo_movimentacoes
                FROM contas_bancarias cb
                $where_sql
                ORDER BY cb.nome ASC";
    } else {
        // Fallback sem transferências
        $sql = "SELECT cb.*,
                       (SELECT COUNT(*) FROM transacoes_financeiras WHERE conta_bancaria_id = cb.id) as total_transacoes,
                       (SELECT COALESCE(SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END), 0)
                        FROM transacoes_financeiras WHERE conta_bancaria_id = cb.id) as saldo_movimentacoes
                FROM contas_bancarias cb
                $where_sql
                ORDER BY cb.nome ASC";
    }
    
    $contas = $db->fetchAll($sql, $params);
}

// Busca dados para edição
if (($acao === 'editar' || $acao === 'visualizar') && $id) {
    $conta = $db->fetchOne("SELECT * FROM contas_bancarias WHERE id = ?", [$id]);
    if (!$conta) {
        header('Location: contas_bancarias.php?erro=conta_nao_encontrada');
        exit;
    }
}

$titulo_pagina = "Contas Bancárias";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-university text-green-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gestão de contas bancárias da instituição</p>
                    </div>
                    
                    <?php if ($acao === 'listar'): ?>
                        <a href="contas_bancarias.php?acao=nova" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Nova Conta
                        </a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'nova':
                    case 'editar':
                        include 'views/contas_bancarias/formulario.php';
                        break;
                    case 'visualizar':
                        include 'views/contas_bancarias/visualizar.php';
                        break;
                    default:
                        include 'views/contas_bancarias/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script src="../secretaria/js/formatacao.js"></script>
    <script>
        function confirmarExclusao(id, nome) {
            if (confirm(`Tem certeza que deseja excluir a conta "${nome}"?\n\nEsta ação não pode ser desfeita.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `contas_bancarias.php?acao=excluir&id=${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
