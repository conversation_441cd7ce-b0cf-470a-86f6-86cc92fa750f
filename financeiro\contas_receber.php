<?php
/**
 * ============================================================================
 * CONTAS A RECEBER - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gerenciamento completo de contas a receber da instituição
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!verificarPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'salvar':
                $dados = [
                    'descricao' => $_POST['descricao'],
                    'valor' => str_replace(['.', ','], ['', '.'], $_POST['valor']),
                    'data_vencimento' => $_POST['data_vencimento'],
                    'categoria_id' => $_POST['categoria_id'],
                    'cliente_nome' => $_POST['cliente_nome'],
                    'observacoes' => $_POST['observacoes'] ?? '',
                    'status' => 'pendente',
                    'data_criacao' => date('Y-m-d H:i:s'),
                    'usuario_id' => $_SESSION['usuario_id']
                ];

                if ($id) {
                    // Atualizar
                    $sql = "UPDATE contas_receber SET descricao = ?, valor = ?, data_vencimento = ?, 
                           categoria_id = ?, cliente_nome = ?, observacoes = ?, data_atualizacao = NOW() 
                           WHERE id = ?";
                    $params = array_values($dados);
                    unset($params[6], $params[7]); // Remove status, data_criacao, usuario_id
                    $params[] = $id;
                    $db->execute($sql, $params);
                    $mensagem = "Conta a receber atualizada com sucesso!";
                } else {
                    // Inserir
                    $sql = "INSERT INTO contas_receber (descricao, valor, data_vencimento, categoria_id, 
                           cliente_nome, observacoes, status, data_criacao, usuario_id) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $db->execute($sql, array_values($dados));
                    $mensagem = "Conta a receber cadastrada com sucesso!";
                }
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'receber':
                $valor_recebido = str_replace(['.', ','], ['', '.'], $_POST['valor_recebido']);
                $data_recebimento = $_POST['data_recebimento'];
                $forma_pagamento = $_POST['forma_pagamento'];
                $observacoes_recebimento = $_POST['observacoes_recebimento'] ?? '';

                // Atualiza a conta
                $sql = "UPDATE contas_receber SET status = 'recebido', valor_recebido = ?, data_recebimento = ?, 
                       forma_pagamento = ?, observacoes_recebimento = ?, data_atualizacao = NOW() 
                       WHERE id = ?";
                $db->execute($sql, [$valor_recebido, $data_recebimento, $forma_pagamento, $observacoes_recebimento, $id]);

                // Registra a transação
                $conta = $db->fetchOne("SELECT * FROM contas_receber WHERE id = ?", [$id]);
                $sql_transacao = "INSERT INTO transacoes_financeiras (tipo, descricao, valor, data_transacao, 
                                 forma_pagamento, categoria_id, referencia_tipo, referencia_id, usuario_id) 
                                 VALUES ('receita', ?, ?, ?, ?, ?, 'conta_receber', ?, ?)";
                $db->execute($sql_transacao, [
                    $conta['descricao'], $valor_recebido, $data_recebimento, $forma_pagamento,
                    $conta['categoria_id'], $id, $_SESSION['usuario_id']
                ]);

                $mensagem = "Recebimento registrado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'excluir':
                $sql = "DELETE FROM contas_receber WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Conta a receber excluída com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "cr.status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['categoria'])) {
        $where_clauses[] = "cr.categoria_id = ?";
        $params[] = $_GET['categoria'];
    }
    if (!empty($_GET['data_inicio'])) {
        $where_clauses[] = "cr.data_vencimento >= ?";
        $params[] = $_GET['data_inicio'];
    }
    if (!empty($_GET['data_fim'])) {
        $where_clauses[] = "cr.data_vencimento <= ?";
        $params[] = $_GET['data_fim'];
    }
    if (!empty($_GET['busca'])) {
        $where_clauses[] = "(cr.descricao LIKE ? OR cr.cliente_nome LIKE ?)";
        $params[] = '%' . $_GET['busca'] . '%';
        $params[] = '%' . $_GET['busca'] . '%';
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    $sql = "SELECT cr.*, cf.nome as categoria_nome 
            FROM contas_receber cr 
            LEFT JOIN categorias_financeiras cf ON cr.categoria_id = cf.id 
            $where_sql 
            ORDER BY cr.data_vencimento ASC, cr.id DESC";
    
    $contas = $db->fetchAll($sql, $params);

    // Busca categorias para filtro
    $categorias = $db->fetchAll("SELECT * FROM categorias_financeiras WHERE tipo IN ('receita', 'ambos') ORDER BY nome");
}

// Busca dados para formulário
if (in_array($acao, ['nova', 'editar'])) {
    $categorias = $db->fetchAll("SELECT * FROM categorias_financeiras WHERE tipo IN ('receita', 'ambos') ORDER BY nome");
    
    if ($acao === 'editar' && $id) {
        $conta = $db->fetchOne("SELECT * FROM contas_receber WHERE id = ?", [$id]);
        if (!$conta) {
            header('Location: contas_receber.php?erro=conta_nao_encontrada');
            exit;
        }
    }
}

// Busca dados para recebimento
if ($acao === 'receber' && $id) {
    $conta = $db->fetchOne("SELECT * FROM contas_receber WHERE id = ?", [$id]);
    if (!$conta || $conta['status'] !== 'pendente') {
        header('Location: contas_receber.php?erro=conta_nao_encontrada');
        exit;
    }
}

$titulo_pagina = "Contas a Receber";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-hand-holding-usd text-green-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gerenciamento de contas a receber da instituição</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <?php if ($acao === 'listar'): ?>
                            <a href="contas_receber.php?acao=nova" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Nova Conta
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'listar':
                        include 'views/contas_receber/listar.php';
                        break;
                    case 'nova':
                    case 'editar':
                        include 'views/contas_receber/formulario.php';
                        break;
                    case 'receber':
                        include 'views/contas_receber/receber.php';
                        break;
                    case 'visualizar':
                        include 'views/contas_receber/visualizar.php';
                        break;
                    default:
                        include 'views/contas_receber/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        // Formatação de valores monetários
        function formatarMoeda(input) {
            let valor = input.value.replace(/\D/g, '');
            valor = (valor / 100).toFixed(2) + '';
            valor = valor.replace(".", ",");
            valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
            input.value = valor;
        }

        // Confirmação de exclusão
        function confirmarExclusao(id, descricao) {
            if (confirm(`Tem certeza que deseja excluir a conta "${descricao}"?`)) {
                window.location.href = `contas_receber.php?acao=excluir&id=${id}`;
            }
        }

        // Auto-submit do formulário de filtros
        document.addEventListener('DOMContentLoaded', function() {
            const filtros = document.querySelectorAll('.filtro-auto');
            filtros.forEach(filtro => {
                filtro.addEventListener('change', function() {
                    document.getElementById('form-filtros').submit();
                });
            });
        });
    </script>
</body>
</html>
