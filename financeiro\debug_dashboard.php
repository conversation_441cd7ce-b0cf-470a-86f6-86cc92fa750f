<?php
/**
 * Debug específico do dashboard para identificar problemas
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

$db = Database::getInstance();

echo "<h1>🐛 Debug do Dashboard Financeiro</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .aviso { color: orange; }
    .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>";

echo "<div class='debug'>";
echo "<h2>🔍 Executando o mesmo código do dashboard...</h2>";

try {
    echo "<h3>1. Testando consulta de indicadores</h3>";
    
    // Mesma consulta do dashboard
    $sql_indicadores = "
        SELECT 
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente') as contas_pagar_pendentes,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente') as contas_receber_pendentes,
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_pagar_vencidas,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_receber_vencidas,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar WHERE status = 'pendente') as total_pagar,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_receber WHERE status = 'pendente') as total_receber,
            (SELECT COALESCE(SUM(saldo_inicial), 0) FROM contas_bancarias WHERE status = 'ativo') as saldo_total_bancos
    ";
    
    echo "<pre>$sql_indicadores</pre>";
    
    $indicadores = $db->fetchOne($sql_indicadores);
    
    echo "<h4>✅ Resultado da consulta:</h4>";
    echo "<pre>";
    print_r($indicadores);
    echo "</pre>";
    
    echo "<h4>📊 Valores formatados:</h4>";
    echo "<ul>";
    echo "<li><strong>Contas a Pagar Pendentes:</strong> " . ($indicadores['contas_pagar_pendentes'] ?? 'NULL') . "</li>";
    echo "<li><strong>Contas a Receber Pendentes:</strong> " . ($indicadores['contas_receber_pendentes'] ?? 'NULL') . "</li>";
    echo "<li><strong>Total a Pagar:</strong> R$ " . number_format($indicadores['total_pagar'] ?? 0, 2, ',', '.') . "</li>";
    echo "<li><strong>Total a Receber:</strong> R$ " . number_format($indicadores['total_receber'] ?? 0, 2, ',', '.') . "</li>";
    echo "<li><strong>Saldo Total Bancos:</strong> R$ " . number_format($indicadores['saldo_total_bancos'] ?? 0, 2, ',', '.') . "</li>";
    echo "</ul>";

    echo "<h3>2. Testando fluxo de caixa</h3>";
    
    // Fluxo de caixa dos últimos 30 dias (baseado em contas pagas/recebidas)
    $sql_fluxo = "
        SELECT 
            DATE(COALESCE(data_recebimento, data_vencimento)) as data,
            SUM(CASE WHEN data_recebimento IS NOT NULL THEN valor ELSE 0 END) as receitas,
            0 as despesas
        FROM contas_receber 
        WHERE (data_recebimento >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) OR 
               (data_recebimento IS NULL AND data_vencimento >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)))
        GROUP BY DATE(COALESCE(data_recebimento, data_vencimento))
        
        UNION ALL
        
        SELECT 
            DATE(COALESCE(data_pagamento, data_vencimento)) as data,
            0 as receitas,
            SUM(CASE WHEN data_pagamento IS NOT NULL THEN valor ELSE 0 END) as despesas
        FROM contas_pagar 
        WHERE (data_pagamento >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) OR 
               (data_pagamento IS NULL AND data_vencimento >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)))
        GROUP BY DATE(COALESCE(data_pagamento, data_vencimento))
        
        ORDER BY data DESC
        LIMIT 30
    ";
    
    echo "<pre>$sql_fluxo</pre>";
    
    $fluxo_caixa_raw = $db->fetchAll($sql_fluxo);
    
    echo "<h4>✅ Resultado do fluxo de caixa:</h4>";
    echo "<pre>";
    print_r($fluxo_caixa_raw);
    echo "</pre>";

    echo "<h3>3. Testando vencimentos</h3>";
    
    $sql_vencimentos = "
        (SELECT 'pagar' as tipo, id, descricao, valor, data_vencimento, 
                COALESCE(fornecedor_nome, 'Não informado') as terceiro
         FROM contas_pagar 
         WHERE status = 'pendente' AND data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         ORDER BY data_vencimento ASC
         LIMIT 10)
        UNION ALL
        (SELECT 'receber' as tipo, id, descricao, valor, data_vencimento, 
                COALESCE(cliente_nome, 'Não informado') as terceiro
         FROM contas_receber 
         WHERE status = 'pendente' AND data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         ORDER BY data_vencimento ASC
         LIMIT 10)
        ORDER BY data_vencimento ASC
    ";
    
    echo "<pre>$sql_vencimentos</pre>";
    
    $vencimentos = $db->fetchAll($sql_vencimentos);
    
    echo "<h4>✅ Resultado dos vencimentos:</h4>";
    echo "<pre>";
    print_r($vencimentos);
    echo "</pre>";

    echo "<h3>4. Testando transações</h3>";
    
    $sql_transacoes = "
        SELECT tipo, descricao, valor, data_transacao, forma_pagamento
        FROM transacoes_financeiras 
        ORDER BY data_transacao DESC, id DESC
        LIMIT 10
    ";
    
    echo "<pre>$sql_transacoes</pre>";
    
    $ultimas_transacoes = $db->fetchAll($sql_transacoes);
    
    echo "<h4>✅ Resultado das transações:</h4>";
    echo "<pre>";
    print_r($ultimas_transacoes);
    echo "</pre>";

    echo "<h2>🎉 Todas as consultas executadas com sucesso!</h2>";
    echo "<p class='ok'>✅ Se você está vendo valores aqui mas zeros no dashboard, o problema pode estar na view HTML.</p>";

} catch (Exception $e) {
    echo "<h2>❌ ERRO ENCONTRADO!</h2>";
    echo "<p class='erro'>Erro: " . $e->getMessage() . "</p>";
    echo "<p class='erro'>Arquivo: " . $e->getFile() . "</p>";
    echo "<p class='erro'>Linha: " . $e->getLine() . "</p>";
    echo "<pre class='erro'>" . $e->getTraceAsString() . "</pre>";
}

echo "</div>";

echo "<hr>";
echo "<p><a href='index.php'>← Voltar para o Dashboard</a></p>";
echo "<p><a href='teste_consultas.php'>🔍 Teste de Consultas Individuais</a></p>";
?>
