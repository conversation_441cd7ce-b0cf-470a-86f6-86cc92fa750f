<?php
/**
 * Diagnóstico do Módulo Financeiro
 * Verifica se os dados estão sendo calculados corretamente
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

echo "<h1>🔍 Diagnóstico do Módulo Financeiro</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .aviso { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
</style>";

$db = Database::getInstance();

echo "<div class='section'>";
echo "<h2>📊 Verificação de Dados nas Tabelas</h2>";

$tabelas = [
    'contas_pagar' => 'Contas a Pagar',
    'contas_receber' => 'Contas a Receber', 
    'contas_bancarias' => 'Contas Bancárias',
    'transacoes_financeiras' => 'Transações Financeiras',
    'mensalidades_alunos' => 'Mensalidades',
    'boletos' => 'Boletos',
    'categorias_financeiras' => 'Categorias Financeiras'
];

echo "<table>";
echo "<tr><th>Tabela</th><th>Total de Registros</th><th>Status</th></tr>";

foreach ($tabelas as $tabela => $nome) {
    try {
        $resultado = $db->fetchOne("SELECT COUNT(*) as total FROM $tabela");
        $total = $resultado['total'];
        $status = $total > 0 ? "<span class='ok'>✅ Com dados</span>" : "<span class='aviso'>⚠️ Vazia</span>";
        echo "<tr><td>$nome</td><td>$total</td><td>$status</td></tr>";
    } catch (Exception $e) {
        echo "<tr><td>$nome</td><td>-</td><td><span class='erro'>❌ Erro: " . $e->getMessage() . "</span></td></tr>";
    }
}
echo "</table>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>💰 Verificação de Valores - Contas a Pagar</h2>";

try {
    $contas_pagar = $db->fetchAll("SELECT status, COUNT(*) as qtd, COALESCE(SUM(valor), 0) as total FROM contas_pagar GROUP BY status");
    echo "<table>";
    echo "<tr><th>Status</th><th>Quantidade</th><th>Valor Total</th></tr>";
    foreach ($contas_pagar as $conta) {
        echo "<tr><td>{$conta['status']}</td><td>{$conta['qtd']}</td><td>R$ " . number_format($conta['total'], 2, ',', '.') . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>💰 Verificação de Valores - Contas a Receber</h2>";

try {
    $contas_receber = $db->fetchAll("SELECT status, COUNT(*) as qtd, COALESCE(SUM(valor), 0) as total FROM contas_receber GROUP BY status");
    echo "<table>";
    echo "<tr><th>Status</th><th>Quantidade</th><th>Valor Total</th></tr>";
    foreach ($contas_receber as $conta) {
        echo "<tr><td>{$conta['status']}</td><td>{$conta['qtd']}</td><td>R$ " . number_format($conta['total'], 2, ',', '.') . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🏦 Verificação de Saldos Bancários</h2>";

try {
    $contas_bancarias = $db->fetchAll("SELECT nome, banco, saldo_inicial, status FROM contas_bancarias ORDER BY nome");
    echo "<table>";
    echo "<tr><th>Nome</th><th>Banco</th><th>Saldo Inicial</th><th>Status</th></tr>";
    $total_saldo = 0;
    foreach ($contas_bancarias as $conta) {
        $total_saldo += $conta['saldo_inicial'];
        echo "<tr><td>{$conta['nome']}</td><td>{$conta['banco']}</td><td>R$ " . number_format($conta['saldo_inicial'], 2, ',', '.') . "</td><td>{$conta['status']}</td></tr>";
    }
    echo "<tr style='background-color: #f0f0f0; font-weight: bold;'><td colspan='2'>TOTAL</td><td>R$ " . number_format($total_saldo, 2, ',', '.') . "</td><td>-</td></tr>";
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>📈 Verificação de Transações Financeiras</h2>";

try {
    $transacoes = $db->fetchAll("SELECT tipo, COUNT(*) as qtd, COALESCE(SUM(valor), 0) as total FROM transacoes_financeiras GROUP BY tipo");
    echo "<table>";
    echo "<tr><th>Tipo</th><th>Quantidade</th><th>Valor Total</th></tr>";
    foreach ($transacoes as $transacao) {
        echo "<tr><td>{$transacao['tipo']}</td><td>{$transacao['qtd']}</td><td>R$ " . number_format($transacao['total'], 2, ',', '.') . "</td></tr>";
    }
    echo "</table>";
    
    // Últimas 10 transações
    echo "<h3>Últimas 10 Transações</h3>";
    $ultimas = $db->fetchAll("SELECT * FROM transacoes_financeiras ORDER BY data_transacao DESC, id DESC LIMIT 10");
    echo "<table>";
    echo "<tr><th>Data</th><th>Tipo</th><th>Descrição</th><th>Valor</th><th>Status</th></tr>";
    foreach ($ultimas as $transacao) {
        echo "<tr><td>" . date('d/m/Y', strtotime($transacao['data_transacao'])) . "</td><td>{$transacao['tipo']}</td><td>{$transacao['descricao']}</td><td>R$ " . number_format($transacao['valor'], 2, ',', '.') . "</td><td>{$transacao['status']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🎓 Verificação de Mensalidades</h2>";

try {
    $mensalidades = $db->fetchAll("SELECT status, COUNT(*) as qtd, COALESCE(SUM(valor), 0) as total FROM mensalidades_alunos GROUP BY status");
    echo "<table>";
    echo "<tr><th>Status</th><th>Quantidade</th><th>Valor Total</th></tr>";
    foreach ($mensalidades as $mensalidade) {
        echo "<tr><td>{$mensalidade['status']}</td><td>{$mensalidade['qtd']}</td><td>R$ " . number_format($mensalidade['total'], 2, ',', '.') . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🧾 Verificação de Boletos</h2>";

try {
    $boletos = $db->fetchAll("SELECT status, COUNT(*) as qtd, COALESCE(SUM(valor), 0) as total FROM boletos GROUP BY status");
    echo "<table>";
    echo "<tr><th>Status</th><th>Quantidade</th><th>Valor Total</th></tr>";
    foreach ($boletos as $boleto) {
        echo "<tr><td>{$boleto['status']}</td><td>{$boleto['qtd']}</td><td>R$ " . number_format($boleto['total'], 2, ',', '.') . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔧 Teste das Consultas do Dashboard</h2>";

try {
    $sql_indicadores = "
        SELECT 
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente') as contas_pagar_pendentes,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente') as contas_receber_pendentes,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar WHERE status = 'pendente') as total_pagar,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_receber WHERE status = 'pendente') as total_receber,
            (SELECT COALESCE(SUM(saldo_inicial), 0) FROM contas_bancarias WHERE status = 'ativo') as saldo_total_bancos
    ";
    $indicadores = $db->fetchOne($sql_indicadores);
    
    echo "<table>";
    echo "<tr><th>Indicador</th><th>Valor</th></tr>";
    echo "<tr><td>Contas a Pagar Pendentes</td><td>{$indicadores['contas_pagar_pendentes']}</td></tr>";
    echo "<tr><td>Contas a Receber Pendentes</td><td>{$indicadores['contas_receber_pendentes']}</td></tr>";
    echo "<tr><td>Total a Pagar</td><td>R$ " . number_format($indicadores['total_pagar'], 2, ',', '.') . "</td></tr>";
    echo "<tr><td>Total a Receber</td><td>R$ " . number_format($indicadores['total_receber'], 2, ',', '.') . "</td></tr>";
    echo "<tr><td>Saldo Total dos Bancos</td><td>R$ " . number_format($indicadores['saldo_total_bancos'], 2, ',', '.') . "</td></tr>";
    echo "</table>";
} catch (Exception $e) {
    echo "<span class='erro'>❌ Erro nas consultas do dashboard: " . $e->getMessage() . "</span>";
}
echo "</div>";

echo "<hr>";
echo "<p><a href='index.php'>← Voltar para o Dashboard</a></p>";
?>
