<?php
/**
 * Exemplo visual da lógica correta das transferências
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

echo "<h1>💡 Lógica Correta das Transferências</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .exemplo { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
    .enviado { border-left: 4px solid #dc3545; }
    .recebido { border-left: 4px solid #28a745; }
    .interno { border-left: 4px solid #007bff; }
    .operacao { border-left: 4px solid #6c757d; }
    .conta { background: #e9ecef; padding: 8px; border-radius: 4px; margin: 5px 0; }
    .terceiro { background: #fff3cd; padding: 8px; border-radius: 4px; margin: 5px 0; }
    .seta-saida { color: #dc3545; }
    .seta-entrada { color: #28a745; }
    h3 { margin-top: 30px; }
</style>";

echo "<h2>🎯 Como Funciona na Vida Real</h2>";

echo "<h3>📤 Operações de SAÍDA (você envia dinheiro)</h3>";

echo "<div class='exemplo enviado'>";
echo "<h4>PIX Enviado - R$ 500,00</h4>";
echo "<p><strong>Cenário:</strong> Você enviou um PIX de R$ 500 para um cliente</p>";
echo "<div class='conta'>🏦 Sua Conta Corrente Itaú <span class='seta-saida'>→ -R$ 500,00</span></div>";
echo "<div class='terceiro'>👤 Cliente João Silva (Chave PIX: <EMAIL>)</div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Conta Origem:</strong> Sua Conta Corrente Itaú (obrigatório)</li>";
echo "<li>❌ <strong>Conta Destino:</strong> Não seleciona (é um terceiro)</li>";
echo "<li>✅ <strong>Dados PIX:</strong> Chave PIX, Nome do favorecido</li>";
echo "</ul>";
echo "</div>";

echo "<div class='exemplo enviado'>";
echo "<h4>TED Enviado - R$ 2.500,00</h4>";
echo "<p><strong>Cenário:</strong> Você pagou um fornecedor via TED</p>";
echo "<div class='conta'>🏦 Sua Conta Empresarial Bradesco <span class='seta-saida'>→ -R$ 2.515,00</span> (valor + tarifa)</div>";
echo "<div class='terceiro'>🏢 Fornecedor XYZ Ltda (Banco do Brasil - Ag: 1234-5 - CC: 67890-1)</div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Conta Origem:</strong> Sua Conta Empresarial Bradesco (obrigatório)</li>";
echo "<li>❌ <strong>Conta Destino:</strong> Não seleciona (é um terceiro)</li>";
echo "<li>✅ <strong>Dados Bancários:</strong> Banco, Agência, Conta, Nome, CPF/CNPJ</li>";
echo "<li>✅ <strong>Tarifa:</strong> R$ 15,00</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📥 Operações de ENTRADA (você recebe dinheiro)</h3>";

echo "<div class='exemplo recebido'>";
echo "<h4>PIX Recebido - R$ 850,00</h4>";
echo "<p><strong>Cenário:</strong> Um cliente te pagou via PIX</p>";
echo "<div class='terceiro'>👤 Cliente Maria Santos (Chave PIX: 123.456.789-00)</div>";
echo "<div class='conta'>🏦 Sua Conta Corrente Santander <span class='seta-entrada'>← +R$ 850,00</span></div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>❌ <strong>Conta Origem:</strong> Não seleciona (é um terceiro)</li>";
echo "<li>✅ <strong>Conta Destino:</strong> Sua Conta Corrente Santander (obrigatório)</li>";
echo "<li>✅ <strong>Dados PIX:</strong> Chave PIX, Nome do remetente</li>";
echo "</ul>";
echo "</div>";

echo "<div class='exemplo recebido'>";
echo "<h4>Depósito - R$ 1.200,00</h4>";
echo "<p><strong>Cenário:</strong> Você fez um depósito em dinheiro na sua conta</p>";
echo "<div class='terceiro'>💰 Depósito em Dinheiro (Agência bancária)</div>";
echo "<div class='conta'>🏦 Sua Conta Poupança Caixa <span class='seta-entrada'>← +R$ 1.200,00</span></div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>❌ <strong>Conta Origem:</strong> Não seleciona (é dinheiro físico)</li>";
echo "<li>✅ <strong>Conta Destino:</strong> Sua Conta Poupança Caixa (obrigatório)</li>";
echo "<li>✅ <strong>Descrição:</strong> 'Depósito em dinheiro na agência'</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔄 Operações INTERNAS (entre suas próprias contas)</h3>";

echo "<div class='exemplo interno'>";
echo "<h4>Transferência Interna - R$ 3.000,00</h4>";
echo "<p><strong>Cenário:</strong> Você transferiu dinheiro entre suas próprias contas</p>";
echo "<div class='conta'>🏦 Sua Conta Corrente Itaú <span class='seta-saida'>→ -R$ 3.000,00</span></div>";
echo "<div style='text-align: center; margin: 10px 0;'>⬇️ Transferência Interna ⬇️</div>";
echo "<div class='conta'>🏦 Sua Conta Poupança Itaú <span class='seta-entrada'>← +R$ 3.000,00</span></div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Conta Origem:</strong> Sua Conta Corrente Itaú (obrigatório)</li>";
echo "<li>✅ <strong>Conta Destino:</strong> Sua Conta Poupança Itaú (obrigatório)</li>";
echo "<li>✅ <strong>Descrição:</strong> 'Transferência para poupança'</li>";
echo "</ul>";
echo "</div>";

echo "<h3>💸 Operações ESPECIAIS</h3>";

echo "<div class='exemplo operacao'>";
echo "<h4>Saque - R$ 200,00</h4>";
echo "<p><strong>Cenário:</strong> Você sacou dinheiro no caixa eletrônico</p>";
echo "<div class='conta'>🏦 Sua Conta Corrente Itaú <span class='seta-saida'>→ -R$ 200,00</span></div>";
echo "<div class='terceiro'>💰 Dinheiro em Espécie (Caixa eletrônico)</div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Conta Origem:</strong> Sua Conta Corrente Itaú (obrigatório)</li>";
echo "<li>❌ <strong>Conta Destino:</strong> Não seleciona (é dinheiro físico)</li>";
echo "</ul>";
echo "</div>";

echo "<div class='exemplo operacao'>";
echo "<h4>Tarifa Bancária - R$ 25,00</h4>";
echo "<p><strong>Cenário:</strong> O banco cobrou uma tarifa da sua conta</p>";
echo "<div class='conta'>🏦 Sua Conta Corrente Bradesco <span class='seta-saida'>→ -R$ 25,00</span></div>";
echo "<div class='terceiro'>🏦 Banco Bradesco (Tarifa de manutenção)</div>";
echo "<p><strong>No sistema:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>Conta Origem:</strong> Sua Conta Corrente Bradesco (obrigatório)</li>";
echo "<li>❌ <strong>Conta Destino:</strong> Não seleciona (é cobrança do banco)</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📋 Resumo da Lógica</h2>";

echo "<div class='exemplo'>";
echo "<h4>🎯 Regras Simples:</h4>";
echo "<ol>";
echo "<li><strong>Operações de SAÍDA:</strong> Sempre tem Conta Origem (sua conta), nunca tem Conta Destino (é terceiro)</li>";
echo "<li><strong>Operações de ENTRADA:</strong> Sempre tem Conta Destino (sua conta), nunca tem Conta Origem (é terceiro)</li>";
echo "<li><strong>Transferências INTERNAS:</strong> Tem ambas as contas (origem e destino são suas)</li>";
echo "<li><strong>Dados de terceiros:</strong> Vão nos campos específicos (PIX, TED, etc.)</li>";
echo "</ol>";
echo "</div>";

echo "<div class='exemplo'>";
echo "<h4>✅ Vantagens desta lógica:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Realista:</strong> Reflete como as operações funcionam na vida real</li>";
echo "<li>✅ <strong>Saldos corretos:</strong> Só suas contas afetam seus saldos</li>";
echo "<li>✅ <strong>Relatórios precisos:</strong> Entradas e saídas bem definidas</li>";
echo "<li>✅ <strong>Controle completo:</strong> Você sabe exatamente o que entra e sai</li>";
echo "<li>✅ <strong>Dados de terceiros:</strong> Ficam organizados nos campos específicos</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🚀 Como usar no sistema:</h2>";
echo "<ol>";
echo "<li><a href='transferencias.php?acao=nova' target='_blank'>Acesse Nova Transferência</a></li>";
echo "<li>Selecione o tipo de operação</li>";
echo "<li>Os campos aparecerão automaticamente conforme a lógica acima</li>";
echo "<li>Preencha apenas os campos que aparecem</li>";
echo "<li>Salve e veja os saldos atualizados corretamente</li>";
echo "</ol>";

echo "<p><strong>Agora o sistema funciona exatamente como na vida real! 🎯</strong></p>";
?>
