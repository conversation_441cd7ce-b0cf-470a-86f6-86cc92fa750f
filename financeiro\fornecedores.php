<?php
/**
 * ============================================================================
 * FORNECEDORES - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gestão completa de fornecedores
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'salvar':
                $dados = [
                    'nome' => $_POST['nome'],
                    'tipo_pessoa' => $_POST['tipo_pessoa'],
                    'cpf_cnpj' => $_POST['cpf_cnpj'],
                    'email' => $_POST['email'] ?? '',
                    'telefone' => $_POST['telefone'] ?? '',
                    'endereco' => $_POST['endereco'] ?? '',
                    'cidade' => $_POST['cidade'] ?? '',
                    'estado' => $_POST['estado'] ?? '',
                    'cep' => $_POST['cep'] ?? '',
                    'observacoes' => $_POST['observacoes'] ?? '',
                    'status' => $_POST['status'] ?? 'ativo'
                ];

                if ($id) {
                    // Atualizar
                    $sql = "UPDATE fornecedores SET nome = ?, tipo_pessoa = ?, cpf_cnpj = ?, email = ?, 
                           telefone = ?, endereco = ?, cidade = ?, estado = ?, cep = ?, 
                           observacoes = ?, status = ?, updated_at = NOW() WHERE id = ?";
                    $params = array_values($dados);
                    $params[] = $id;
                    $db->execute($sql, $params);
                    $mensagem = "Fornecedor atualizado com sucesso!";
                } else {
                    // Inserir
                    $sql = "INSERT INTO fornecedores (nome, tipo_pessoa, cpf_cnpj, email, telefone, 
                           endereco, cidade, estado, cep, observacoes, status, created_at) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $db->execute($sql, array_values($dados));
                    $mensagem = "Fornecedor cadastrado com sucesso!";
                }
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;

            case 'excluir':
                // Verificar se há contas vinculadas
                $contas_vinculadas = $db->fetchOne("SELECT COUNT(*) as total FROM contas_pagar WHERE fornecedor_id = ?", [$id]);
                if ($contas_vinculadas['total'] > 0) {
                    throw new Exception("Não é possível excluir este fornecedor pois há contas vinculadas a ele.");
                }
                
                $sql = "DELETE FROM fornecedores WHERE id = ?";
                $db->execute($sql, [$id]);
                $mensagem = "Fornecedor excluído com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "f.status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['tipo_pessoa'])) {
        $where_clauses[] = "f.tipo_pessoa = ?";
        $params[] = $_GET['tipo_pessoa'];
    }
    if (!empty($_GET['busca'])) {
        $where_clauses[] = "(f.nome LIKE ? OR f.cpf_cnpj LIKE ? OR f.email LIKE ?)";
        $busca = '%' . $_GET['busca'] . '%';
        $params = array_merge($params, [$busca, $busca, $busca]);
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    $sql = "SELECT f.*
            FROM fornecedores f
            $where_sql
            ORDER BY f.id ASC";
    
    $fornecedores = $db->fetchAll($sql, $params);
}

// Busca dados para edição
if (($acao === 'editar' || $acao === 'visualizar') && $id) {
    $fornecedor = $db->fetchOne("SELECT * FROM fornecedores WHERE id = ?", [$id]);
    if (!$fornecedor) {
        header('Location: fornecedores.php?erro=fornecedor_nao_encontrado');
        exit;
    }
    
    // Buscar histórico de contas
    if ($acao === 'visualizar') {
        $historico_contas = $db->fetchAll("
            SELECT cp.*, c.nome as categoria_nome 
            FROM contas_pagar cp 
            LEFT JOIN categorias_financeiras c ON cp.categoria_id = c.id 
            WHERE cp.fornecedor_id = ? 
            ORDER BY cp.data_vencimento DESC 
            LIMIT 10
        ", [$id]);
    }
}

$titulo_pagina = "Fornecedores";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-truck text-orange-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gestão de fornecedores e prestadores de serviços</p>
                    </div>
                    
                    <?php if ($acao === 'listar'): ?>
                        <a href="fornecedores.php?acao=novo" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Novo Fornecedor
                        </a>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'novo':
                    case 'editar':
                        include 'views/fornecedores/formulario.php';
                        break;
                    case 'visualizar':
                        include 'views/fornecedores/visualizar.php';
                        break;
                    default:
                        include 'views/fornecedores/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script src="../secretaria/js/formatacao.js"></script>
    <script>
        function confirmarExclusao(id, nome) {
            if (confirm(`Tem certeza que deseja excluir o fornecedor "${nome}"?\n\nEsta ação não pode ser desfeita.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `fornecedores.php?acao=excluir&id=${id}`;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function formatarCpfCnpj(input) {
            let valor = input.value.replace(/\D/g, '');
            
            if (valor.length <= 11) {
                // CPF: 000.000.000-00
                valor = valor.replace(/(\d{3})(\d)/, '$1.$2');
                valor = valor.replace(/(\d{3})(\d)/, '$1.$2');
                valor = valor.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            } else {
                // CNPJ: 00.000.000/0000-00
                valor = valor.replace(/^(\d{2})(\d)/, '$1.$2');
                valor = valor.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
                valor = valor.replace(/\.(\d{3})(\d)/, '.$1/$2');
                valor = valor.replace(/(\d{4})(\d)/, '$1-$2');
            }
            
            input.value = valor;
        }

        function alterarTipoPessoa() {
            const tipo = document.getElementById('tipo_pessoa').value;
            const label = document.querySelector('label[for="cpf_cnpj"]');
            const input = document.getElementById('cpf_cnpj');
            
            if (tipo === 'fisica') {
                label.textContent = 'CPF';
                input.placeholder = '000.000.000-00';
                input.maxLength = 14;
            } else {
                label.textContent = 'CNPJ';
                input.placeholder = '00.000.000/0000-00';
                input.maxLength = 18;
            }
        }
    </script>
</body>
</html>
