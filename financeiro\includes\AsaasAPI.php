<?php
/**
 * Classe para integração com a API do Asaas
 */

class AsaasAPI {
    private $apiKey;
    private $ambiente;
    private $baseUrl;
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
        $this->carregarConfiguracoes();
    }
    
    private function carregarConfiguracoes() {
        try {
            $config = $this->db->fetchOne("SELECT * FROM configuracoes_asaas WHERE ativo = 1 ORDER BY id DESC LIMIT 1");
            
            if ($config) {
                $this->apiKey = $config['api_key'];
                $this->ambiente = $config['ambiente'];
                $this->baseUrl = $this->ambiente === 'producao' 
                    ? 'https://www.asaas.com/api/v3' 
                    : 'https://sandbox.asaas.com/api/v3';
            } else {
                throw new Exception("Configurações do Asaas não encontradas");
            }
        } catch (Exception $e) {
            error_log("Erro ao carregar configurações Asaas: " . $e->getMessage());
            throw new Exception("Erro ao carregar configurações do Asaas");
        }
    }
    
    public function isConfigurado() {
        return !empty($this->apiKey);
    }
    
    public function criarCobranca($dados) {
        if (!$this->isConfigurado()) {
            throw new Exception("API do Asaas não configurada");
        }
        
        $endpoint = '/payments';
        $dadosCobranca = $this->prepararDadosCobranca($dados);
        
        $response = $this->fazerRequisicao('POST', $endpoint, $dadosCobranca);
        
        // Log da operação
        $this->logarOperacao('criar_cobranca', $response['id'] ?? null, $dados['cobranca_id_local'] ?? null, $dadosCobranca, $response);
        
        return $response;
    }
    
    public function consultarCobranca($asaasId) {
        if (!$this->isConfigurado()) {
            throw new Exception("API do Asaas não configurada");
        }
        
        $endpoint = "/payments/{$asaasId}";
        $response = $this->fazerRequisicao('GET', $endpoint);
        
        // Log da operação
        $this->logarOperacao('consultar_cobranca', $asaasId, null, null, $response);
        
        return $response;
    }
    
    public function cancelarCobranca($asaasId) {
        if (!$this->isConfigurado()) {
            throw new Exception("API do Asaas não configurada");
        }
        
        $endpoint = "/payments/{$asaasId}";
        $response = $this->fazerRequisicao('DELETE', $endpoint);
        
        // Log da operação
        $this->logarOperacao('cancelar_cobranca', $asaasId, null, null, $response);
        
        return $response;
    }
    
    public function estornarCobranca($asaasId) {
        if (!$this->isConfigurado()) {
            throw new Exception("API do Asaas não configurada");
        }
        
        $endpoint = "/payments/{$asaasId}/refund";
        $response = $this->fazerRequisicao('POST', $endpoint);
        
        // Log da operação
        $this->logarOperacao('estornar_cobranca', $asaasId, null, null, $response);
        
        return $response;
    }
    
    private function prepararDadosCobranca($dados) {
        $config = $this->db->fetchOne("SELECT * FROM configuracoes_asaas WHERE ativo = 1 ORDER BY id DESC LIMIT 1");
        
        $dadosCobranca = [
            'customer' => $dados['customer_id'] ?? $this->criarOuObterCliente($dados),
            'billingType' => 'BOLETO',
            'value' => $dados['valor'],
            'dueDate' => $dados['data_vencimento'],
            'description' => $dados['descricao'] ?? 'Cobrança gerada pelo sistema',
            'externalReference' => $dados['referencia_externa'] ?? null,
        ];
        
        // Configurações de multa e juros
        if ($config['multa_percentual'] > 0) {
            $dadosCobranca['fine'] = [
                'value' => $config['multa_percentual']
            ];
        }
        
        if ($config['juros_percentual'] > 0) {
            $dadosCobranca['interest'] = [
                'value' => $config['juros_percentual']
            ];
        }
        
        // Desconto por antecipação
        if ($config['desconto_antecipacao_dias'] > 0 && $config['desconto_antecipacao_percentual'] > 0) {
            $dadosCobranca['discount'] = [
                'value' => $config['desconto_antecipacao_percentual'],
                'dueDateLimitDays' => $config['desconto_antecipacao_dias']
            ];
        }
        
        return $dadosCobranca;
    }
    
    private function criarOuObterCliente($dados) {
        // Verificar se cliente já existe no Asaas
        $cpfCnpj = preg_replace('/[^0-9]/', '', $dados['cpf_cnpj']);
        
        $endpoint = "/customers?cpfCnpj={$cpfCnpj}";
        $clientes = $this->fazerRequisicao('GET', $endpoint);
        
        if (!empty($clientes['data'])) {
            return $clientes['data'][0]['id'];
        }
        
        // Criar novo cliente
        $dadosCliente = [
            'name' => $dados['nome'],
            'cpfCnpj' => $cpfCnpj,
            'email' => $dados['email'] ?? null,
            'phone' => $dados['telefone'] ?? null,
            'mobilePhone' => $dados['celular'] ?? null,
        ];
        
        if (!empty($dados['endereco'])) {
            $dadosCliente['address'] = $dados['endereco']['logradouro'] ?? null;
            $dadosCliente['addressNumber'] = $dados['endereco']['numero'] ?? null;
            $dadosCliente['complement'] = $dados['endereco']['complemento'] ?? null;
            $dadosCliente['province'] = $dados['endereco']['bairro'] ?? null;
            $dadosCliente['postalCode'] = preg_replace('/[^0-9]/', '', $dados['endereco']['cep'] ?? '');
        }
        
        $cliente = $this->fazerRequisicao('POST', '/customers', $dadosCliente);
        
        return $cliente['id'];
    }
    
    private function fazerRequisicao($method, $endpoint, $data = null) {
        $url = $this->baseUrl . $endpoint;
        
        $headers = [
            'Content-Type: application/json',
            'access_token: ' . $this->apiKey
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);
        
        switch ($method) {
            case 'POST':
                curl_setopt($ch, CURLOPT_POST, true);
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'PUT':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                if ($data) {
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                }
                break;
            case 'DELETE':
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                break;
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception("Erro na requisição cURL: " . $error);
        }
        
        $responseData = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = $responseData['errors'][0]['description'] ?? 'Erro desconhecido na API';
            throw new Exception("Erro na API Asaas: " . $errorMessage);
        }
        
        return $responseData;
    }
    
    private function logarOperacao($tipo, $asaasId, $localId, $request, $response) {
        try {
            $sucesso = !isset($response['errors']);
            $errorMessage = $sucesso ? null : json_encode($response['errors']);
            
            $this->db->query("
                INSERT INTO logs_asaas 
                (tipo_operacao, cobranca_id_asaas, cobranca_id_local, request_data, response_data, sucesso, erro_mensagem, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ", [
                $tipo,
                $asaasId,
                $localId,
                json_encode($request),
                json_encode($response),
                $sucesso ? 1 : 0,
                $errorMessage
            ]);
        } catch (Exception $e) {
            error_log("Erro ao logar operação Asaas: " . $e->getMessage());
        }
    }
    
    public function processarWebhook($dados) {
        try {
            $this->logarOperacao('webhook', $dados['payment']['id'] ?? null, null, $dados, null);
            
            // Atualizar status do boleto local
            if (isset($dados['payment']['id'])) {
                $this->atualizarStatusBoleto($dados['payment']['id'], $dados['event'], $dados['payment']);
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erro ao processar webhook Asaas: " . $e->getMessage());
            return false;
        }
    }
    
    private function atualizarStatusBoleto($asaasId, $evento, $dadosPagamento) {
        $novoStatus = $this->mapearStatusAsaas($dadosPagamento['status']);
        
        $this->db->query("
            UPDATE boletos 
            SET asaas_status = ?, webhook_recebido = 1, updated_at = NOW() 
            WHERE asaas_id = ?
        ", [$novoStatus, $asaasId]);
        
        // Se foi pago, atualizar também a conta a receber
        if ($evento === 'PAYMENT_RECEIVED') {
            $boleto = $this->db->fetchOne("SELECT conta_receber_id FROM boletos WHERE asaas_id = ?", [$asaasId]);
            if ($boleto && $boleto['conta_receber_id']) {
                $this->db->query("
                    UPDATE contas_receber 
                    SET status = 'recebido', data_recebimento = NOW() 
                    WHERE id = ?
                ", [$boleto['conta_receber_id']]);
            }
        }
    }
    
    private function mapearStatusAsaas($statusAsaas) {
        $mapeamento = [
            'PENDING' => 'pendente',
            'RECEIVED' => 'pago',
            'CONFIRMED' => 'pago',
            'OVERDUE' => 'vencido',
            'REFUNDED' => 'estornado',
            'RECEIVED_IN_CASH' => 'pago',
            'REFUND_REQUESTED' => 'estorno_solicitado',
            'CHARGEBACK_REQUESTED' => 'contestado',
            'CHARGEBACK_DISPUTE' => 'contestado',
            'AWAITING_CHARGEBACK_REVERSAL' => 'contestado',
            'DUNNING_REQUESTED' => 'cobranca_solicitada',
            'DUNNING_RECEIVED' => 'cobranca_recebida',
            'AWAITING_RISK_ANALYSIS' => 'analise_risco'
        ];
        
        return $mapeamento[$statusAsaas] ?? 'desconhecido';
    }
}
?>
