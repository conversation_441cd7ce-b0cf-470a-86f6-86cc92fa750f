<!-- Sidebar Moderno - <PERSON><PERSON><PERSON><PERSON> -->
<div id="sidebar" class="sidebar sidebar-expanded bg-green-600 text-white flex flex-col w-64 min-h-screen fixed left-0 top-0 transition-all duration-300 z-10">
    <!-- Logo -->
    <div class="p-4 flex items-center justify-between bg-green-700 border-b border-green-500">
        <div class="flex items-center sidebar-logo-full">
            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center shadow-lg">
                <i class="fas fa-chart-line text-white text-xl"></i>
            </div>
            <div class="ml-3">
                <h1 class="text-white font-bold text-lg">Financeiro</h1>
                <p class="text-green-100 text-xs">Faciência ERP</p>
            </div>
        </div>
        <!-- Botão de toggle para mobile -->
        <button id="sidebar-toggle" class="lg:hidden text-white hover:bg-green-600 p-2 rounded">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- <PERSON><PERSON> de Na<PERSON>gação -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        <!-- Dashboard -->
        <a href="index.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group">
            <i class="fas fa-tachometer-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
            <span class="sidebar-text">Dashboard</span>
        </a>

        <!-- Tesouraria -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-university mr-2"></i>
                <span class="sidebar-text">Tesouraria</span>
            </div>
            <a href="tesouraria.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-coins text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Caixa e Bancos</span>
            </a>
            <a href="tesouraria.php?acao=extrato" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-list-alt text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Extrato</span>
            </a>
        </div>

        <!-- Contas a Pagar -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-file-invoice mr-2"></i>
                <span class="sidebar-text">Contas a Pagar</span>
            </div>
            <a href="contas_pagar.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-list text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Listar Contas</span>
            </a>
            <a href="contas_pagar.php?acao=nova" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-plus text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Nova Conta</span>
            </a>

        </div>

        <!-- Contas a Receber -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-hand-holding-usd mr-2"></i>
                <span class="sidebar-text">Contas a Receber</span>
            </div>
            <a href="contas_receber.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-list text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Listar Contas</span>
            </a>
            <a href="contas_receber.php?acao=nova" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-plus text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Nova Conta</span>
            </a>
            <a href="mensalidades.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-graduation-cap text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Mensalidades</span>
            </a>
        </div>

        <!-- Relatórios -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-chart-bar mr-2"></i>
                <span class="sidebar-text">Relatórios</span>
            </div>
            <a href="relatorios.php?tipo=dre" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-chart-pie text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">DRE</span>
            </a>
            <a href="relatorios.php?tipo=balanco" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-balance-scale-right text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Balanço</span>
            </a>
            <a href="relatorios.php?tipo=fluxo_caixa" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-chart-line text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Fluxo de Caixa</span>
            </a>
            <a href="relatorios.php?tipo=customizado" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-filter text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Customizados</span>
            </a>
        </div>

        <!-- Ferramentas -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-tools mr-2"></i>
                <span class="sidebar-text">Ferramentas</span>
            </div>
            <a href="boletos.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-barcode text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Boletos</span>
            </a>
            <a href="contas_bancarias.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-university text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Contas Bancárias</span>
            </a>
            <a href="fornecedores.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-truck text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Fornecedores</span>
            </a>
            <a href="comprovantes.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-file-upload text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Comprovantes</span>
            </a>
        </div>

        <!-- Configurações -->
        <div class="space-y-1">
            <div class="flex items-center px-4 py-2 text-green-200 text-sm font-semibold uppercase tracking-wider">
                <i class="fas fa-cog mr-2"></i>
                <span class="sidebar-text">Configurações</span>
            </div>
            <a href="configuracoes.php?acao=categorias" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-tags text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Categorias</span>
            </a>
            <a href="configuracoes.php?acao=formas_pagamento" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-credit-card text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Formas de Pagamento</span>
            </a>
            <a href="configuracoes.php" class="flex items-center px-4 py-3 text-green-100 hover:bg-green-700 hover:text-white rounded-lg transition-all duration-200 group ml-4">
                <i class="fas fa-sliders-h text-lg mr-3 group-hover:scale-110 transition-transform"></i>
                <span class="sidebar-text">Gerais</span>
            </a>
        </div>
    </nav>

    <!-- Rodapé do Sidebar -->
    <div class="p-4 border-t border-green-500">
        <!-- Informações do Usuário -->
        <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-user text-white"></i>
            </div>
            <div class="ml-3 sidebar-text">
                <p class="text-sm font-medium text-white"><?php echo htmlspecialchars($_SESSION['usuario_nome'] ?? 'Usuário'); ?></p>
                <p class="text-xs text-green-200">Módulo Financeiro</p>
            </div>
        </div>

        <!-- Botões de Ação -->
        <div class="space-y-2">
            <a href="../secretaria/index.php" class="flex items-center px-3 py-2 text-green-100 hover:bg-green-700 rounded-lg transition-colors text-sm">
                <i class="fas fa-home mr-2"></i>
                <span class="sidebar-text">Voltar ao Sistema</span>
            </a>
            <a href="../secretaria/logout.php" class="flex items-center px-3 py-2 text-green-100 hover:bg-red-600 rounded-lg transition-colors text-sm">
                <i class="fas fa-sign-out-alt mr-2"></i>
                <span class="sidebar-text">Sair</span>
            </a>
        </div>
    </div>
</div>

<!-- Overlay para mobile -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    // Toggle sidebar no mobile
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('sidebar-expanded');
            sidebarOverlay.classList.toggle('hidden');
        });
    }

    // Fechar sidebar ao clicar no overlay
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            sidebar.classList.remove('sidebar-expanded');
            sidebarOverlay.classList.add('hidden');
        });
    }

    // Marcar item ativo baseado na URL atual
    const currentPath = window.location.pathname;
    const menuItems = document.querySelectorAll('#sidebar nav a');
    
    menuItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href.split('?')[0])) {
            item.classList.add('bg-green-700', 'text-white');
            item.classList.remove('text-green-100');
        }
    });
});
</script>
