<?php
/**
 * ============================================================================
 * DASHBOARD DO MÓDULO FINANCEIRO - SISTEMA FACIÊNCIA ERP
 * ============================================================================
 *
 * Este arquivo é responsável por exibir o dashboard principal do módulo
 * financeiro, contendo indicadores, fluxo de caixa e ações rápidas.
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 *
 * Funcionalidades Principais:
 * - Dashboard com indicadores financeiros
 * - Fluxo de caixa em tempo real
 * - Contas a pagar e receber
 * - Relatórios gerenciais
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões do módulo financeiro
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Busca dados para o dashboard
try {
    // Indicadores principais
    $sql_indicadores = "
        SELECT 
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente') as contas_pagar_pendentes,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente') as contas_receber_pendentes,
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_pagar_vencidas,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_receber_vencidas,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar WHERE status = 'pendente') as total_pagar,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_receber WHERE status = 'pendente') as total_receber,
            (SELECT COALESCE(SUM(saldo_inicial), 0) FROM contas_bancarias WHERE status = 'ativo') as saldo_total_bancos
    ";
    $indicadores = $db->fetchOne($sql_indicadores);

    // Fluxo de caixa dos últimos 30 dias
    $sql_fluxo = "
        SELECT 
            DATE(data_transacao) as data,
            SUM(CASE WHEN tipo = 'receita' THEN valor ELSE 0 END) as receitas,
            SUM(CASE WHEN tipo = 'despesa' THEN valor ELSE 0 END) as despesas
        FROM transacoes_financeiras 
        WHERE data_transacao >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(data_transacao)
        ORDER BY data_transacao DESC
        LIMIT 30
    ";
    $fluxo_caixa = $db->fetchAll($sql_fluxo);

    // Contas a vencer nos próximos 7 dias
    $sql_vencimentos = "
        (SELECT 'pagar' as tipo, id, descricao, valor, data_vencimento, fornecedor_nome as terceiro
         FROM contas_pagar 
         WHERE status = 'pendente' AND data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         ORDER BY data_vencimento ASC
         LIMIT 10)
        UNION ALL
        (SELECT 'receber' as tipo, id, descricao, valor, data_vencimento, cliente_nome as terceiro
         FROM contas_receber 
         WHERE status = 'pendente' AND data_vencimento BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
         ORDER BY data_vencimento ASC
         LIMIT 10)
        ORDER BY data_vencimento ASC
    ";
    $vencimentos = $db->fetchAll($sql_vencimentos);

    // Últimas transações
    $sql_transacoes = "
        SELECT tipo, descricao, valor, data_transacao, forma_pagamento
        FROM transacoes_financeiras 
        ORDER BY data_transacao DESC, id DESC
        LIMIT 10
    ";
    $ultimas_transacoes = $db->fetchAll($sql_transacoes);

} catch (Exception $e) {
    error_log("Erro no dashboard financeiro: " . $e->getMessage());
    $indicadores = [];
    $fluxo_caixa = [];
    $vencimentos = [];
    $ultimas_transacoes = [];
}

$titulo_pagina = "Dashboard Financeiro";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-chart-line text-green-600 mr-3"></i>
                            Dashboard Financeiro
                        </h1>
                        <p class="text-gray-600 mt-1">Visão geral da situação financeira da instituição</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">
                            Última atualização: <?php echo date('d/m/Y H:i'); ?>
                        </span>
                        <button onclick="location.reload()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Atualizar
                        </button>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Indicadores Principais -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Contas a Pagar -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Contas a Pagar</p>
                                <p class="text-2xl font-bold text-red-600">
                                    <?php echo number_format($indicadores['contas_pagar_pendentes'] ?? 0); ?>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <?php echo number_format($indicadores['contas_pagar_vencidas'] ?? 0); ?> vencidas
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-file-invoice text-red-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                Total: <span class="font-semibold text-red-600">
                                    R$ <?php echo number_format($indicadores['total_pagar'] ?? 0, 2, ',', '.'); ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Contas a Receber -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Contas a Receber</p>
                                <p class="text-2xl font-bold text-green-600">
                                    <?php echo number_format($indicadores['contas_receber_pendentes'] ?? 0); ?>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">
                                    <?php echo number_format($indicadores['contas_receber_vencidas'] ?? 0); ?> vencidas
                                </p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-hand-holding-usd text-green-600 text-xl"></i>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">
                                Total: <span class="font-semibold text-green-600">
                                    R$ <?php echo number_format($indicadores['total_receber'] ?? 0, 2, ',', '.'); ?>
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Saldo em Bancos -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Saldo em Bancos</p>
                                <p class="text-2xl font-bold text-blue-600">
                                    R$ <?php echo number_format($indicadores['saldo_total_bancos'] ?? 0, 2, ',', '.'); ?>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">Todas as contas</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-university text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Fluxo do Dia -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Fluxo Hoje</p>
                                <?php 
                                $fluxo_hoje = 0;
                                foreach ($fluxo_caixa as $fluxo) {
                                    if ($fluxo['data'] == date('Y-m-d')) {
                                        $fluxo_hoje = ($fluxo['receitas'] ?? 0) - ($fluxo['despesas'] ?? 0);
                                        break;
                                    }
                                }
                                $cor_fluxo = $fluxo_hoje >= 0 ? 'text-green-600' : 'text-red-600';
                                ?>
                                <p class="text-2xl font-bold <?php echo $cor_fluxo; ?>">
                                    R$ <?php echo number_format($fluxo_hoje, 2, ',', '.'); ?>
                                </p>
                                <p class="text-xs text-gray-500 mt-1">Receitas - Despesas</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-exchange-alt text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ações Rápidas -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-bolt text-yellow-500 mr-2"></i>
                        Ações Rápidas
                    </h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <a href="contas_pagar.php" class="flex flex-col items-center p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors">
                            <i class="fas fa-file-invoice text-red-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-red-700">Contas a Pagar</span>
                        </a>
                        <a href="contas_receber.php" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                            <i class="fas fa-hand-holding-usd text-green-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-green-700">Contas a Receber</span>
                        </a>
                        <a href="tesouraria.php" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                            <i class="fas fa-university text-blue-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-blue-700">Tesouraria</span>
                        </a>
                        <a href="mensalidades.php" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                            <i class="fas fa-graduation-cap text-purple-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-purple-700">Mensalidades</span>
                        </a>
                        <a href="relatorios.php" class="flex flex-col items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors">
                            <i class="fas fa-chart-bar text-indigo-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-indigo-700">Relatórios</span>
                        </a>
                        <a href="configuracoes.php" class="flex flex-col items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                            <i class="fas fa-cog text-gray-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-gray-700">Configurações</span>
                        </a>
                    </div>
                </div>

                <!-- Conteúdo Principal em Duas Colunas -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Vencimentos Próximos -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">
                                <i class="fas fa-calendar-alt text-orange-500 mr-2"></i>
                                Vencimentos nos Próximos 7 Dias
                            </h3>
                        </div>
                        <div class="p-6">
                            <?php if (empty($vencimentos)): ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-calendar-check text-gray-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Nenhum vencimento nos próximos 7 dias</p>
                                </div>
                            <?php else: ?>
                                <div class="space-y-4">
                                    <?php foreach ($vencimentos as $vencimento): ?>
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-lg flex items-center justify-center <?php echo $vencimento['tipo'] == 'pagar' ? 'bg-red-100' : 'bg-green-100'; ?>">
                                                    <i class="fas <?php echo $vencimento['tipo'] == 'pagar' ? 'fa-arrow-up text-red-600' : 'fa-arrow-down text-green-600'; ?>"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <p class="font-medium text-gray-800"><?php echo htmlspecialchars($vencimento['descricao']); ?></p>
                                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($vencimento['terceiro']); ?></p>
                                                    <p class="text-xs text-gray-500">
                                                        Vence em: <?php echo date('d/m/Y', strtotime($vencimento['data_vencimento'])); ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-semibold <?php echo $vencimento['tipo'] == 'pagar' ? 'text-red-600' : 'text-green-600'; ?>">
                                                    R$ <?php echo number_format($vencimento['valor'], 2, ',', '.'); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Últimas Transações -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">
                                <i class="fas fa-history text-blue-500 mr-2"></i>
                                Últimas Transações
                            </h3>
                        </div>
                        <div class="p-6">
                            <?php if (empty($ultimas_transacoes)): ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-receipt text-gray-300 text-4xl mb-4"></i>
                                    <p class="text-gray-500">Nenhuma transação recente</p>
                                </div>
                            <?php else: ?>
                                <div class="space-y-4">
                                    <?php foreach ($ultimas_transacoes as $transacao): ?>
                                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                            <div class="flex items-center">
                                                <div class="w-10 h-10 rounded-lg flex items-center justify-center <?php echo $transacao['tipo'] == 'receita' ? 'bg-green-100' : 'bg-red-100'; ?>">
                                                    <i class="fas <?php echo $transacao['tipo'] == 'receita' ? 'fa-plus text-green-600' : 'fa-minus text-red-600'; ?>"></i>
                                                </div>
                                                <div class="ml-4">
                                                    <p class="font-medium text-gray-800"><?php echo htmlspecialchars($transacao['descricao']); ?></p>
                                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($transacao['forma_pagamento'] ?? 'N/A'); ?></p>
                                                    <p class="text-xs text-gray-500">
                                                        <?php echo date('d/m/Y H:i', strtotime($transacao['data_transacao'])); ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-semibold <?php echo $transacao['tipo'] == 'receita' ? 'text-green-600' : 'text-red-600'; ?>">
                                                    <?php echo $transacao['tipo'] == 'receita' ? '+' : '-'; ?>
                                                    R$ <?php echo number_format($transacao['valor'], 2, ',', '.'); ?>
                                                </p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Gráfico de Fluxo de Caixa -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-chart-area text-purple-500 mr-2"></i>
                            Fluxo de Caixa - Últimos 30 Dias
                        </h3>
                    </div>
                    <div class="p-6">
                        <canvas id="fluxoCaixaChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        // Gráfico de Fluxo de Caixa
        const ctx = document.getElementById('fluxoCaixaChart').getContext('2d');
        const fluxoData = <?php echo json_encode(array_reverse($fluxo_caixa)); ?>;

        const labels = fluxoData.map(item => {
            const date = new Date(item.data);
            return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
        });

        const receitas = fluxoData.map(item => parseFloat(item.receitas) || 0);
        const despesas = fluxoData.map(item => parseFloat(item.despesas) || 0);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Receitas',
                    data: receitas,
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'Despesas',
                    data: despesas,
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'R$ ' + value.toLocaleString('pt-BR');
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': R$ ' + context.parsed.y.toLocaleString('pt-BR');
                            }
                        }
                    }
                }
            }
        });

        // Auto-refresh a cada 5 minutos
        setTimeout(function() {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
