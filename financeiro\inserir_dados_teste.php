<?php
/**
 * Script para inserir dados de teste no módulo financeiro
 * ATENÇÃO: Execute apenas uma vez para não duplicar dados
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

if (!usuarioTemPermissao('financeiro', 'administrar')) {
    die('Sem permissão para executar este script');
}

$db = Database::getInstance();

echo "<h1>🧪 Inserindo Dados de Teste - Módulo Financeiro</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .info { color: blue; }
</style>";

try {
    // 1. Inserir categorias financeiras se não existirem
    echo "<h2>📂 Inserindo Categorias Financeiras</h2>";
    
    $categorias = [
        ['nome' => 'Mensalidades', 'tipo' => 'receita', 'descricao' => 'Receitas de mensalidades dos alunos'],
        ['nome' => 'Matrículas', 'tipo' => 'receita', 'descricao' => 'Receitas de matrículas'],
        ['nome' => 'Cursos Extras', 'tipo' => 'receita', 'descricao' => 'Receitas de cursos extras'],
        ['nome' => 'Salários', 'tipo' => 'despesa', 'descricao' => 'Pagamento de salários'],
        ['nome' => 'Aluguel', 'tipo' => 'despesa', 'descricao' => 'Pagamento de aluguel'],
        ['nome' => 'Material Escolar', 'tipo' => 'despesa', 'descricao' => 'Compra de material escolar'],
        ['nome' => 'Energia Elétrica', 'tipo' => 'despesa', 'descricao' => 'Conta de energia elétrica'],
        ['nome' => 'Internet', 'tipo' => 'despesa', 'descricao' => 'Conta de internet']
    ];
    
    foreach ($categorias as $categoria) {
        $existe = $db->fetchOne("SELECT id FROM categorias_financeiras WHERE nome = ?", [$categoria['nome']]);
        if (!$existe) {
            $db->execute("INSERT INTO categorias_financeiras (nome, tipo, descricao, status, created_at) VALUES (?, ?, ?, 'ativo', NOW())", 
                        [$categoria['nome'], $categoria['tipo'], $categoria['descricao']]);
            echo "<p class='ok'>✅ Categoria '{$categoria['nome']}' inserida</p>";
        } else {
            echo "<p class='info'>ℹ️ Categoria '{$categoria['nome']}' já existe</p>";
        }
    }

    // 2. Inserir contas bancárias se não existirem
    echo "<h2>🏦 Inserindo Contas Bancárias</h2>";
    
    $contas_bancarias = [
        ['nome' => 'Conta Corrente Principal', 'banco' => 'Banco do Brasil', 'agencia' => '1234-5', 'conta' => '12345-6', 'tipo' => 'corrente', 'saldo_inicial' => 50000.00],
        ['nome' => 'Conta Poupança', 'banco' => 'Caixa Econômica', 'agencia' => '5678-9', 'conta' => '98765-4', 'tipo' => 'poupanca', 'saldo_inicial' => 25000.00],
        ['nome' => 'Conta Digital', 'banco' => 'Nubank', 'agencia' => '0001', 'conta' => '11111-1', 'tipo' => 'corrente', 'saldo_inicial' => 15000.00]
    ];
    
    foreach ($contas_bancarias as $conta) {
        $existe = $db->fetchOne("SELECT id FROM contas_bancarias WHERE nome = ?", [$conta['nome']]);
        if (!$existe) {
            $db->execute("INSERT INTO contas_bancarias (nome, banco, agencia, conta, tipo, saldo_inicial, status, created_at) VALUES (?, ?, ?, ?, ?, ?, 'ativo', NOW())", 
                        [$conta['nome'], $conta['banco'], $conta['agencia'], $conta['conta'], $conta['tipo'], $conta['saldo_inicial']]);
            echo "<p class='ok'>✅ Conta '{$conta['nome']}' inserida</p>";
        } else {
            echo "<p class='info'>ℹ️ Conta '{$conta['nome']}' já existe</p>";
        }
    }

    // 3. Inserir contas a pagar
    echo "<h2>💸 Inserindo Contas a Pagar</h2>";
    
    $categoria_despesa = $db->fetchOne("SELECT id FROM categorias_financeiras WHERE tipo = 'despesa' LIMIT 1");
    $categoria_id = $categoria_despesa['id'] ?? 1;
    
    $contas_pagar = [
        ['descricao' => 'Aluguel Janeiro 2024', 'valor' => 3500.00, 'data_vencimento' => '2024-01-10', 'fornecedor_nome' => 'Imobiliária ABC', 'status' => 'pendente'],
        ['descricao' => 'Energia Elétrica Dezembro', 'valor' => 850.00, 'data_vencimento' => '2024-01-15', 'fornecedor_nome' => 'CEMIG', 'status' => 'pendente'],
        ['descricao' => 'Internet Janeiro', 'valor' => 299.90, 'data_vencimento' => '2024-01-20', 'fornecedor_nome' => 'Vivo Fibra', 'status' => 'pago'],
        ['descricao' => 'Material de Limpeza', 'valor' => 450.00, 'data_vencimento' => '2024-01-05', 'fornecedor_nome' => 'Distribuidora XYZ', 'status' => 'pago']
    ];
    
    foreach ($contas_pagar as $conta) {
        $existe = $db->fetchOne("SELECT id FROM contas_pagar WHERE descricao = ?", [$conta['descricao']]);
        if (!$existe) {
            $db->execute("INSERT INTO contas_pagar (descricao, valor, data_vencimento, categoria_id, fornecedor_nome, status, created_at, usuario_id) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)", 
                        [$conta['descricao'], $conta['valor'], $conta['data_vencimento'], $categoria_id, $conta['fornecedor_nome'], $conta['status'], $_SESSION['user_id']]);
            echo "<p class='ok'>✅ Conta a pagar '{$conta['descricao']}' inserida</p>";
        } else {
            echo "<p class='info'>ℹ️ Conta a pagar '{$conta['descricao']}' já existe</p>";
        }
    }

    // 4. Inserir contas a receber
    echo "<h2>💰 Inserindo Contas a Receber</h2>";
    
    $categoria_receita = $db->fetchOne("SELECT id FROM categorias_financeiras WHERE tipo = 'receita' LIMIT 1");
    $categoria_receita_id = $categoria_receita['id'] ?? 1;
    
    $contas_receber = [
        ['descricao' => 'Mensalidade João Silva - Janeiro', 'valor' => 850.00, 'data_vencimento' => '2024-01-10', 'cliente_nome' => 'João Silva', 'status' => 'recebido'],
        ['descricao' => 'Mensalidade Maria Santos - Janeiro', 'valor' => 850.00, 'data_vencimento' => '2024-01-10', 'cliente_nome' => 'Maria Santos', 'status' => 'pendente'],
        ['descricao' => 'Curso Extra - Informática', 'valor' => 300.00, 'data_vencimento' => '2024-01-15', 'cliente_nome' => 'Pedro Costa', 'status' => 'recebido'],
        ['descricao' => 'Matrícula Ana Oliveira', 'valor' => 200.00, 'data_vencimento' => '2024-01-05', 'cliente_nome' => 'Ana Oliveira', 'status' => 'recebido']
    ];
    
    foreach ($contas_receber as $conta) {
        $existe = $db->fetchOne("SELECT id FROM contas_receber WHERE descricao = ?", [$conta['descricao']]);
        if (!$existe) {
            $db->execute("INSERT INTO contas_receber (descricao, valor, data_vencimento, categoria_id, cliente_nome, status, created_at, usuario_id) VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)", 
                        [$conta['descricao'], $conta['valor'], $conta['data_vencimento'], $categoria_receita_id, $conta['cliente_nome'], $conta['status'], $_SESSION['user_id']]);
            echo "<p class='ok'>✅ Conta a receber '{$conta['descricao']}' inserida</p>";
        } else {
            echo "<p class='info'>ℹ️ Conta a receber '{$conta['descricao']}' já existe</p>";
        }
    }

    // 5. Inserir algumas transações financeiras
    echo "<h2>📊 Inserindo Transações Financeiras</h2>";
    
    $conta_bancaria = $db->fetchOne("SELECT id FROM contas_bancarias LIMIT 1");
    $conta_bancaria_id = $conta_bancaria['id'] ?? 1;
    
    $transacoes = [
        ['tipo' => 'receita', 'descricao' => 'Recebimento Mensalidade João', 'valor' => 850.00, 'data_transacao' => '2024-01-08', 'forma_pagamento' => 'PIX'],
        ['tipo' => 'receita', 'descricao' => 'Recebimento Curso Extra', 'valor' => 300.00, 'data_transacao' => '2024-01-12', 'forma_pagamento' => 'Cartão'],
        ['tipo' => 'despesa', 'descricao' => 'Pagamento Internet', 'valor' => 299.90, 'data_transacao' => '2024-01-18', 'forma_pagamento' => 'Débito Automático'],
        ['tipo' => 'despesa', 'descricao' => 'Pagamento Material Limpeza', 'valor' => 450.00, 'data_transacao' => '2024-01-03', 'forma_pagamento' => 'Transferência']
    ];
    
    foreach ($transacoes as $transacao) {
        $existe = $db->fetchOne("SELECT id FROM transacoes_financeiras WHERE descricao = ?", [$transacao['descricao']]);
        if (!$existe) {
            $db->execute("INSERT INTO transacoes_financeiras (tipo, descricao, valor, data_transacao, conta_bancaria_id, forma_pagamento, status, usuario_id, created_at) VALUES (?, ?, ?, ?, ?, ?, 'efetivada', ?, NOW())", 
                        [$transacao['tipo'], $transacao['descricao'], $transacao['valor'], $transacao['data_transacao'], $conta_bancaria_id, $transacao['forma_pagamento'], $_SESSION['user_id']]);
            echo "<p class='ok'>✅ Transação '{$transacao['descricao']}' inserida</p>";
        } else {
            echo "<p class='info'>ℹ️ Transação '{$transacao['descricao']}' já existe</p>";
        }
    }

    echo "<h2>🎉 Dados de Teste Inseridos com Sucesso!</h2>";
    echo "<p class='ok'>✅ Agora você pode testar o módulo financeiro com dados reais.</p>";
    echo "<p><a href='index.php'>🏠 Ir para o Dashboard Financeiro</a></p>";
    echo "<p><a href='diagnostico.php'>🔍 Executar Diagnóstico</a></p>";

} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro ao inserir dados: " . $e->getMessage() . "</p>";
}
?>
