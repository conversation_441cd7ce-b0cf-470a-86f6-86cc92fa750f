<?php
/**
 * <PERSON>ript para instalar a integração com Asaas
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

if (!usuarioTemPermissao('financeiro', 'administrar')) {
    die('Sem permissão para executar este script');
}

$db = Database::getInstance();

echo "<h1>🏦 Instalação da Integração Asaas</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .info { color: blue; }
    .aviso { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>";

try {
    echo "<h2>1. C<PERSON>do tabelas para integração Asaas</h2>";
    
    // Criar tabela de configurações
    $sql_config = "
    CREATE TABLE IF NOT EXISTS `configuracoes_asaas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `api_key` varchar(255) DEFAULT NULL,
      `ambiente` enum('sandbox','producao') NOT NULL DEFAULT 'sandbox',
      `webhook_url` varchar(255) DEFAULT NULL,
      `webhook_token` varchar(255) DEFAULT NULL,
      `multa_percentual` decimal(5,2) DEFAULT 2.00,
      `juros_percentual` decimal(5,2) DEFAULT 1.00,
      `desconto_antecipacao_dias` int(11) DEFAULT 0,
      `desconto_antecipacao_percentual` decimal(5,2) DEFAULT 0.00,
      `dias_vencimento_padrao` int(11) DEFAULT 7,
      `instrucoes_boleto` text DEFAULT NULL,
      `observacoes_boleto` text DEFAULT NULL,
      `ativo` tinyint(1) NOT NULL DEFAULT 1,
      `created_at` datetime NOT NULL DEFAULT current_timestamp(),
      `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    $db->query($sql_config);
    echo "<p class='ok'>✅ Tabela 'configuracoes_asaas' criada</p>";
    
    // Criar tabela de logs
    $sql_logs = "
    CREATE TABLE IF NOT EXISTS `logs_asaas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tipo_operacao` enum('criar_cobranca','consultar_cobranca','webhook','cancelar_cobranca','estornar_cobranca') NOT NULL,
      `cobranca_id_asaas` varchar(100) DEFAULT NULL,
      `cobranca_id_local` int(11) DEFAULT NULL,
      `request_data` text DEFAULT NULL,
      `response_data` text DEFAULT NULL,
      `status_code` int(11) DEFAULT NULL,
      `sucesso` tinyint(1) NOT NULL DEFAULT 0,
      `erro_mensagem` text DEFAULT NULL,
      `created_at` datetime NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_cobranca_asaas` (`cobranca_id_asaas`),
      KEY `idx_cobranca_local` (`cobranca_id_local`),
      KEY `idx_tipo_operacao` (`tipo_operacao`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ";
    
    $db->query($sql_logs);
    echo "<p class='ok'>✅ Tabela 'logs_asaas' criada</p>";

    echo "<h2>2. Atualizando tabela de boletos</h2>";
    
    // Adicionar campos do Asaas na tabela boletos
    $campos_asaas = [
        'asaas_id' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_id` varchar(100) DEFAULT NULL AFTER `id`",
        'asaas_url' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_url` varchar(500) DEFAULT NULL AFTER `asaas_id`",
        'asaas_linha_digitavel' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_linha_digitavel` varchar(100) DEFAULT NULL AFTER `asaas_url`",
        'asaas_codigo_barras' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_codigo_barras` varchar(100) DEFAULT NULL AFTER `asaas_linha_digitavel`",
        'asaas_status' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_status` varchar(50) DEFAULT NULL AFTER `asaas_codigo_barras`",
        'asaas_data_criacao' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_data_criacao` datetime DEFAULT NULL AFTER `asaas_status`",
        'asaas_data_vencimento' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `asaas_data_vencimento` date DEFAULT NULL AFTER `asaas_data_criacao`",
        'webhook_recebido' => "ALTER TABLE `boletos` ADD COLUMN IF NOT EXISTS `webhook_recebido` tinyint(1) DEFAULT 0 AFTER `asaas_data_vencimento`"
    ];
    
    foreach ($campos_asaas as $campo => $sql) {
        try {
            $db->query($sql);
            echo "<p class='ok'>✅ Campo '$campo' adicionado à tabela boletos</p>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "<p class='info'>ℹ️ Campo '$campo' já existe na tabela boletos</p>";
            } else {
                echo "<p class='erro'>❌ Erro ao adicionar campo '$campo': " . $e->getMessage() . "</p>";
            }
        }
    }

    echo "<h2>3. Criando índices para performance</h2>";
    
    $indices = [
        "CREATE INDEX IF NOT EXISTS idx_boletos_asaas_id ON boletos(asaas_id)",
        "CREATE INDEX IF NOT EXISTS idx_boletos_asaas_status ON boletos(asaas_status)"
    ];
    
    foreach ($indices as $indice) {
        try {
            $db->query($indice);
            echo "<p class='ok'>✅ Índice criado</p>";
        } catch (Exception $e) {
            echo "<p class='info'>ℹ️ Índice já existe ou erro: " . $e->getMessage() . "</p>";
        }
    }

    echo "<h2>4. Inserindo configuração padrão</h2>";
    
    // Verificar se já existe configuração
    $config_existe = $db->fetchOne("SELECT id FROM configuracoes_asaas WHERE id = 1");
    
    if (!$config_existe) {
        $sql_config_padrao = "
        INSERT INTO `configuracoes_asaas` 
        (`id`, `ambiente`, `multa_percentual`, `juros_percentual`, `dias_vencimento_padrao`, `instrucoes_boleto`, `observacoes_boleto`, `ativo`) 
        VALUES 
        (1, 'sandbox', 2.00, 1.00, 7, 
        'Após o vencimento, multa de 2% e juros de 1% ao mês.', 
        'Boleto gerado automaticamente pelo sistema Faciência ERP.',
        0)
        ";
        
        $db->query($sql_config_padrao);
        echo "<p class='ok'>✅ Configuração padrão inserida</p>";
    } else {
        echo "<p class='info'>ℹ️ Configuração padrão já existe</p>";
    }

    echo "<h2>5. Verificando instalação</h2>";
    
    // Verificar tabelas
    $tabelas = ['configuracoes_asaas', 'logs_asaas'];
    foreach ($tabelas as $tabela) {
        try {
            $resultado = $db->fetchOne("SELECT COUNT(*) as total FROM $tabela");
            echo "<p class='ok'>✅ Tabela '$tabela': {$resultado['total']} registros</p>";
        } catch (Exception $e) {
            echo "<p class='erro'>❌ Erro ao verificar tabela '$tabela': " . $e->getMessage() . "</p>";
        }
    }
    
    // Verificar campos na tabela boletos
    try {
        $colunas = $db->fetchAll("SHOW COLUMNS FROM boletos LIKE 'asaas_%'");
        echo "<p class='ok'>✅ Campos Asaas na tabela boletos: " . count($colunas) . " campos</p>";
    } catch (Exception $e) {
        echo "<p class='erro'>❌ Erro ao verificar campos Asaas: " . $e->getMessage() . "</p>";
    }

    echo "<h2>🎉 Instalação Concluída!</h2>";
    echo "<p class='ok'>✅ A integração com Asaas foi instalada com sucesso!</p>";
    
    echo "<h3>🔗 Próximos passos:</h3>";
    echo "<ol>";
    echo "<li><a href='configuracoes.php?acao=asaas' target='_blank'>📝 Configurar API do Asaas</a></li>";
    echo "<li><a href='boletos.php?acao=gerar' target='_blank'>🎫 Gerar primeiro boleto</a></li>";
    echo "<li><a href='boletos.php' target='_blank'>📋 Listar boletos</a></li>";
    echo "</ol>";
    
    echo "<h3>📊 Funcionalidades disponíveis:</h3>";
    echo "<ul>";
    echo "<li>✅ Geração automática de boletos via Asaas</li>";
    echo "<li>✅ Configuração completa da API</li>";
    echo "<li>✅ Controle de multas e juros</li>";
    echo "<li>✅ Desconto por antecipação</li>";
    echo "<li>✅ Webhook para atualizações automáticas</li>";
    echo "<li>✅ Logs detalhados de todas as operações</li>";
    echo "<li>✅ Fallback para geração local</li>";
    echo "</ul>";

    echo "<h3>⚙️ Como configurar:</h3>";
    echo "<ol>";
    echo "<li><strong>Obtenha sua API Key:</strong> Acesse o painel do Asaas → Configurações → Integrações</li>";
    echo "<li><strong>Configure o ambiente:</strong> Use 'Sandbox' para testes e 'Produção' para uso real</li>";
    echo "<li><strong>Defina multas e juros:</strong> Configure os percentuais conforme sua política</li>";
    echo "<li><strong>Configure webhook (opcional):</strong> Para receber notificações automáticas de pagamento</li>";
    echo "<li><strong>Teste a integração:</strong> Gere um boleto de teste</li>";
    echo "</ol>";

    echo "<h3>🔒 Segurança:</h3>";
    echo "<ul>";
    echo "<li>✅ API Key armazenada de forma segura no banco</li>";
    echo "<li>✅ Logs detalhados de todas as operações</li>";
    echo "<li>✅ Validação de dados antes do envio</li>";
    echo "<li>✅ Fallback para geração local em caso de erro</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ ERRO!</h2>";
    echo "<p class='erro'>Erro: " . $e->getMessage() . "</p>";
    echo "<p class='erro'>Arquivo: " . $e->getFile() . "</p>";
    echo "<p class='erro'>Linha: " . $e->getLine() . "</p>";
    echo "<pre class='erro'>" . $e->getTraceAsString() . "</pre>";
}
?>
