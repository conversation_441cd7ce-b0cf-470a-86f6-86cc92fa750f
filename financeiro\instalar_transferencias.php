<?php
/**
 * Script para instalar o módulo de transferências bancárias
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

if (!usuarioTemPermissao('financeiro', 'administrar')) {
    die('Sem permissão para executar este script');
}

$db = Database::getInstance();

echo "<h1>🏦 Instalação do Módulo de Transferências Bancárias</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .info { color: blue; }
    .aviso { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>";

try {
    echo "<h2>1. Verificando se as tabelas já existem</h2>";
    
    // Verificar se a tabela de transferências existe
    $tabela_transferencias = $db->fetchOne("SHOW TABLES LIKE 'transferencias_bancarias'");
    $tabela_saldos = $db->fetchOne("SHOW TABLES LIKE 'saldos_contas_bancarias'");
    
    if ($tabela_transferencias) {
        echo "<p class='aviso'>⚠️ Tabela 'transferencias_bancarias' já existe</p>";
    } else {
        echo "<p class='info'>ℹ️ Tabela 'transferencias_bancarias' não existe - será criada</p>";
    }
    
    if ($tabela_saldos) {
        echo "<p class='aviso'>⚠️ Tabela 'saldos_contas_bancarias' já existe</p>";
    } else {
        echo "<p class='info'>ℹ️ Tabela 'saldos_contas_bancarias' não existe - será criada</p>";
    }

    echo "<h2>2. Criando tabelas</h2>";
    
    // Criar tabela de transferências
    if (!$tabela_transferencias) {
        $sql_transferencias = "
        CREATE TABLE `transferencias_bancarias` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `tipo_operacao` enum('transferencia','deposito','saque','pix_enviado','pix_recebido','ted_enviado','ted_recebido','doc_enviado','doc_recebido','tarifa','juros') NOT NULL,
          `conta_origem_id` int(11) DEFAULT NULL,
          `conta_destino_id` int(11) DEFAULT NULL,
          `valor` decimal(10,2) NOT NULL,
          `descricao` varchar(255) NOT NULL,
          `data_operacao` datetime NOT NULL,
          `numero_documento` varchar(100) DEFAULT NULL,
          `chave_pix` varchar(255) DEFAULT NULL,
          `banco_destino` varchar(100) DEFAULT NULL,
          `agencia_destino` varchar(20) DEFAULT NULL,
          `conta_destino_numero` varchar(30) DEFAULT NULL,
          `nome_favorecido` varchar(255) DEFAULT NULL,
          `cpf_cnpj_favorecido` varchar(20) DEFAULT NULL,
          `tarifa` decimal(10,2) DEFAULT 0.00,
          `categoria_id` int(11) DEFAULT NULL,
          `observacoes` text DEFAULT NULL,
          `comprovante_arquivo` varchar(255) DEFAULT NULL,
          `status` enum('pendente','processada','cancelada','estornada') NOT NULL DEFAULT 'processada',
          `usuario_id` int(11) NOT NULL,
          `created_at` datetime NOT NULL DEFAULT current_timestamp(),
          `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          KEY `idx_conta_origem` (`conta_origem_id`),
          KEY `idx_conta_destino` (`conta_destino_id`),
          KEY `idx_data_operacao` (`data_operacao`),
          KEY `idx_tipo_operacao` (`tipo_operacao`),
          KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $db->query($sql_transferencias);
        echo "<p class='ok'>✅ Tabela 'transferencias_bancarias' criada com sucesso</p>";
    }
    
    // Criar tabela de saldos
    if (!$tabela_saldos) {
        $sql_saldos = "
        CREATE TABLE `saldos_contas_bancarias` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `conta_bancaria_id` int(11) NOT NULL,
          `data_saldo` date NOT NULL,
          `saldo_inicial_dia` decimal(10,2) NOT NULL DEFAULT 0.00,
          `total_entradas` decimal(10,2) NOT NULL DEFAULT 0.00,
          `total_saidas` decimal(10,2) NOT NULL DEFAULT 0.00,
          `saldo_final_dia` decimal(10,2) NOT NULL DEFAULT 0.00,
          `created_at` datetime NOT NULL DEFAULT current_timestamp(),
          `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
          PRIMARY KEY (`id`),
          UNIQUE KEY `uk_conta_data` (`conta_bancaria_id`, `data_saldo`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ";
        
        $db->query($sql_saldos);
        echo "<p class='ok'>✅ Tabela 'saldos_contas_bancarias' criada com sucesso</p>";
    }

    echo "<h2>3. Inserindo dados de exemplo</h2>";
    
    // Buscar contas bancárias existentes
    $contas = $db->fetchAll("SELECT id, nome FROM contas_bancarias LIMIT 2");
    
    if (count($contas) >= 1) {
        $conta1 = $contas[0];
        $conta2 = count($contas) > 1 ? $contas[1] : $conta1;
        
        // Inserir transferências de exemplo
        $transferencias_exemplo = [
            [
                'tipo_operacao' => 'pix_enviado',
                'conta_origem_id' => $conta1['id'],
                'valor' => 500.00,
                'descricao' => 'PIX para fornecedor - Material de escritório',
                'data_operacao' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'chave_pix' => '11999887766',
                'nome_favorecido' => 'Papelaria ABC Ltda',
                'cpf_cnpj_favorecido' => '12.345.678/0001-90',
                'tarifa' => 0.00
            ],
            [
                'tipo_operacao' => 'pix_recebido',
                'conta_destino_id' => $conta1['id'],
                'valor' => 850.00,
                'descricao' => 'PIX recebido - Mensalidade João Silva',
                'data_operacao' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'chave_pix' => '<EMAIL>',
                'nome_favorecido' => 'João Silva',
                'cpf_cnpj_favorecido' => '123.456.789-00',
                'tarifa' => 0.00
            ],
            [
                'tipo_operacao' => 'transferencia',
                'conta_origem_id' => $conta1['id'],
                'conta_destino_id' => $conta2['id'],
                'valor' => 1000.00,
                'descricao' => 'Transferência entre contas - Reserva de emergência',
                'data_operacao' => date('Y-m-d H:i:s'),
                'tarifa' => 0.00
            ],
            [
                'tipo_operacao' => 'ted_enviado',
                'conta_origem_id' => $conta1['id'],
                'valor' => 2500.00,
                'descricao' => 'TED para pagamento de fornecedor',
                'data_operacao' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'banco_destino' => 'Banco do Brasil',
                'agencia_destino' => '1234-5',
                'conta_destino_numero' => '12345-6',
                'nome_favorecido' => 'Fornecedor XYZ Ltda',
                'cpf_cnpj_favorecido' => '98.765.432/0001-10',
                'tarifa' => 15.00
            ]
        ];
        
        foreach ($transferencias_exemplo as $transferencia) {
            // Verificar se já existe
            $existe = $db->fetchOne("SELECT id FROM transferencias_bancarias WHERE descricao = ?", [$transferencia['descricao']]);
            
            if (!$existe) {
                $sql = "INSERT INTO transferencias_bancarias 
                        (tipo_operacao, conta_origem_id, conta_destino_id, valor, descricao, data_operacao, 
                         numero_documento, chave_pix, banco_destino, agencia_destino, conta_destino_numero, 
                         nome_favorecido, cpf_cnpj_favorecido, tarifa, usuario_id, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                
                $params = [
                    $transferencia['tipo_operacao'],
                    $transferencia['conta_origem_id'] ?? null,
                    $transferencia['conta_destino_id'] ?? null,
                    $transferencia['valor'],
                    $transferencia['descricao'],
                    $transferencia['data_operacao'],
                    $transferencia['numero_documento'] ?? null,
                    $transferencia['chave_pix'] ?? null,
                    $transferencia['banco_destino'] ?? null,
                    $transferencia['agencia_destino'] ?? null,
                    $transferencia['conta_destino_numero'] ?? null,
                    $transferencia['nome_favorecido'] ?? null,
                    $transferencia['cpf_cnpj_favorecido'] ?? null,
                    $transferencia['tarifa'],
                    $_SESSION['user_id']
                ];
                
                $db->query($sql, $params);
                echo "<p class='ok'>✅ Transferência '{$transferencia['descricao']}' inserida</p>";
            } else {
                echo "<p class='info'>ℹ️ Transferência '{$transferencia['descricao']}' já existe</p>";
            }
        }
    } else {
        echo "<p class='aviso'>⚠️ Nenhuma conta bancária encontrada. Crie contas bancárias primeiro.</p>";
    }

    echo "<h2>4. Verificando instalação</h2>";
    
    $total_transferencias = $db->fetchOne("SELECT COUNT(*) as total FROM transferencias_bancarias")['total'];
    echo "<p class='ok'>✅ Total de transferências: $total_transferencias</p>";
    
    $tipos_operacao = $db->fetchAll("SELECT tipo_operacao, COUNT(*) as total FROM transferencias_bancarias GROUP BY tipo_operacao");
    echo "<p class='ok'>✅ Tipos de operação:</p>";
    echo "<ul>";
    foreach ($tipos_operacao as $tipo) {
        echo "<li>{$tipo['tipo_operacao']}: {$tipo['total']} operações</li>";
    }
    echo "</ul>";

    echo "<h2>🎉 Instalação Concluída!</h2>";
    echo "<p class='ok'>✅ O módulo de transferências bancárias foi instalado com sucesso!</p>";
    
    echo "<h3>🔗 Links para testar:</h3>";
    echo "<ul>";
    echo "<li><a href='transferencias.php' target='_blank'>📋 Lista de Transferências</a></li>";
    echo "<li><a href='transferencias.php?acao=nova' target='_blank'>➕ Nova Transferência</a></li>";
    echo "<li><a href='tesouraria.php' target='_blank'>💰 Tesouraria (com saldos atualizados)</a></li>";
    echo "</ul>";
    
    echo "<h3>📊 Funcionalidades disponíveis:</h3>";
    echo "<ul>";
    echo "<li>✅ Registro de PIX (enviado/recebido)</li>";
    echo "<li>✅ Registro de TED (enviado/recebido)</li>";
    echo "<li>✅ Registro de DOC (enviado/recebido)</li>";
    echo "<li>✅ Transferências entre contas próprias</li>";
    echo "<li>✅ Depósitos e saques</li>";
    echo "<li>✅ Controle de tarifas</li>";
    echo "<li>✅ Atualização automática de saldos</li>";
    echo "<li>✅ Relatórios e extratos</li>";
    echo "</ul>";

} catch (Exception $e) {
    echo "<h2>❌ ERRO!</h2>";
    echo "<p class='erro'>Erro: " . $e->getMessage() . "</p>";
    echo "<p class='erro'>Arquivo: " . $e->getFile() . "</p>";
    echo "<p class='erro'>Linha: " . $e->getLine() . "</p>";
    echo "<pre class='erro'>" . $e->getTraceAsString() . "</pre>";
}
?>
