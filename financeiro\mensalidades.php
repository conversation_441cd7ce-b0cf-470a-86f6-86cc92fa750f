<?php
/**
 * ============================================================================
 * MENSALIDADES - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Gerenciamento de mensalidades dos alunos
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!verificarPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'gerar_mensalidades':
                $mes = $_POST['mes'];
                $ano = $_POST['ano'];
                $polo_id = $_POST['polo_id'] ?? null;
                $curso_id = $_POST['curso_id'] ?? null;
                
                // Busca matrículas ativas
                $where_clauses = ["m.status = 'ativo'"];
                $params = [];
                
                if ($polo_id) {
                    $where_clauses[] = "m.polo_id = ?";
                    $params[] = $polo_id;
                }
                if ($curso_id) {
                    $where_clauses[] = "m.curso_id = ?";
                    $params[] = $curso_id;
                }
                
                $where_sql = implode(' AND ', $where_clauses);
                
                $sql = "SELECT m.*, a.nome as aluno_nome, a.email, c.nome as curso_nome, 
                               c.valor_mensalidade, p.nome as polo_nome
                        FROM matriculas m
                        JOIN alunos a ON m.aluno_id = a.id
                        JOIN cursos c ON m.curso_id = c.id
                        LEFT JOIN polos p ON m.polo_id = p.id
                        WHERE $where_sql";
                
                $matriculas = $db->fetchAll($sql, $params);
                
                $geradas = 0;
                $data_vencimento = "$ano-$mes-10"; // Vencimento dia 10
                
                foreach ($matriculas as $matricula) {
                    // Verifica se já existe mensalidade para este período
                    $mes_referencia = "$ano-$mes-01"; // Primeiro dia do mês de referência
                    $existe = $db->fetchOne(
                        "SELECT id FROM mensalidades_alunos WHERE aluno_id = ? AND mes_referencia = ?",
                        [$matricula['aluno_id'], $mes_referencia]
                    );

                    if (!$existe) {
                        $descricao = "Mensalidade {$matricula['aluno_nome']} - $mes/$ano";

                        $sql_insert = "INSERT INTO mensalidades_alunos
                                      (aluno_id, matricula_id, curso_id, descricao, valor,
                                       data_vencimento, mes_referencia, status, created_at)
                                      VALUES (?, ?, ?, ?, ?, ?, ?, 'pendente', NOW())";

                        $db->execute($sql_insert, [
                            $matricula['aluno_id'],
                            $matricula['id'],
                            $matricula['curso_id'],
                            $descricao,
                            $matricula['valor_mensalidade'],
                            $data_vencimento,
                            $mes_referencia
                        ]);
                        $geradas++;
                    }
                }
                
                $mensagem = "$geradas mensalidades geradas com sucesso!";
                $tipo_mensagem = 'success';
                break;

            case 'receber_mensalidade':
                $valor_recebido = str_replace(['.', ','], ['', '.'], $_POST['valor_recebido']);
                $data_pagamento = $_POST['data_pagamento'];
                $forma_pagamento = $_POST['forma_pagamento'];
                $observacoes = $_POST['observacoes'] ?? '';

                // Atualiza a mensalidade
                $sql = "UPDATE mensalidades_alunos SET status = 'pago',
                       data_pagamento = ?, forma_pagamento = ?, observacoes = ?,
                       updated_at = NOW() WHERE id = ?";
                $db->execute($sql, [$data_pagamento, $forma_pagamento, $observacoes, $id]);

                // Busca dados da mensalidade para registrar transação
                $mensalidade = $db->fetchOne(
                    "SELECT ma.*, a.nome as aluno_nome, c.nome as curso_nome 
                     FROM mensalidades_alunos ma
                     JOIN alunos a ON ma.aluno_id = a.id
                     JOIN cursos c ON ma.curso_id = c.id
                     WHERE ma.id = ?", 
                    [$id]
                );

                // Registra a transação
                $descricao = "Mensalidade - {$mensalidade['aluno_nome']} - {$mensalidade['curso_nome']} - {$mensalidade['mes']}/{$mensalidade['ano']}";
                $sql_transacao = "INSERT INTO transacoes_financeiras 
                                 (tipo, descricao, valor, data_transacao, forma_pagamento, 
                                  referencia_tipo, referencia_id, usuario_id) 
                                 VALUES ('receita', ?, ?, ?, ?, 'mensalidade', ?, ?)";
                $db->execute($sql_transacao, [
                    $descricao, $valor_recebido, $data_pagamento, $forma_pagamento, $id, $_SESSION['usuario_id']
                ]);

                $mensagem = "Pagamento de mensalidade registrado com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'listar';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para listagem
if ($acao === 'listar') {
    $filtros = [];
    $params = [];
    $where_clauses = [];

    // Filtros
    if (!empty($_GET['status'])) {
        $where_clauses[] = "ma.status = ?";
        $params[] = $_GET['status'];
    }
    if (!empty($_GET['polo'])) {
        $where_clauses[] = "p.id = ?";
        $params[] = $_GET['polo'];
    }
    if (!empty($_GET['curso'])) {
        $where_clauses[] = "ma.curso_id = ?";
        $params[] = $_GET['curso'];
    }
    if (!empty($_GET['mes']) && !empty($_GET['ano'])) {
        $mes_referencia = $_GET['ano'] . '-' . sprintf('%02d', $_GET['mes']) . '-01';
        $where_clauses[] = "ma.mes_referencia = ?";
        $params[] = $mes_referencia;
    } elseif (!empty($_GET['mes'])) {
        $where_clauses[] = "MONTH(ma.mes_referencia) = ?";
        $params[] = $_GET['mes'];
    } elseif (!empty($_GET['ano'])) {
        $where_clauses[] = "YEAR(ma.mes_referencia) = ?";
        $params[] = $_GET['ano'];
    }
    if (!empty($_GET['busca'])) {
        $where_clauses[] = "a.nome LIKE ?";
        $params[] = '%' . $_GET['busca'] . '%';
    }

    $where_sql = !empty($where_clauses) ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    $sql = "SELECT ma.*, a.nome as aluno_nome, a.cpf, c.nome as curso_nome,
                   p.nome as polo_nome,
                   MONTH(ma.mes_referencia) as mes,
                   YEAR(ma.mes_referencia) as ano
            FROM mensalidades_alunos ma
            JOIN alunos a ON ma.aluno_id = a.id
            JOIN cursos c ON ma.curso_id = c.id
            LEFT JOIN matriculas m ON ma.matricula_id = m.id
            LEFT JOIN polos p ON m.polo_id = p.id
            $where_sql
            ORDER BY ma.mes_referencia DESC, a.nome ASC";
    
    $mensalidades = $db->fetchAll($sql, $params);

    // Busca dados para filtros
    $polos = $db->fetchAll("SELECT id, nome FROM polos ORDER BY nome");
    $cursos = $db->fetchAll("SELECT id, nome FROM cursos ORDER BY nome");
}

// Busca dados para geração de mensalidades
if ($acao === 'gerar') {
    $polos = $db->fetchAll("SELECT id, nome FROM polos ORDER BY nome");
    $cursos = $db->fetchAll("SELECT id, nome FROM cursos ORDER BY nome");
}

// Busca dados para recebimento
if ($acao === 'receber' && $id) {
    $mensalidade = $db->fetchOne(
        "SELECT ma.*, a.nome as aluno_nome, a.cpf, c.nome as curso_nome, p.nome as polo_nome,
                MONTH(ma.mes_referencia) as mes,
                YEAR(ma.mes_referencia) as ano
         FROM mensalidades_alunos ma
         JOIN alunos a ON ma.aluno_id = a.id
         JOIN cursos c ON ma.curso_id = c.id
         LEFT JOIN matriculas m ON ma.matricula_id = m.id
         LEFT JOIN polos p ON m.polo_id = p.id
         WHERE ma.id = ?", 
        [$id]
    );
    
    if (!$mensalidade || $mensalidade['status'] !== 'pendente') {
        header('Location: mensalidades.php?erro=mensalidade_nao_encontrada');
        exit;
    }
}

$titulo_pagina = "Mensalidades";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-graduation-cap text-purple-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Gerenciamento de mensalidades dos alunos</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <?php if ($acao === 'listar'): ?>
                            <a href="mensalidades.php?acao=gerar" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                Gerar Mensalidades
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'listar':
                        include 'views/mensalidades/listar.php';
                        break;
                    case 'gerar':
                        include 'views/mensalidades/gerar.php';
                        break;
                    case 'receber':
                        include 'views/mensalidades/receber.php';
                        break;
                    default:
                        include 'views/mensalidades/listar.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        // Formatação de valores monetários
        function formatarMoeda(input) {
            let valor = input.value.replace(/\D/g, '');
            valor = (valor / 100).toFixed(2) + '';
            valor = valor.replace(".", ",");
            valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
            input.value = valor;
        }

        // Auto-submit do formulário de filtros
        document.addEventListener('DOMContentLoaded', function() {
            const filtros = document.querySelectorAll('.filtro-auto');
            filtros.forEach(filtro => {
                filtro.addEventListener('change', function() {
                    document.getElementById('form-filtros').submit();
                });
            });
        });
    </script>
</body>
</html>
