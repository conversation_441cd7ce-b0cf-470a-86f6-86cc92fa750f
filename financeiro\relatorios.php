<?php
/**
 * ============================================================================
 * RELATÓRIOS - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Relatórios gerenciais e financeiros
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$tipo = $_GET['tipo'] ?? 'dashboard';
$formato = $_GET['formato'] ?? 'html';
$data_inicio = $_GET['data_inicio'] ?? date('Y-m-01');
$data_fim = $_GET['data_fim'] ?? date('Y-m-d');

// Dados para relatórios
$dados_relatorio = [];

try {
    switch ($tipo) {
        case 'dre':
            // Demonstrativo de Resultados do Exercício
            // Buscar receitas (contas recebidas)
            $sql_receitas = "
                SELECT
                    COALESCE(cf.nome, 'Sem categoria') as categoria,
                    SUM(cr.valor) as total
                FROM contas_receber cr
                LEFT JOIN categorias_financeiras cf ON cr.categoria_id = cf.id
                WHERE cr.status = 'recebido'
                AND DATE(COALESCE(cr.data_recebimento, cr.data_vencimento)) BETWEEN ? AND ?
                GROUP BY cr.categoria_id, cf.nome
                ORDER BY total DESC
            ";
            $receitas = $db->fetchAll($sql_receitas, [$data_inicio, $data_fim]);

            // Buscar despesas (contas pagas)
            $sql_despesas = "
                SELECT
                    COALESCE(cf.nome, 'Sem categoria') as categoria,
                    SUM(cp.valor) as total
                FROM contas_pagar cp
                LEFT JOIN categorias_financeiras cf ON cp.categoria_id = cf.id
                WHERE cp.status = 'pago'
                AND DATE(COALESCE(cp.data_pagamento, cp.data_vencimento)) BETWEEN ? AND ?
                GROUP BY cp.categoria_id, cf.nome
                ORDER BY total DESC
            ";
            $despesas = $db->fetchAll($sql_despesas, [$data_inicio, $data_fim]);

            // Verificar se tabela de transferências existe e adicionar tarifas
            $tabela_transferencias_existe = false;
            try {
                $db->fetchOne("SELECT 1 FROM transferencias_bancarias LIMIT 1");
                $tabela_transferencias_existe = true;
            } catch (Exception $e) {
                // Tabela não existe
            }

            if ($tabela_transferencias_existe) {
                // Buscar tarifas bancárias como despesas
                $sql_tarifas = "
                    SELECT
                        'Tarifas Bancárias' as categoria,
                        SUM(tarifa) as total
                    FROM transferencias_bancarias
                    WHERE status = 'processada'
                    AND tarifa > 0
                    AND DATE(data_operacao) BETWEEN ? AND ?
                ";
                $tarifas = $db->fetchOne($sql_tarifas, [$data_inicio, $data_fim]);

                if ($tarifas && $tarifas['total'] > 0) {
                    $despesas[] = $tarifas;
                }

                // Buscar transferências enviadas (PIX, TED, etc.) como despesas operacionais
                $sql_transferencias_saida = "
                    SELECT
                        'Transferências e PIX Enviados' as categoria,
                        SUM(valor) as total
                    FROM transferencias_bancarias
                    WHERE status = 'processada'
                    AND tipo_operacao IN ('pix_enviado', 'ted_enviado', 'doc_enviado', 'saque')
                    AND DATE(data_operacao) BETWEEN ? AND ?
                ";
                $transferencias_saida = $db->fetchOne($sql_transferencias_saida, [$data_inicio, $data_fim]);

                if ($transferencias_saida && $transferencias_saida['total'] > 0) {
                    $despesas[] = $transferencias_saida;
                }
            }

            $total_receitas = array_sum(array_column($receitas, 'total'));
            $total_despesas = array_sum(array_column($despesas, 'total'));
            $resultado = $total_receitas - $total_despesas;

            $dados_relatorio = [
                'receitas' => $receitas,
                'despesas' => $despesas,
                'total_receitas' => $total_receitas,
                'total_despesas' => $total_despesas,
                'resultado' => $resultado
            ];
            break;

        case 'fluxo_caixa':
            // Verificar se tabela de transferências existe
            $tabela_transferencias_existe = false;
            try {
                $db->fetchOne("SELECT 1 FROM transferencias_bancarias LIMIT 1");
                $tabela_transferencias_existe = true;
            } catch (Exception $e) {
                // Tabela não existe
            }

            // Fluxo de Caixa baseado em contas pagas/recebidas + transferências
            $sql_fluxo = "
                SELECT
                    DATE(COALESCE(data_recebimento, data_vencimento)) as data,
                    SUM(valor) as receitas,
                    0 as despesas
                FROM contas_receber
                WHERE status = 'recebido'
                AND DATE(COALESCE(data_recebimento, data_vencimento)) BETWEEN ? AND ?
                GROUP BY DATE(COALESCE(data_recebimento, data_vencimento))

                UNION ALL

                SELECT
                    DATE(COALESCE(data_pagamento, data_vencimento)) as data,
                    0 as receitas,
                    SUM(valor) as despesas
                FROM contas_pagar
                WHERE status = 'pago'
                AND DATE(COALESCE(data_pagamento, data_vencimento)) BETWEEN ? AND ?
                GROUP BY DATE(COALESCE(data_pagamento, data_vencimento))
            ";

            $params = [$data_inicio, $data_fim, $data_inicio, $data_fim];

            // Adicionar transferências se a tabela existir
            if ($tabela_transferencias_existe) {
                $sql_fluxo .= "
                    UNION ALL

                    SELECT
                        DATE(data_operacao) as data,
                        SUM(CASE WHEN tipo_operacao IN ('pix_recebido', 'ted_recebido', 'doc_recebido', 'deposito') THEN valor ELSE 0 END) as receitas,
                        SUM(CASE WHEN tipo_operacao IN ('pix_enviado', 'ted_enviado', 'doc_enviado', 'saque', 'tarifa') THEN valor + tarifa ELSE 0 END) as despesas
                    FROM transferencias_bancarias
                    WHERE status = 'processada'
                    AND DATE(data_operacao) BETWEEN ? AND ?
                    GROUP BY DATE(data_operacao)
                ";
                $params[] = $data_inicio;
                $params[] = $data_fim;
            }

            $sql_fluxo .= " ORDER BY data ASC";

            $fluxo_raw = $db->fetchAll($sql_fluxo, $params);

            // Consolidar dados por data
            $fluxo = [];
            foreach ($fluxo_raw as $item) {
                $data = $item['data'];
                if (!isset($fluxo[$data])) {
                    $fluxo[$data] = ['data' => $data, 'receitas' => 0, 'despesas' => 0, 'saldo_dia' => 0];
                }
                $fluxo[$data]['receitas'] += $item['receitas'];
                $fluxo[$data]['despesas'] += $item['despesas'];
                $fluxo[$data]['saldo_dia'] = $fluxo[$data]['receitas'] - $fluxo[$data]['despesas'];
            }
            $fluxo = array_values($fluxo);

            // Ordenar por data
            usort($fluxo, function($a, $b) {
                return strtotime($a['data']) - strtotime($b['data']);
            });

            // Calcula saldo acumulado
            $saldo_acumulado = 0;
            foreach ($fluxo as &$dia) {
                $saldo_acumulado += $dia['saldo_dia'];
                $dia['saldo_acumulado'] = $saldo_acumulado;
            }

            $dados_relatorio = ['fluxo' => $fluxo];
            break;

        case 'contas_pagar':
            // Relatório de Contas a Pagar
            $sql_contas = "
                SELECT cp.*, cf.nome as categoria_nome
                FROM contas_pagar cp
                LEFT JOIN categorias_financeiras cf ON cp.categoria_id = cf.id
                WHERE DATE(cp.data_vencimento) BETWEEN ? AND ?
                ORDER BY cp.data_vencimento ASC, cp.status ASC
            ";
            $contas = $db->fetchAll($sql_contas, [$data_inicio, $data_fim]);

            $resumo = [
                'total_pendente' => 0,
                'total_pago' => 0,
                'total_vencido' => 0,
                'count_pendente' => 0,
                'count_pago' => 0,
                'count_vencido' => 0
            ];

            foreach ($contas as $conta) {
                if ($conta['status'] === 'pago') {
                    $resumo['total_pago'] += $conta['valor_pago'] ?? $conta['valor'];
                    $resumo['count_pago']++;
                } else {
                    $resumo['total_pendente'] += $conta['valor'];
                    $resumo['count_pendente']++;
                    if ($conta['data_vencimento'] < date('Y-m-d')) {
                        $resumo['total_vencido'] += $conta['valor'];
                        $resumo['count_vencido']++;
                    }
                }
            }

            $dados_relatorio = [
                'contas' => $contas,
                'resumo' => $resumo
            ];
            break;

        case 'contas_receber':
            // Relatório de Contas a Receber
            $sql_contas = "
                SELECT cr.*, cf.nome as categoria_nome
                FROM contas_receber cr
                LEFT JOIN categorias_financeiras cf ON cr.categoria_id = cf.id
                WHERE DATE(cr.data_vencimento) BETWEEN ? AND ?
                ORDER BY cr.data_vencimento ASC, cr.status ASC
            ";
            $contas = $db->fetchAll($sql_contas, [$data_inicio, $data_fim]);

            $resumo = [
                'total_pendente' => 0,
                'total_recebido' => 0,
                'total_vencido' => 0,
                'count_pendente' => 0,
                'count_recebido' => 0,
                'count_vencido' => 0
            ];

            foreach ($contas as $conta) {
                if ($conta['status'] === 'recebido') {
                    $resumo['total_recebido'] += $conta['valor_recebido'] ?? $conta['valor'];
                    $resumo['count_recebido']++;
                } else {
                    $resumo['total_pendente'] += $conta['valor'];
                    $resumo['count_pendente']++;
                    if ($conta['data_vencimento'] < date('Y-m-d')) {
                        $resumo['total_vencido'] += $conta['valor'];
                        $resumo['count_vencido']++;
                    }
                }
            }

            $dados_relatorio = [
                'contas' => $contas,
                'resumo' => $resumo
            ];
            break;

        case 'mensalidades':
            // Relatório de Mensalidades
            $mes = $_GET['mes'] ?? date('m');
            $ano = $_GET['ano'] ?? date('Y');

            $sql_mensalidades = "
                SELECT ma.*, a.nome as aluno_nome, a.cpf, c.nome as curso_nome, 
                       p.nome as polo_nome
                FROM mensalidades_alunos ma
                JOIN alunos a ON ma.aluno_id = a.id
                JOIN cursos c ON ma.curso_id = c.id
                LEFT JOIN polos p ON ma.polo_id = p.id
                WHERE ma.mes = ? AND ma.ano = ?
                ORDER BY p.nome, c.nome, a.nome
            ";
            $mensalidades = $db->fetchAll($sql_mensalidades, [$mes, $ano]);

            $resumo = [
                'total_valor' => 0,
                'total_pago' => 0,
                'total_pendente' => 0,
                'count_total' => count($mensalidades),
                'count_pago' => 0,
                'count_pendente' => 0
            ];

            foreach ($mensalidades as $mensalidade) {
                $resumo['total_valor'] += $mensalidade['valor'];
                if ($mensalidade['status'] === 'pago') {
                    $resumo['total_pago'] += $mensalidade['valor_pago'] ?? $mensalidade['valor'];
                    $resumo['count_pago']++;
                } else {
                    $resumo['total_pendente'] += $mensalidade['valor'];
                    $resumo['count_pendente']++;
                }
            }

            $dados_relatorio = [
                'mensalidades' => $mensalidades,
                'resumo' => $resumo,
                'mes' => $mes,
                'ano' => $ano
            ];
            break;

        default:
            // Dashboard de relatórios
            // Resumo mensal baseado em contas pagas/recebidas
            $sql_receitas_mes = "
                SELECT
                    COALESCE(SUM(valor), 0) as total_receitas,
                    COUNT(*) as qtd_receitas
                FROM contas_receber
                WHERE status = 'recebido'
                AND MONTH(COALESCE(data_recebimento, data_vencimento)) = MONTH(CURDATE())
                AND YEAR(COALESCE(data_recebimento, data_vencimento)) = YEAR(CURDATE())
            ";
            $receitas_mes = $db->fetchOne($sql_receitas_mes);

            $sql_despesas_mes = "
                SELECT
                    COALESCE(SUM(valor), 0) as total_despesas,
                    COUNT(*) as qtd_despesas
                FROM contas_pagar
                WHERE status = 'pago'
                AND MONTH(COALESCE(data_pagamento, data_vencimento)) = MONTH(CURDATE())
                AND YEAR(COALESCE(data_pagamento, data_vencimento)) = YEAR(CURDATE())
            ";
            $despesas_mes = $db->fetchOne($sql_despesas_mes);

            $resumo_mes = [
                'total_receitas' => $receitas_mes['total_receitas'],
                'total_despesas' => $despesas_mes['total_despesas'],
                'qtd_receitas' => $receitas_mes['qtd_receitas'],
                'qtd_despesas' => $despesas_mes['qtd_despesas']
            ];

            $dados_relatorio = ['resumo_mes' => $resumo_mes];
            break;
    }
} catch (Exception $e) {
    error_log("Erro ao gerar relatório: " . $e->getMessage());

    // Valores padrão baseados no tipo de relatório
    switch ($tipo) {
        case 'dre':
            $dados_relatorio = [
                'receitas' => [],
                'despesas' => [],
                'total_receitas' => 0,
                'total_despesas' => 0,
                'resultado' => 0
            ];
            break;
        case 'fluxo_caixa':
            $dados_relatorio = ['fluxo' => []];
            break;
        case 'contas_pagar':
        case 'contas_receber':
        case 'mensalidades':
            $dados_relatorio = [
                'contas' => [],
                'resumo' => ['total' => 0, 'pendentes' => 0, 'pagas' => 0, 'vencidas' => 0]
            ];
            break;
        default:
            $dados_relatorio = [];
    }
}

// Exportação para Excel/PDF
if ($formato !== 'html') {
    // Aqui seria implementada a exportação
    // Por enquanto, redirecionamos de volta
    header("Location: relatorios.php?tipo=$tipo&data_inicio=$data_inicio&data_fim=$data_fim");
    exit;
}

$titulo_pagina = "Relatórios Financeiros";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-chart-bar text-indigo-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Relatórios gerenciais e análises financeiras</p>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- Menu de Relatórios -->
                        <div class="relative">
                            <select onchange="window.location.href=this.value" class="bg-white border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-indigo-500">
                                <option value="relatorios.php?tipo=dashboard" <?php echo $tipo === 'dashboard' ? 'selected' : ''; ?>>Dashboard</option>
                                <option value="relatorios.php?tipo=dre" <?php echo $tipo === 'dre' ? 'selected' : ''; ?>>DRE</option>
                                <option value="relatorios.php?tipo=fluxo_caixa" <?php echo $tipo === 'fluxo_caixa' ? 'selected' : ''; ?>>Fluxo de Caixa</option>
                                <option value="relatorios.php?tipo=contas_pagar" <?php echo $tipo === 'contas_pagar' ? 'selected' : ''; ?>>Contas a Pagar</option>
                                <option value="relatorios.php?tipo=contas_receber" <?php echo $tipo === 'contas_receber' ? 'selected' : ''; ?>>Contas a Receber</option>
                                <option value="relatorios.php?tipo=mensalidades" <?php echo $tipo === 'mensalidades' ? 'selected' : ''; ?>>Mensalidades</option>
                            </select>
                        </div>
                        
                        <?php if ($tipo !== 'dashboard'): ?>
                            <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-print mr-2"></i>
                                Imprimir
                            </button>
                            <a href="relatorios.php?<?php echo http_build_query(array_merge($_GET, ['formato' => 'excel'])); ?>" 
                               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-file-excel mr-2"></i>
                                Excel
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <?php
                // Inclui a view correspondente
                switch ($tipo) {
                    case 'dre':
                        include 'views/relatorios/dre.php';
                        break;
                    case 'fluxo_caixa':
                        include 'views/relatorios/fluxo_caixa.php';
                        break;
                    case 'contas_pagar':
                        include 'views/relatorios/contas_pagar.php';
                        break;
                    case 'contas_receber':
                        include 'views/relatorios/contas_receber.php';
                        break;
                    case 'mensalidades':
                        include 'views/relatorios/mensalidades.php';
                        break;
                    default:
                        include 'views/relatorios/dashboard.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
</body>
</html>
