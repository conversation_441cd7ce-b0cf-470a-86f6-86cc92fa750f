-- Tabela para configurações da API do Asaas
CREATE TABLE IF NOT EXISTS `configuracoes_asaas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `api_key` varchar(255) DEFAULT NULL,
  `ambiente` enum('sandbox','producao') NOT NULL DEFAULT 'sandbox',
  `webhook_url` varchar(255) DEFAULT NULL,
  `webhook_token` varchar(255) DEFAULT NULL,
  `multa_percentual` decimal(5,2) DEFAULT 2.00,
  `juros_percentual` decimal(5,2) DEFAULT 1.00,
  `desconto_antecipacao_dias` int(11) DEFAULT 0,
  `desconto_antecipacao_percentual` decimal(5,2) DEFAULT 0.00,
  `dias_vencimento_padrao` int(11) DEFAULT 7,
  `instrucoes_boleto` text DEFAULT NULL,
  `observacoes_boleto` text DEFAULT NULL,
  `ativo` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Tabela para logs da API do Asaas
CREATE TABLE IF NOT EXISTS `logs_asaas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tipo_operacao` enum('criar_cobranca','consultar_cobranca','webhook','cancelar_cobranca','estornar_cobranca') NOT NULL,
  `cobranca_id_asaas` varchar(100) DEFAULT NULL,
  `cobranca_id_local` int(11) DEFAULT NULL,
  `request_data` text DEFAULT NULL,
  `response_data` text DEFAULT NULL,
  `status_code` int(11) DEFAULT NULL,
  `sucesso` tinyint(1) NOT NULL DEFAULT 0,
  `erro_mensagem` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_cobranca_asaas` (`cobranca_id_asaas`),
  KEY `idx_cobranca_local` (`cobranca_id_local`),
  KEY `idx_tipo_operacao` (`tipo_operacao`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Adicionar campos na tabela de boletos para integração com Asaas
ALTER TABLE `boletos` 
ADD COLUMN IF NOT EXISTS `asaas_id` varchar(100) DEFAULT NULL AFTER `id`,
ADD COLUMN IF NOT EXISTS `asaas_url` varchar(500) DEFAULT NULL AFTER `asaas_id`,
ADD COLUMN IF NOT EXISTS `asaas_linha_digitavel` varchar(100) DEFAULT NULL AFTER `asaas_url`,
ADD COLUMN IF NOT EXISTS `asaas_codigo_barras` varchar(100) DEFAULT NULL AFTER `asaas_linha_digitavel`,
ADD COLUMN IF NOT EXISTS `asaas_status` varchar(50) DEFAULT NULL AFTER `asaas_codigo_barras`,
ADD COLUMN IF NOT EXISTS `asaas_data_criacao` datetime DEFAULT NULL AFTER `asaas_status`,
ADD COLUMN IF NOT EXISTS `asaas_data_vencimento` date DEFAULT NULL AFTER `asaas_data_criacao`,
ADD COLUMN IF NOT EXISTS `webhook_recebido` tinyint(1) DEFAULT 0 AFTER `asaas_data_vencimento`;

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_boletos_asaas_id ON boletos(asaas_id);
CREATE INDEX IF NOT EXISTS idx_boletos_asaas_status ON boletos(asaas_status);

-- Inserir configuração padrão
INSERT IGNORE INTO `configuracoes_asaas` 
(`id`, `ambiente`, `multa_percentual`, `juros_percentual`, `dias_vencimento_padrao`, `instrucoes_boleto`, `observacoes_boleto`) 
VALUES 
(1, 'sandbox', 2.00, 1.00, 7, 
'Após o vencimento, multa de 2% e juros de 1% ao mês.', 
'Boleto gerado automaticamente pelo sistema Faciência ERP.');
