<?php
/**
 * ============================================================================
 * TESOURARIA - MÓDULO FINANCEIRO
 * ============================================================================
 *
 * Controle de caixa e contas bancárias
 *
 * <AUTHOR> Faciência ERP
 * @version 2.0
 * @since 2024
 * @updated 2025-07-11
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

// Verifica permissões
if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/index.php?erro=sem_permissao');
    exit;
}

// Inclui as classes necessárias
require_once '../secretaria/includes/Database.php';
require_once '../secretaria/includes/Utils.php';

// Conecta ao banco de dados
$db = Database::getInstance();

// Processa ações
$acao = $_GET['acao'] ?? 'dashboard';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processa formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($acao) {
            case 'movimentacao':
                $tipo = $_POST['tipo']; // entrada ou saida
                $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                $descricao = $_POST['descricao'];
                $categoria_id = $_POST['categoria_id'];
                $forma_pagamento = $_POST['forma_pagamento'];
                $observacoes = $_POST['observacoes'] ?? '';

                // Registra a transação
                $sql_transacao = "INSERT INTO transacoes_financeiras 
                                 (tipo, descricao, valor, data_transacao, forma_pagamento, 
                                  categoria_id, observacoes, usuario_id) 
                                 VALUES (?, ?, ?, NOW(), ?, ?, ?, ?)";
                
                $tipo_transacao = $tipo === 'entrada' ? 'receita' : 'despesa';
                $db->execute($sql_transacao, [
                    $tipo_transacao, $descricao, $valor, $forma_pagamento, 
                    $categoria_id, $observacoes, $_SESSION['usuario_id']
                ]);

                $mensagem = ucfirst($tipo) . " registrada com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'dashboard';
                break;

            case 'transferencia':
                $conta_origem = $_POST['conta_origem'];
                $conta_destino = $_POST['conta_destino'];
                $valor = str_replace(['.', ','], ['', '.'], $_POST['valor']);
                $descricao = $_POST['descricao'];
                $observacoes = $_POST['observacoes'] ?? '';

                // Registra saída da conta origem
                $sql_saida = "INSERT INTO transacoes_financeiras 
                             (tipo, descricao, valor, data_transacao, forma_pagamento, 
                              observacoes, usuario_id) 
                             VALUES ('despesa', ?, ?, NOW(), 'transferencia', ?, ?)";
                $db->execute($sql_saida, [
                    "Transferência para conta $conta_destino - $descricao", 
                    $valor, $observacoes, $_SESSION['usuario_id']
                ]);

                // Registra entrada na conta destino
                $sql_entrada = "INSERT INTO transacoes_financeiras 
                               (tipo, descricao, valor, data_transacao, forma_pagamento, 
                                observacoes, usuario_id) 
                               VALUES ('receita', ?, ?, NOW(), 'transferencia', ?, ?)";
                $db->execute($sql_entrada, [
                    "Transferência da conta $conta_origem - $descricao", 
                    $valor, $observacoes, $_SESSION['usuario_id']
                ]);

                $mensagem = "Transferência registrada com sucesso!";
                $tipo_mensagem = 'success';
                $acao = 'dashboard';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Busca dados para o dashboard
if ($acao === 'dashboard') {
    // Saldos das contas bancárias
    $sql_saldos = "
        SELECT
            nome as forma_pagamento,
            saldo_inicial as saldo
        FROM contas_bancarias
        WHERE status = 'ativo'
        ORDER BY nome
    ";
    $saldos = $db->fetchAll($sql_saldos);

    // Movimentações do dia (contas pagas/recebidas hoje)
    $sql_movimentacoes = "
        (SELECT 'receita' as tipo, descricao, valor, 'Recebimento' as forma_pagamento,
                TIME(COALESCE(data_recebimento, data_vencimento)) as hora
         FROM contas_receber
         WHERE status = 'recebido' AND DATE(COALESCE(data_recebimento, data_vencimento)) = CURDATE()
         ORDER BY data_recebimento DESC
         LIMIT 10)
        UNION ALL
        (SELECT 'despesa' as tipo, descricao, valor, 'Pagamento' as forma_pagamento,
                TIME(COALESCE(data_pagamento, data_vencimento)) as hora
         FROM contas_pagar
         WHERE status = 'pago' AND DATE(COALESCE(data_pagamento, data_vencimento)) = CURDATE()
         ORDER BY data_pagamento DESC
         LIMIT 10)
        ORDER BY hora DESC
        LIMIT 20
    ";
    $movimentacoes_hoje = $db->fetchAll($sql_movimentacoes);

    // Resumo do mês
    $sql_resumo = "
        SELECT 
            SUM(CASE WHEN tipo = 'receita' THEN valor ELSE 0 END) as total_receitas,
            SUM(CASE WHEN tipo = 'despesa' THEN valor ELSE 0 END) as total_despesas,
            COUNT(CASE WHEN tipo = 'receita' THEN 1 END) as qtd_receitas,
            COUNT(CASE WHEN tipo = 'despesa' THEN 1 END) as qtd_despesas
        FROM transacoes_financeiras 
        WHERE MONTH(data_transacao) = MONTH(CURDATE()) 
        AND YEAR(data_transacao) = YEAR(CURDATE())
    ";
    $resumo_mes = $db->fetchOne($sql_resumo);

    // Categorias para formulários
    $categorias = $db->fetchAll("SELECT * FROM categorias_financeiras ORDER BY nome");
}

// Busca dados para extrato
if ($acao === 'extrato') {
    $data_inicio = $_GET['data_inicio'] ?? date('Y-m-01');
    $data_fim = $_GET['data_fim'] ?? date('Y-m-d');
    $forma_pagamento = $_GET['forma_pagamento'] ?? '';

    $where_clauses = ["DATE(data_transacao) BETWEEN ? AND ?"];
    $params = [$data_inicio, $data_fim];

    if ($forma_pagamento) {
        $where_clauses[] = "forma_pagamento = ?";
        $params[] = $forma_pagamento;
    }

    $where_sql = implode(' AND ', $where_clauses);

    $sql_extrato = "
        SELECT tipo, descricao, valor, forma_pagamento, data_transacao,
               @saldo := @saldo + (CASE WHEN tipo = 'receita' THEN valor ELSE -valor END) as saldo_acumulado
        FROM transacoes_financeiras, (SELECT @saldo := 0) as s
        WHERE $where_sql
        ORDER BY data_transacao ASC, id ASC
    ";
    
    $extrato = $db->fetchAll($sql_extrato, $params);
}

$titulo_pagina = "Tesouraria";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-university text-blue-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Controle de caixa e contas bancárias</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="tesouraria.php?acao=dashboard" class="<?php echo $acao === 'dashboard' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?> px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            Dashboard
                        </a>
                        <a href="tesouraria.php?acao=extrato" class="<?php echo $acao === 'extrato' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?> px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-list mr-2"></i>
                            Extrato
                        </a>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($mensagem); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Inclui a view correspondente
                switch ($acao) {
                    case 'dashboard':
                        include 'views/tesouraria/dashboard.php';
                        break;
                    case 'extrato':
                        include 'views/tesouraria/extrato.php';
                        break;
                    case 'movimentacao':
                        include 'views/tesouraria/movimentacao.php';
                        break;
                    case 'transferencia':
                        include 'views/tesouraria/transferencia.php';
                        break;
                    default:
                        include 'views/tesouraria/dashboard.php';
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../secretaria/js/layout-fixes.js"></script>
    <script>
        // Formatação de valores monetários
        function formatarMoeda(input) {
            let valor = input.value.replace(/\D/g, '');
            valor = (valor / 100).toFixed(2) + '';
            valor = valor.replace(".", ",");
            valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
            input.value = valor;
        }

        // Auto-submit do formulário de filtros
        document.addEventListener('DOMContentLoaded', function() {
            const filtros = document.querySelectorAll('.filtro-auto');
            filtros.forEach(filtro => {
                filtro.addEventListener('change', function() {
                    document.getElementById('form-filtros').submit();
                });
            });
        });
    </script>
</body>
</html>
