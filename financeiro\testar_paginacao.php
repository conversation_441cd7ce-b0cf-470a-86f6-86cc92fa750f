<?php
/**
 * Script para testar a paginação das categorias
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

$db = Database::getInstance();

echo "<h1>🧪 Teste da Paginação de Categorias</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .info { color: blue; }
</style>";

try {
    echo "<h2>1. Verificando quantidade atual de categorias</h2>";
    
    $total_categorias = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras")['total'];
    echo "<p class='info'>📊 Total de categorias existentes: <strong>$total_categorias</strong></p>";
    
    if ($total_categorias < 15) {
        echo "<h2>2. Inserindo categorias de teste para paginação</h2>";
        
        $categorias_teste = [
            ['nome' => 'Mensalidades Ensino Fundamental', 'tipo' => 'receita', 'descricao' => 'Receitas de mensalidades do ensino fundamental'],
            ['nome' => 'Mensalidades Ensino Médio', 'tipo' => 'receita', 'descricao' => 'Receitas de mensalidades do ensino médio'],
            ['nome' => 'Mensalidades Pré-Vestibular', 'tipo' => 'receita', 'descricao' => 'Receitas de mensalidades do pré-vestibular'],
            ['nome' => 'Taxas de Matrícula', 'tipo' => 'receita', 'descricao' => 'Receitas de taxas de matrícula'],
            ['nome' => 'Taxas de Rematrícula', 'tipo' => 'receita', 'descricao' => 'Receitas de taxas de rematrícula'],
            ['nome' => 'Cursos Extracurriculares', 'tipo' => 'receita', 'descricao' => 'Receitas de cursos extras'],
            ['nome' => 'Eventos e Excursões', 'tipo' => 'receita', 'descricao' => 'Receitas de eventos e excursões'],
            ['nome' => 'Material Didático', 'tipo' => 'receita', 'descricao' => 'Venda de material didático'],
            ['nome' => 'Uniformes Escolares', 'tipo' => 'receita', 'descricao' => 'Venda de uniformes'],
            ['nome' => 'Salários Professores', 'tipo' => 'despesa', 'descricao' => 'Pagamento de salários dos professores'],
            ['nome' => 'Salários Funcionários', 'tipo' => 'despesa', 'descricao' => 'Pagamento de salários dos funcionários'],
            ['nome' => 'Energia Elétrica', 'tipo' => 'despesa', 'descricao' => 'Conta de energia elétrica'],
            ['nome' => 'Água e Esgoto', 'tipo' => 'despesa', 'descricao' => 'Conta de água e esgoto'],
            ['nome' => 'Internet e Telefone', 'tipo' => 'despesa', 'descricao' => 'Contas de internet e telefone'],
            ['nome' => 'Material de Limpeza', 'tipo' => 'despesa', 'descricao' => 'Compra de material de limpeza'],
            ['nome' => 'Material de Escritório', 'tipo' => 'despesa', 'descricao' => 'Compra de material de escritório'],
            ['nome' => 'Manutenção Predial', 'tipo' => 'despesa', 'descricao' => 'Gastos com manutenção do prédio'],
            ['nome' => 'Segurança', 'tipo' => 'despesa', 'descricao' => 'Gastos com segurança'],
            ['nome' => 'Marketing e Publicidade', 'tipo' => 'despesa', 'descricao' => 'Gastos com marketing'],
            ['nome' => 'Impostos e Taxas', 'tipo' => 'despesa', 'descricao' => 'Pagamento de impostos e taxas']
        ];
        
        $inseridas = 0;
        foreach ($categorias_teste as $categoria) {
            $existe = $db->fetchOne("SELECT id FROM categorias_financeiras WHERE nome = ?", [$categoria['nome']]);
            if (!$existe) {
                $sql = "INSERT INTO categorias_financeiras (nome, tipo, descricao, status, created_at) VALUES (?, ?, ?, 'ativo', NOW())";
                $db->query($sql, [$categoria['nome'], $categoria['tipo'], $categoria['descricao']]);
                $inseridas++;
                echo "<p class='ok'>✅ Categoria '{$categoria['nome']}' inserida</p>";
            }
        }
        
        echo "<p class='info'>📊 Total de categorias inseridas: <strong>$inseridas</strong></p>";
        
        // Atualizar total
        $total_categorias = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras")['total'];
        echo "<p class='info'>📊 Novo total de categorias: <strong>$total_categorias</strong></p>";
    }
    
    echo "<h2>3. Testando consultas de paginação</h2>";
    
    $limit = 10;
    $total_paginas = ceil($total_categorias / $limit);
    
    echo "<p class='info'>📄 Com $limit itens por página, teremos <strong>$total_paginas páginas</strong></p>";
    
    // Testar primeira página
    $sql_page1 = "SELECT * FROM categorias_financeiras ORDER BY nome LIMIT $limit OFFSET 0";
    $page1 = $db->fetchAll($sql_page1);
    echo "<p class='ok'>✅ Página 1: " . count($page1) . " registros</p>";
    
    // Testar segunda página se existir
    if ($total_paginas > 1) {
        $sql_page2 = "SELECT * FROM categorias_financeiras ORDER BY nome LIMIT $limit OFFSET $limit";
        $page2 = $db->fetchAll($sql_page2);
        echo "<p class='ok'>✅ Página 2: " . count($page2) . " registros</p>";
    }
    
    echo "<h2>4. Testando filtros</h2>";
    
    // Testar filtro por tipo
    $receitas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE tipo = 'receita'")['total'];
    $despesas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE tipo = 'despesa'")['total'];
    
    echo "<p class='ok'>✅ Filtro por tipo:</p>";
    echo "<ul>";
    echo "<li>Receitas: <strong>$receitas</strong></li>";
    echo "<li>Despesas: <strong>$despesas</strong></li>";
    echo "</ul>";
    
    // Testar filtro por status
    $ativas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE status = 'ativo'")['total'];
    $inativas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE status = 'inativo'")['total'];
    
    echo "<p class='ok'>✅ Filtro por status:</p>";
    echo "<ul>";
    echo "<li>Ativas: <strong>$ativas</strong></li>";
    echo "<li>Inativas: <strong>$inativas</strong></li>";
    echo "</ul>";
    
    // Testar busca
    $busca_test = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE nome LIKE '%mensalidade%' OR descricao LIKE '%mensalidade%'")['total'];
    echo "<p class='ok'>✅ Busca por 'mensalidade': <strong>$busca_test</strong> resultados</p>";
    
    echo "<h2>5. Links para testar a interface</h2>";
    
    echo "<ul>";
    echo "<li><a href='configuracoes.php?acao=categorias' target='_blank'>📄 Página 1 (padrão)</a></li>";
    if ($total_paginas > 1) {
        echo "<li><a href='configuracoes.php?acao=categorias&page=2' target='_blank'>📄 Página 2</a></li>";
    }
    echo "<li><a href='configuracoes.php?acao=categorias&filtro_tipo=receita' target='_blank'>💰 Filtrar por Receitas</a></li>";
    echo "<li><a href='configuracoes.php?acao=categorias&filtro_tipo=despesa' target='_blank'>💸 Filtrar por Despesas</a></li>";
    echo "<li><a href='configuracoes.php?acao=categorias&busca=mensalidade' target='_blank'>🔍 Buscar por 'mensalidade'</a></li>";
    echo "<li><a href='configuracoes.php?acao=categorias&filtro_tipo=receita&page=1' target='_blank'>📄 Receitas - Página 1</a></li>";
    echo "</ul>";
    
    echo "<h2>🎉 Teste Concluído!</h2>";
    echo "<p class='ok'>✅ A paginação está pronta para ser testada.</p>";
    echo "<p class='info'>ℹ️ Use os links acima para testar diferentes cenários de paginação e filtros.</p>";

} catch (Exception $e) {
    echo "<h2>❌ ERRO!</h2>";
    echo "<p class='erro'>Erro: " . $e->getMessage() . "</p>";
    echo "<p class='erro'>Arquivo: " . $e->getFile() . "</p>";
    echo "<p class='erro'>Linha: " . $e->getLine() . "</p>";
}
?>
