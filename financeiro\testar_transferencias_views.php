<?php
/**
 * Script para testar se as views de transferências estão funcionando
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

$db = Database::getInstance();

echo "<h1>🧪 Teste das Views de Transferências</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .info { color: blue; }
</style>";

try {
    echo "<h2>1. Verificando arquivos das views</h2>";
    
    $views = [
        'views/transferencias/listar.php',
        'views/transferencias/formulario.php',
        'views/transferencias/visualizar.php'
    ];
    
    foreach ($views as $view) {
        if (file_exists($view)) {
            echo "<p class='ok'>✅ $view existe</p>";
        } else {
            echo "<p class='erro'>❌ $view não encontrado</p>";
        }
    }

    echo "<h2>2. Verificando se a tabela de transferências existe</h2>";
    
    try {
        $db->fetchOne("SELECT 1 FROM transferencias_bancarias LIMIT 1");
        echo "<p class='ok'>✅ Tabela transferencias_bancarias existe</p>";
        
        $total = $db->fetchOne("SELECT COUNT(*) as total FROM transferencias_bancarias")['total'];
        echo "<p class='info'>ℹ️ Total de transferências: $total</p>";
        
    } catch (Exception $e) {
        echo "<p class='erro'>❌ Tabela transferencias_bancarias não existe</p>";
        echo "<p class='info'>ℹ️ Execute o script de instalação: <a href='instalar_transferencias_simples.php'>instalar_transferencias_simples.php</a></p>";
    }

    echo "<h2>3. Verificando contas bancárias</h2>";
    
    $contas = $db->fetchAll("SELECT id, nome, banco FROM contas_bancarias WHERE status = 'ativo'");
    echo "<p class='info'>ℹ️ Contas bancárias ativas: " . count($contas) . "</p>";
    
    if (count($contas) > 0) {
        echo "<ul>";
        foreach ($contas as $conta) {
            echo "<li>{$conta['nome']} - {$conta['banco']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p class='erro'>❌ Nenhuma conta bancária ativa encontrada</p>";
        echo "<p class='info'>ℹ️ Crie contas bancárias em: <a href='contas_bancarias.php'>contas_bancarias.php</a></p>";
    }

    echo "<h2>4. Verificando categorias financeiras</h2>";
    
    $categorias = $db->fetchAll("SELECT id, nome, tipo FROM categorias_financeiras WHERE status = 'ativo'");
    echo "<p class='info'>ℹ️ Categorias ativas: " . count($categorias) . "</p>";
    
    if (count($categorias) > 0) {
        echo "<ul>";
        foreach (array_slice($categorias, 0, 5) as $categoria) {
            echo "<li>{$categoria['nome']} ({$categoria['tipo']})</li>";
        }
        if (count($categorias) > 5) {
            echo "<li>... e mais " . (count($categorias) - 5) . " categorias</li>";
        }
        echo "</ul>";
    }

    echo "<h2>5. Links para testar as páginas</h2>";
    
    echo "<ul>";
    echo "<li><a href='transferencias.php' target='_blank'>📋 Lista de Transferências</a></li>";
    echo "<li><a href='transferencias.php?acao=nova' target='_blank'>➕ Nova Transferência</a></li>";
    
    // Se há transferências, mostrar link para visualizar
    try {
        $primeira_transferencia = $db->fetchOne("SELECT id FROM transferencias_bancarias ORDER BY id DESC LIMIT 1");
        if ($primeira_transferencia) {
            echo "<li><a href='transferencias.php?acao=visualizar&id={$primeira_transferencia['id']}' target='_blank'>👁️ Visualizar Transferência</a></li>";
            echo "<li><a href='transferencias.php?acao=editar&id={$primeira_transferencia['id']}' target='_blank'>✏️ Editar Transferência</a></li>";
        }
    } catch (Exception $e) {
        // Tabela não existe
    }
    
    echo "</ul>";

    echo "<h2>6. Verificando includes necessários</h2>";
    
    $includes = [
        'includes/AsaasAPI.php'
    ];
    
    foreach ($includes as $include) {
        if (file_exists($include)) {
            echo "<p class='ok'>✅ $include existe</p>";
        } else {
            echo "<p class='erro'>❌ $include não encontrado</p>";
        }
    }

    echo "<h2>🎉 Teste Concluído!</h2>";
    
    $problemas = [];
    
    // Verificar problemas
    foreach ($views as $view) {
        if (!file_exists($view)) {
            $problemas[] = "View $view não encontrada";
        }
    }
    
    try {
        $db->fetchOne("SELECT 1 FROM transferencias_bancarias LIMIT 1");
    } catch (Exception $e) {
        $problemas[] = "Tabela transferencias_bancarias não existe";
    }
    
    if (count($contas) == 0) {
        $problemas[] = "Nenhuma conta bancária ativa";
    }
    
    if (empty($problemas)) {
        echo "<p class='ok'>✅ <strong>Tudo funcionando perfeitamente!</strong></p>";
        echo "<p class='ok'>✅ O módulo de transferências está pronto para uso</p>";
        
        echo "<h3>🚀 Como usar:</h3>";
        echo "<ol>";
        echo "<li><strong>Acesse a lista:</strong> <a href='transferencias.php'>transferencias.php</a></li>";
        echo "<li><strong>Crie uma nova:</strong> Clique em 'Nova Transferência'</li>";
        echo "<li><strong>Preencha os dados:</strong> Tipo, valor, contas, etc.</li>";
        echo "<li><strong>Salve:</strong> A transferência será registrada e os saldos atualizados</li>";
        echo "</ol>";
        
    } else {
        echo "<p class='erro'>❌ <strong>Problemas encontrados:</strong></p>";
        echo "<ul>";
        foreach ($problemas as $problema) {
            echo "<li class='erro'>$problema</li>";
        }
        echo "</ul>";
        
        echo "<h3>🔧 Como resolver:</h3>";
        echo "<ol>";
        if (in_array("Tabela transferencias_bancarias não existe", $problemas)) {
            echo "<li><a href='instalar_transferencias_simples.php'>Execute a instalação das transferências</a></li>";
        }
        if (in_array("Nenhuma conta bancária ativa", $problemas)) {
            echo "<li><a href='contas_bancarias.php?acao=nova'>Crie pelo menos uma conta bancária</a></li>";
        }
        echo "</ol>";
    }

} catch (Exception $e) {
    echo "<h2>❌ ERRO!</h2>";
    echo "<p class='erro'>Erro: " . $e->getMessage() . "</p>";
    echo "<p class='erro'>Arquivo: " . $e->getFile() . "</p>";
    echo "<p class='erro'>Linha: " . $e->getLine() . "</p>";
}
?>
