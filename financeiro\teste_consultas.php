<?php
/**
 * Teste específico das consultas do dashboard
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

$db = Database::getInstance();

echo "<h1>🔍 Teste das Consultas do Dashboard</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .ok { color: green; }
    .erro { color: red; }
    .aviso { color: orange; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>";

echo "<h2>📊 Teste Individual de Cada Consulta</h2>";

// Teste 1: Contas a Pagar Pendentes
echo "<h3>1. Contas a Pagar Pendentes</h3>";
try {
    $sql = "SELECT COUNT(*) as total FROM contas_pagar WHERE status = 'pendente'";
    echo "<pre>SQL: $sql</pre>";
    $resultado = $db->fetchOne($sql);
    echo "<p class='ok'>✅ Resultado: {$resultado['total']} contas pendentes</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

// Teste 2: Total a Pagar
echo "<h3>2. Total a Pagar</h3>";
try {
    $sql = "SELECT COALESCE(SUM(valor), 0) as total FROM contas_pagar WHERE status = 'pendente'";
    echo "<pre>SQL: $sql</pre>";
    $resultado = $db->fetchOne($sql);
    echo "<p class='ok'>✅ Resultado: R$ " . number_format($resultado['total'], 2, ',', '.') . "</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

// Teste 3: Contas a Receber Pendentes
echo "<h3>3. Contas a Receber Pendentes</h3>";
try {
    $sql = "SELECT COUNT(*) as total FROM contas_receber WHERE status = 'pendente'";
    echo "<pre>SQL: $sql</pre>";
    $resultado = $db->fetchOne($sql);
    echo "<p class='ok'>✅ Resultado: {$resultado['total']} contas pendentes</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

// Teste 4: Total a Receber
echo "<h3>4. Total a Receber</h3>";
try {
    $sql = "SELECT COALESCE(SUM(valor), 0) as total FROM contas_receber WHERE status = 'pendente'";
    echo "<pre>SQL: $sql</pre>";
    $resultado = $db->fetchOne($sql);
    echo "<p class='ok'>✅ Resultado: R$ " . number_format($resultado['total'], 2, ',', '.') . "</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

// Teste 5: Saldo Total dos Bancos
echo "<h3>5. Saldo Total dos Bancos</h3>";
try {
    $sql = "SELECT COALESCE(SUM(saldo_inicial), 0) as total FROM contas_bancarias WHERE status = 'ativo'";
    echo "<pre>SQL: $sql</pre>";
    $resultado = $db->fetchOne($sql);
    echo "<p class='ok'>✅ Resultado: R$ " . number_format($resultado['total'], 2, ',', '.') . "</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

// Teste 6: Consulta Completa do Dashboard
echo "<h3>6. Consulta Completa do Dashboard</h3>";
try {
    $sql_indicadores = "
        SELECT 
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente') as contas_pagar_pendentes,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente') as contas_receber_pendentes,
            (SELECT COUNT(*) FROM contas_pagar WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_pagar_vencidas,
            (SELECT COUNT(*) FROM contas_receber WHERE status = 'pendente' AND data_vencimento < CURDATE()) as contas_receber_vencidas,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_pagar WHERE status = 'pendente') as total_pagar,
            (SELECT COALESCE(SUM(valor), 0) FROM contas_receber WHERE status = 'pendente') as total_receber,
            (SELECT COALESCE(SUM(saldo_inicial), 0) FROM contas_bancarias WHERE status = 'ativo') as saldo_total_bancos
    ";
    echo "<pre>SQL: $sql_indicadores</pre>";
    $indicadores = $db->fetchOne($sql_indicadores);
    
    echo "<table>";
    echo "<tr><th>Campo</th><th>Valor</th></tr>";
    echo "<tr><td>contas_pagar_pendentes</td><td>{$indicadores['contas_pagar_pendentes']}</td></tr>";
    echo "<tr><td>contas_receber_pendentes</td><td>{$indicadores['contas_receber_pendentes']}</td></tr>";
    echo "<tr><td>contas_pagar_vencidas</td><td>{$indicadores['contas_pagar_vencidas']}</td></tr>";
    echo "<tr><td>contas_receber_vencidas</td><td>{$indicadores['contas_receber_vencidas']}</td></tr>";
    echo "<tr><td>total_pagar</td><td>R$ " . number_format($indicadores['total_pagar'], 2, ',', '.') . "</td></tr>";
    echo "<tr><td>total_receber</td><td>R$ " . number_format($indicadores['total_receber'], 2, ',', '.') . "</td></tr>";
    echo "<tr><td>saldo_total_bancos</td><td>R$ " . number_format($indicadores['saldo_total_bancos'], 2, ',', '.') . "</td></tr>";
    echo "</table>";
    
    echo "<p class='ok'>✅ Consulta completa executada com sucesso!</p>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro na consulta completa: " . $e->getMessage() . "</p>";
}

// Teste 7: Verificar dados brutos das tabelas
echo "<h3>7. Dados Brutos das Tabelas</h3>";

echo "<h4>Contas a Pagar:</h4>";
try {
    $contas_pagar = $db->fetchAll("SELECT id, descricao, valor, status, data_vencimento FROM contas_pagar");
    echo "<table>";
    echo "<tr><th>ID</th><th>Descrição</th><th>Valor</th><th>Status</th><th>Vencimento</th></tr>";
    foreach ($contas_pagar as $conta) {
        echo "<tr><td>{$conta['id']}</td><td>{$conta['descricao']}</td><td>R$ " . number_format($conta['valor'], 2, ',', '.') . "</td><td>{$conta['status']}</td><td>" . date('d/m/Y', strtotime($conta['data_vencimento'])) . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<h4>Contas a Receber:</h4>";
try {
    $contas_receber = $db->fetchAll("SELECT id, descricao, valor, status, data_vencimento FROM contas_receber");
    echo "<table>";
    echo "<tr><th>ID</th><th>Descrição</th><th>Valor</th><th>Status</th><th>Vencimento</th></tr>";
    foreach ($contas_receber as $conta) {
        echo "<tr><td>{$conta['id']}</td><td>{$conta['descricao']}</td><td>R$ " . number_format($conta['valor'], 2, ',', '.') . "</td><td>{$conta['status']}</td><td>" . date('d/m/Y', strtotime($conta['data_vencimento'])) . "</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<h4>Contas Bancárias:</h4>";
try {
    $contas_bancarias = $db->fetchAll("SELECT id, nome, banco, saldo_inicial, status FROM contas_bancarias");
    echo "<table>";
    echo "<tr><th>ID</th><th>Nome</th><th>Banco</th><th>Saldo Inicial</th><th>Status</th></tr>";
    foreach ($contas_bancarias as $conta) {
        echo "<tr><td>{$conta['id']}</td><td>{$conta['nome']}</td><td>{$conta['banco']}</td><td>R$ " . number_format($conta['saldo_inicial'], 2, ',', '.') . "</td><td>{$conta['status']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='erro'>❌ Erro: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Voltar para o Dashboard</a></p>";
echo "<p><a href='diagnostico.php'>🔍 Executar Diagnóstico Completo</a></p>";
?>
