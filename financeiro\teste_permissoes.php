<?php
/**
 * Arquivo de teste para verificar permissões
 */

// Inicializa o sistema
require_once '../secretaria/includes/init.php';

// Verifica se o usuário está autenticado
exigirLogin();

echo "<h1>Teste de Permissões - <PERSON><PERSON><PERSON>lo <PERSON>iro</h1>";

echo "<h2>Informações da Sessão:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Teste de Funções:</h2>";

// Testa se as funções existem
$funcoes = ['usuarioTemPermissao', 'exigirLogin', 'exigirPermissao'];

foreach ($funcoes as $funcao) {
    if (function_exists($funcao)) {
        echo "<p style='color: green;'>✅ Função <strong>$funcao</strong> existe</p>";
    } else {
        echo "<p style='color: red;'>❌ Função <strong>$funcao</strong> NÃO existe</p>";
    }
}

echo "<h2>Teste de Permissão Financeiro:</h2>";

if (function_exists('usuarioTemPermissao')) {
    $temPermissao = usuarioTemPermissao('financeiro', 'visualizar');
    if ($temPermissao) {
        echo "<p style='color: green;'>✅ Usuário TEM permissão para o módulo financeiro</p>";
    } else {
        echo "<p style='color: red;'>❌ Usuário NÃO tem permissão para o módulo financeiro</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Não foi possível testar permissões - função não existe</p>";
}

echo "<h2>Informações do Usuário:</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p><strong>ID:</strong> " . $_SESSION['user_id'] . "</p>";
}
if (isset($_SESSION['user_nome'])) {
    echo "<p><strong>Nome:</strong> " . $_SESSION['user_nome'] . "</p>";
}
if (isset($_SESSION['user_tipo'])) {
    echo "<p><strong>Tipo:</strong> " . $_SESSION['user_tipo'] . "</p>";
}
if (isset($_SESSION['user_email'])) {
    echo "<p><strong>Email:</strong> " . $_SESSION['user_email'] . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Voltar para o Dashboard Financeiro</a></p>";
?>
