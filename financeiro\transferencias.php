<?php
/**
 * Módulo de Transferências e Movimentações Bancárias
 */

require_once '../secretaria/includes/init.php';
exigirLogin();

if (!usuarioTemPermissao('financeiro', 'visualizar')) {
    header('Location: ../secretaria/acesso_negado.php');
    exit;
}

$db = Database::getInstance();
$acao = $_GET['acao'] ?? 'listar';
$id = $_GET['id'] ?? null;
$mensagem = '';
$tipo_mensagem = '';

// Processar formulários
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['form_tipo']) {
            case 'transferencia':
                $tipo_operacao = $_POST['tipo_operacao'];
                $conta_origem_id = $_POST['conta_origem_id'] ?? null;
                $conta_destino_id = $_POST['conta_destino_id'] ?? null;
                $valor = (float)$_POST['valor'];
                $descricao = $_POST['descricao'];
                $data_operacao = $_POST['data_operacao'] . ' ' . ($_POST['hora_operacao'] ?? '12:00:00');
                $numero_documento = $_POST['numero_documento'] ?? null;
                $chave_pix = $_POST['chave_pix'] ?? null;
                $banco_destino = $_POST['banco_destino'] ?? null;
                $agencia_destino = $_POST['agencia_destino'] ?? null;
                $conta_destino_numero = $_POST['conta_destino_numero'] ?? null;
                $nome_favorecido = $_POST['nome_favorecido'] ?? null;
                $cpf_cnpj_favorecido = $_POST['cpf_cnpj_favorecido'] ?? null;
                $tarifa = (float)($_POST['tarifa'] ?? 0);
                $categoria_id = $_POST['categoria_id'] ?? null;
                $observacoes = $_POST['observacoes'] ?? null;
                $id = $_POST['id'] ?? null;

                if ($id) {
                    // Atualizar transferência
                    $sql = "UPDATE transferencias_bancarias SET 
                            tipo_operacao = ?, conta_origem_id = ?, conta_destino_id = ?, valor = ?, 
                            descricao = ?, data_operacao = ?, numero_documento = ?, chave_pix = ?, 
                            banco_destino = ?, agencia_destino = ?, conta_destino_numero = ?, 
                            nome_favorecido = ?, cpf_cnpj_favorecido = ?, tarifa = ?, categoria_id = ?, 
                            observacoes = ?, updated_at = NOW() 
                            WHERE id = ?";
                    $params = [$tipo_operacao, $conta_origem_id, $conta_destino_id, $valor, $descricao, 
                              $data_operacao, $numero_documento, $chave_pix, $banco_destino, $agencia_destino, 
                              $conta_destino_numero, $nome_favorecido, $cpf_cnpj_favorecido, $tarifa, 
                              $categoria_id, $observacoes, $id];
                    $db->query($sql, $params);
                    $mensagem = "Transferência atualizada com sucesso!";
                } else {
                    // Inserir nova transferência
                    $sql = "INSERT INTO transferencias_bancarias 
                            (tipo_operacao, conta_origem_id, conta_destino_id, valor, descricao, data_operacao, 
                             numero_documento, chave_pix, banco_destino, agencia_destino, conta_destino_numero, 
                             nome_favorecido, cpf_cnpj_favorecido, tarifa, categoria_id, observacoes, usuario_id, created_at) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                    $params = [$tipo_operacao, $conta_origem_id, $conta_destino_id, $valor, $descricao, 
                              $data_operacao, $numero_documento, $chave_pix, $banco_destino, $agencia_destino, 
                              $conta_destino_numero, $nome_favorecido, $cpf_cnpj_favorecido, $tarifa, 
                              $categoria_id, $observacoes, $_SESSION['user_id']];
                    $db->query($sql, $params);
                    
                    // Atualizar saldos das contas
                    atualizarSaldosConta($db, $conta_origem_id, $conta_destino_id, $data_operacao);
                    
                    $mensagem = "Transferência registrada com sucesso!";
                }
                $tipo_mensagem = 'success';
                break;
        }
    } catch (Exception $e) {
        $mensagem = "Erro: " . $e->getMessage();
        $tipo_mensagem = 'error';
    }
}

// Função para atualizar saldos das contas
function atualizarSaldosConta($db, $conta_origem_id, $conta_destino_id, $data_operacao) {
    $data = date('Y-m-d', strtotime($data_operacao));
    
    // Atualizar conta origem se existir
    if ($conta_origem_id) {
        atualizarSaldoDiario($db, $conta_origem_id, $data);
    }
    
    // Atualizar conta destino se existir
    if ($conta_destino_id) {
        atualizarSaldoDiario($db, $conta_destino_id, $data);
    }
}

function atualizarSaldoDiario($db, $conta_id, $data) {
    // Calcular movimentações do dia
    $sql_movimentacoes = "
        SELECT 
            COALESCE(SUM(CASE WHEN conta_destino_id = ? THEN valor ELSE 0 END), 0) as entradas,
            COALESCE(SUM(CASE WHEN conta_origem_id = ? THEN valor + tarifa ELSE 0 END), 0) as saidas
        FROM transferencias_bancarias 
        WHERE DATE(data_operacao) = ? AND status = 'processada'
    ";
    $movimentacoes = $db->fetchOne($sql_movimentacoes, [$conta_id, $conta_id, $data]);
    
    // Buscar saldo inicial da conta
    $conta = $db->fetchOne("SELECT saldo_inicial FROM contas_bancarias WHERE id = ?", [$conta_id]);
    $saldo_inicial = $conta['saldo_inicial'] ?? 0;
    
    // Calcular saldo final
    $saldo_final = $saldo_inicial + $movimentacoes['entradas'] - $movimentacoes['saidas'];
    
    // Inserir ou atualizar registro de saldo
    $sql_saldo = "
        INSERT INTO saldos_contas_bancarias 
        (conta_bancaria_id, data_saldo, saldo_inicial_dia, total_entradas, total_saidas, saldo_final_dia) 
        VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        total_entradas = VALUES(total_entradas),
        total_saidas = VALUES(total_saidas),
        saldo_final_dia = VALUES(saldo_final_dia),
        updated_at = NOW()
    ";
    $db->query($sql_saldo, [$conta_id, $data, $saldo_inicial, $movimentacoes['entradas'], $movimentacoes['saidas'], $saldo_final]);
}

// Buscar dados para listagem
if ($acao === 'listar') {
    // Filtros
    $filtro_tipo = $_GET['filtro_tipo'] ?? '';
    $filtro_conta = $_GET['filtro_conta'] ?? '';
    $data_inicio = $_GET['data_inicio'] ?? date('Y-m-01');
    $data_fim = $_GET['data_fim'] ?? date('Y-m-d');
    $busca = $_GET['busca'] ?? '';
    
    // Paginação
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // Construir WHERE
    $where_clauses = ["DATE(t.data_operacao) BETWEEN ? AND ?"];
    $params = [$data_inicio, $data_fim];
    
    if ($filtro_tipo) {
        $where_clauses[] = "t.tipo_operacao = ?";
        $params[] = $filtro_tipo;
    }
    
    if ($filtro_conta) {
        $where_clauses[] = "(t.conta_origem_id = ? OR t.conta_destino_id = ?)";
        $params[] = $filtro_conta;
        $params[] = $filtro_conta;
    }
    
    if ($busca) {
        $where_clauses[] = "(t.descricao LIKE ? OR t.nome_favorecido LIKE ? OR t.numero_documento LIKE ?)";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
        $params[] = "%$busca%";
    }
    
    $where_sql = 'WHERE ' . implode(' AND ', $where_clauses);
    
    // Contar total
    $sql_count = "SELECT COUNT(*) as total FROM transferencias_bancarias t $where_sql";
    $total_registros = $db->fetchOne($sql_count, $params)['total'];
    $total_paginas = ceil($total_registros / $limit);
    
    // Buscar transferências
    $sql_transferencias = "
        SELECT t.*, 
               co.nome as conta_origem_nome, co.banco as conta_origem_banco,
               cd.nome as conta_destino_nome, cd.banco as conta_destino_banco,
               c.nome as categoria_nome,
               u.nome as usuario_nome
        FROM transferencias_bancarias t
        LEFT JOIN contas_bancarias co ON t.conta_origem_id = co.id
        LEFT JOIN contas_bancarias cd ON t.conta_destino_id = cd.id
        LEFT JOIN categorias_financeiras c ON t.categoria_id = c.id
        LEFT JOIN usuarios u ON t.usuario_id = u.id
        $where_sql
        ORDER BY t.data_operacao DESC, t.id DESC
        LIMIT $limit OFFSET $offset
    ";
    $transferencias = $db->fetchAll($sql_transferencias, $params);
}

// Buscar dados para formulários
$contas_bancarias = $db->fetchAll("SELECT id, nome, banco FROM contas_bancarias WHERE status = 'ativo' ORDER BY nome");
$categorias = $db->fetchAll("SELECT id, nome, tipo FROM categorias_financeiras WHERE status = 'ativo' ORDER BY nome");

// Buscar dados para edição
if (($acao === 'editar' || $acao === 'visualizar') && $id) {
    $transferencia = $db->fetchOne("SELECT * FROM transferencias_bancarias WHERE id = ?", [$id]);
    if (!$transferencia) {
        header('Location: transferencias.php?erro=transferencia_nao_encontrada');
        exit;
    }
}

$titulo_pagina = "Transferências e Movimentações Bancárias";
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Faciência ERP - <?php echo $titulo_pagina; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../secretaria/css/styles.css">
    <link rel="stylesheet" href="../secretaria/css/sidebar.css">
    <link rel="stylesheet" href="../secretaria/css/layout-fixes.css">
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Conteúdo Principal -->
        <div class="main-content flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">
                            <i class="fas fa-exchange-alt text-blue-600 mr-3"></i>
                            <?php echo $titulo_pagina; ?>
                        </h1>
                        <p class="text-gray-600 mt-1">Registre transferências, PIX, TEDs e outras movimentações bancárias</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="?acao=nova" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            Nova Transferência
                        </a>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Mensagens -->
                <?php if ($mensagem): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $tipo_mensagem === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?>">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium"><?php echo $mensagem; ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php
                // Incluir a view correspondente
                switch ($acao) {
                    case 'nova':
                    case 'editar':
                        include 'views/transferencias/formulario.php';
                        break;
                    case 'visualizar':
                        include 'views/transferencias/visualizar.php';
                        break;
                    default:
                        include 'views/transferencias/listar.php';
                }
                ?>
            </main>
        </div>
    </div>
</body>
</html>
