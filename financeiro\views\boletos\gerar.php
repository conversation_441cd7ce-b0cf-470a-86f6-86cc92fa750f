<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-plus text-blue-500 mr-2"></i>
                Gerar Novo Boleto
            </h3>
            <a href="boletos.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="gerar">
        
        <!-- Tipo de Boleto -->
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Tipo de Boleto</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_entidade" value="aluno" class="mr-3" onchange="alterarTipo('aluno')" checked>
                    <div>
                        <div class="font-medium text-gray-900">Boleto para Aluno</div>
                        <div class="text-sm text-gray-500">Gerar boleto para mensalidade ou taxa de aluno</div>
                    </div>
                </label>
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_entidade" value="empresa" class="mr-3" onchange="alterarTipo('empresa')">
                    <div>
                        <div class="font-medium text-gray-900">Boleto Avulso</div>
                        <div class="text-sm text-gray-500">Gerar boleto para empresa ou pessoa física</div>
                    </div>
                </label>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Seleção de Aluno (visível quando tipo = aluno) -->
            <div id="campo-aluno">
                <label for="entidade_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Aluno <span class="text-red-500">*</span>
                </label>
                <select id="entidade_id" name="entidade_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onchange="carregarMensalidades()">
                    <option value="">Selecione o aluno</option>
                    <?php foreach ($alunos as $aluno): ?>
                        <option value="<?php echo $aluno['id']; ?>" data-nome="<?php echo htmlspecialchars($aluno['nome']); ?>">
                            <?php echo htmlspecialchars($aluno['nome']); ?> - <?php echo htmlspecialchars($aluno['cpf']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Mensalidade (visível quando tipo = aluno) -->
            <div id="campo-mensalidade">
                <label for="mensalidade_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Mensalidade (Opcional)
                </label>
                <select id="mensalidade_id" name="mensalidade_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        onchange="preencherValorMensalidade()">
                    <option value="">Selecione uma mensalidade pendente</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">Se não selecionar, será um boleto avulso para o aluno</p>
            </div>

            <!-- Valor -->
            <div>
                <label for="valor" class="block text-sm font-medium text-gray-700 mb-2">
                    Valor <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="valor" name="valor" required
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0,00"
                           onkeyup="formatarMoeda(this)">
                </div>
            </div>

            <!-- Data de Vencimento -->
            <div>
                <label for="data_vencimento" class="block text-sm font-medium text-gray-700 mb-2">
                    Data de Vencimento <span class="text-red-500">*</span>
                </label>
                <input type="date" id="data_vencimento" name="data_vencimento" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Informações adicionais sobre o boleto..."></textarea>
            </div>
        </div>

        <!-- Informações Bancárias -->
        <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 class="text-md font-semibold text-blue-800 mb-2">Informações Bancárias</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
                <div>
                    <span class="font-medium">Banco:</span> Itaú (341)
                </div>
                <div>
                    <span class="font-medium">Agência:</span> 1234
                </div>
                <div>
                    <span class="font-medium">Conta:</span> 12345-6
                </div>
            </div>
            <p class="text-xs text-blue-600 mt-2">
                <i class="fas fa-info-circle mr-1"></i>
                Estas informações são configuradas nas configurações do sistema
            </p>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="boletos.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-barcode mr-2"></i>
                    Gerar Boleto
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Informações sobre Boletos -->
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Informações Importantes</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>O boleto será gerado com as informações bancárias configuradas no sistema</li>
                    <li>Para boletos de mensalidade, o pagamento será automaticamente vinculado</li>
                    <li>Boletos vencidos podem ser pagos normalmente</li>
                    <li>O código de barras e linha digitável são gerados automaticamente</li>
                    <li>Após a geração, o boleto pode ser impresso ou enviado por email</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Dados das mensalidades para JavaScript
const mensalidadesData = <?php echo json_encode($mensalidades); ?>;

function alterarTipo(tipo) {
    const campoAluno = document.getElementById('campo-aluno');
    const campoMensalidade = document.getElementById('campo-mensalidade');
    const entidadeSelect = document.getElementById('entidade_id');
    
    if (tipo === 'aluno') {
        campoAluno.style.display = 'block';
        campoMensalidade.style.display = 'block';
        entidadeSelect.required = true;
    } else {
        campoAluno.style.display = 'none';
        campoMensalidade.style.display = 'none';
        entidadeSelect.required = false;
        entidadeSelect.value = '';
        document.getElementById('mensalidade_id').innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
    }
}

function carregarMensalidades() {
    const alunoId = document.getElementById('entidade_id').value;
    const mensalidadeSelect = document.getElementById('mensalidade_id');
    
    // Limpar opções
    mensalidadeSelect.innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
    
    if (alunoId) {
        // Filtrar mensalidades do aluno selecionado
        const mensalidadesAluno = mensalidadesData.filter(m => m.aluno_nome === document.querySelector(`#entidade_id option[value="${alunoId}"]`).dataset.nome);
        
        mensalidadesAluno.forEach(mensalidade => {
            const option = document.createElement('option');
            option.value = mensalidade.id;
            option.textContent = `${mensalidade.descricao} - R$ ${parseFloat(mensalidade.valor).toFixed(2).replace('.', ',')}`;
            option.dataset.valor = mensalidade.valor;
            mensalidadeSelect.appendChild(option);
        });
    }
}

function preencherValorMensalidade() {
    const mensalidadeSelect = document.getElementById('mensalidade_id');
    const valorInput = document.getElementById('valor');
    
    if (mensalidadeSelect.value) {
        const selectedOption = mensalidadeSelect.options[mensalidadeSelect.selectedIndex];
        const valor = parseFloat(selectedOption.dataset.valor);
        valorInput.value = valor.toFixed(2).replace('.', ',');
    }
}

function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.querySelector('input[name="tipo_entidade"][value="aluno"]').checked = true;
        alterarTipo('aluno');
        document.getElementById('entidade_id').value = '';
        document.getElementById('mensalidade_id').innerHTML = '<option value="">Selecione uma mensalidade pendente</option>';
        document.getElementById('valor').value = '';
        document.getElementById('data_vencimento').value = '';
        document.getElementById('observacoes').value = '';
    }
}

// Definir data de vencimento padrão (30 dias)
document.addEventListener('DOMContentLoaded', function() {
    const hoje = new Date();
    hoje.setDate(hoje.getDate() + 30);
    document.getElementById('data_vencimento').value = hoje.toISOString().split('T')[0];
});
</script>
