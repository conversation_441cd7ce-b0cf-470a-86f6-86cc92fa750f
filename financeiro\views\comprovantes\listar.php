<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <option value="pendente" <?php echo ($_GET['status'] ?? '') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                <option value="aprovado" <?php echo ($_GET['status'] ?? '') === 'aprovado' ? 'selected' : ''; ?>>Aprovado</option>
                <option value="rejeitado" <?php echo ($_GET['status'] ?? '') === 'rejeitado' ? 'selected' : ''; ?>>Rejeitado</option>
            </select>
        </div>

        <!-- Tipo -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
            <select name="tipo_transacao" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <option value="conta_pagar" <?php echo ($_GET['tipo_transacao'] ?? '') === 'conta_pagar' ? 'selected' : ''; ?>>Conta a Pagar</option>
                <option value="conta_receber" <?php echo ($_GET['tipo_transacao'] ?? '') === 'conta_receber' ? 'selected' : ''; ?>>Conta a Receber</option>
                <option value="mensalidade" <?php echo ($_GET['tipo_transacao'] ?? '') === 'mensalidade' ? 'selected' : ''; ?>>Mensalidade</option>
            </select>
        </div>

        <!-- Data Início -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($_GET['data_inicio'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
        </div>

        <!-- Data Fim -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo htmlspecialchars($_GET['data_fim'] ?? ''); ?>" 
                   class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
        </div>

        <!-- Botão Filtrar -->
        <div class="flex items-end">
            <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filtrar
            </button>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_comprovantes = count($comprovantes);
$pendentes = 0;
$aprovados = 0;
$rejeitados = 0;

foreach ($comprovantes as $comprovante) {
    switch ($comprovante['status']) {
        case 'pendente':
            $pendentes++;
            break;
        case 'aprovado':
            $aprovados++;
            break;
        case 'rejeitado':
            $rejeitados++;
            break;
    }
}
?>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Comprovantes</p>
                <p class="text-2xl font-bold text-purple-600"><?php echo $total_comprovantes; ?></p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-file-upload text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Pendentes -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Pendentes</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo $pendentes; ?></p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Aprovados -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Aprovados</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $aprovados; ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Rejeitados -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Rejeitados</p>
                <p class="text-2xl font-bold text-red-600"><?php echo $rejeitados; ?></p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-times-circle text-red-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Comprovantes -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-purple-500 mr-2"></i>
                Comprovantes (<?php echo count($comprovantes); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($comprovantes)): ?>
            <div class="text-center py-12">
                <i class="fas fa-file-upload text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum comprovante encontrado</h3>
                <p class="text-gray-500 mb-6">Não há comprovantes que correspondam aos filtros aplicados.</p>
                <a href="comprovantes.php?acao=upload" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-upload mr-2"></i>
                    Enviar Primeiro Comprovante
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Arquivo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Envio</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($comprovantes as $comprovante): ?>
                        <?php
                        $status_config = [
                            'pendente' => ['class' => 'bg-orange-100 text-orange-800', 'text' => 'Pendente'],
                            'aprovado' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Aprovado'],
                            'rejeitado' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Rejeitado']
                        ];
                        $status = $status_config[$comprovante['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $comprovante['status']];
                        
                        $tipo_config = [
                            'conta_pagar' => 'Conta a Pagar',
                            'conta_receber' => 'Conta a Receber',
                            'mensalidade' => 'Mensalidade'
                        ];
                        $tipo_texto = $tipo_config[$comprovante['tipo_transacao']] ?? $comprovante['tipo_transacao'];
                        
                        $extensao = strtolower(pathinfo($comprovante['nome_arquivo'], PATHINFO_EXTENSION));
                        $icone_arquivo = in_array($extensao, ['jpg', 'jpeg', 'png', 'gif']) ? 'fa-image' : 
                                        ($extensao === 'pdf' ? 'fa-file-pdf' : 'fa-file');
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="fas <?php echo $icone_arquivo; ?> text-gray-400 text-lg mr-3"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($comprovante['nome_arquivo']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo number_format($comprovante['tamanho_arquivo'] / 1024, 1); ?> KB
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $tipo_texto; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?php echo htmlspecialchars($comprovante['descricao'] ?: 'Sem descrição'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y H:i', strtotime($comprovante['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                                    <?php echo $status['text']; ?>
                                </span>
                                <?php if ($comprovante['status'] === 'aprovado' && $comprovante['data_aprovacao']): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        Aprovado em: <?php echo date('d/m/Y', strtotime($comprovante['data_aprovacao'])); ?>
                                    </div>
                                <?php elseif ($comprovante['status'] === 'rejeitado' && $comprovante['data_rejeicao']): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        Rejeitado em: <?php echo date('d/m/Y', strtotime($comprovante['data_rejeicao'])); ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo htmlspecialchars($comprovante['caminho_arquivo']); ?>" target="_blank"
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    
                                    <?php if ($comprovante['status'] === 'pendente'): ?>
                                        <button onclick="aprovarComprovante(<?php echo $comprovante['id']; ?>)" 
                                                class="text-green-600 hover:text-green-900" title="Aprovar">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button onclick="rejeitarComprovante(<?php echo $comprovante['id']; ?>)" 
                                                class="text-red-600 hover:text-red-900" title="Rejeitar">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo htmlspecialchars($comprovante['caminho_arquivo']); ?>" download
                                       class="text-purple-600 hover:text-purple-900" title="Download">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    
                                    <button onclick="excluirComprovante(<?php echo $comprovante['id']; ?>)" 
                                            class="text-red-600 hover:text-red-900" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-submit quando mudar filtros
document.addEventListener('DOMContentLoaded', function() {
    const filtros = document.querySelectorAll('.filtro-auto');
    filtros.forEach(filtro => {
        filtro.addEventListener('change', function() {
            document.getElementById('form-filtros').submit();
        });
    });
});
</script>
