<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-upload text-purple-500 mr-2"></i>
                Enviar Comprovante
            </h3>
            <a href="comprovantes.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" enctype="multipart/form-data" class="p-6">
        <input type="hidden" name="acao" value="upload">
        
        <!-- Tipo de Transação -->
        <div class="mb-6">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Tipo de Transação</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_transacao" value="conta_pagar" class="mr-3" onchange="carregarTransacoes('conta_pagar')" checked>
                    <div>
                        <div class="font-medium text-gray-900">Conta a Pagar</div>
                        <div class="text-sm text-gray-500">Comprovante de pagamento a fornecedor</div>
                    </div>
                </label>
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_transacao" value="conta_receber" class="mr-3" onchange="carregarTransacoes('conta_receber')">
                    <div>
                        <div class="font-medium text-gray-900">Conta a Receber</div>
                        <div class="text-sm text-gray-500">Comprovante de recebimento</div>
                    </div>
                </label>
                <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="radio" name="tipo_transacao" value="mensalidade" class="mr-3" onchange="carregarTransacoes('mensalidade')">
                    <div>
                        <div class="font-medium text-gray-900">Mensalidade</div>
                        <div class="text-sm text-gray-500">Comprovante de pagamento de mensalidade</div>
                    </div>
                </label>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Transação Relacionada -->
            <div>
                <label for="transacao_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Transação Relacionada (Opcional)
                </label>
                <select id="transacao_id" name="transacao_id"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <option value="">Selecione uma transação</option>
                </select>
                <p class="text-xs text-gray-500 mt-1">Vincule o comprovante a uma transação específica</p>
            </div>

            <!-- Arquivo -->
            <div>
                <label for="arquivo" class="block text-sm font-medium text-gray-700 mb-2">
                    Arquivo do Comprovante <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <input type="file" id="arquivo" name="arquivo" required accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                           onchange="mostrarPreview(this)">
                </div>
                <p class="text-xs text-gray-500 mt-1">
                    Formatos aceitos: JPG, PNG, PDF, DOC, DOCX (máx. 10MB)
                </p>
            </div>

            <!-- Descrição -->
            <div class="md:col-span-2">
                <label for="descricao" class="block text-sm font-medium text-gray-700 mb-2">
                    Descrição
                </label>
                <textarea id="descricao" name="descricao" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                          placeholder="Descreva o comprovante ou adicione observações..."></textarea>
            </div>
        </div>

        <!-- Preview do Arquivo -->
        <div id="preview-container" class="mt-6 hidden">
            <h4 class="text-md font-semibold text-gray-800 mb-3">Preview do Arquivo</h4>
            <div id="preview-content" class="p-4 border border-gray-300 rounded-lg bg-gray-50">
                <!-- Preview será inserido aqui via JavaScript -->
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="comprovantes.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-upload mr-2"></i>
                    Enviar Comprovante
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Informações sobre Upload -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Informações sobre Upload de Comprovantes</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li><strong>Formatos aceitos:</strong> JPG, JPEG, PNG, PDF, DOC, DOCX</li>
                    <li><strong>Tamanho máximo:</strong> 10MB por arquivo</li>
                    <li><strong>Qualidade:</strong> Use imagens nítidas e legíveis</li>
                    <li><strong>Organização:</strong> Vincule sempre que possível a uma transação específica</li>
                    <li><strong>Aprovação:</strong> Comprovantes passam por processo de validação</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Dicas de Boas Práticas -->
<div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-lightbulb text-green-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Dicas para Melhores Comprovantes</h3>
            <div class="mt-2 text-sm text-green-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Fotografe ou escaneie documentos com boa iluminação</li>
                    <li>Certifique-se de que todas as informações estão visíveis</li>
                    <li>Para PDFs, prefira arquivos originais ao invés de fotos</li>
                    <li>Inclua sempre uma descrição clara do que se trata</li>
                    <li>Organize por data e tipo para facilitar a localização</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Dados das transações para JavaScript
const transacoesPagar = <?php echo json_encode($transacoes_pagar ?? []); ?>;
const transacoesReceber = <?php echo json_encode($transacoes_receber ?? []); ?>;

function carregarTransacoes(tipo) {
    const select = document.getElementById('transacao_id');
    select.innerHTML = '<option value="">Selecione uma transação</option>';
    
    let transacoes = [];
    
    if (tipo === 'conta_pagar') {
        transacoes = transacoesPagar;
    } else if (tipo === 'conta_receber') {
        transacoes = transacoesReceber;
    }
    
    transacoes.forEach(transacao => {
        const option = document.createElement('option');
        option.value = transacao.id;
        
        if (tipo === 'conta_pagar') {
            option.textContent = `${transacao.descricao} - ${transacao.fornecedor_nome || 'Sem fornecedor'} - R$ ${parseFloat(transacao.valor).toFixed(2).replace('.', ',')}`;
        } else {
            option.textContent = `${transacao.descricao} - ${transacao.cliente_nome || 'Sem cliente'} - R$ ${parseFloat(transacao.valor).toFixed(2).replace('.', ',')}`;
        }
        
        select.appendChild(option);
    });
}

function mostrarPreview(input) {
    const file = input.files[0];
    const container = document.getElementById('preview-container');
    const content = document.getElementById('preview-content');
    
    if (file) {
        container.classList.remove('hidden');
        
        const fileSize = (file.size / 1024 / 1024).toFixed(2);
        const fileName = file.name;
        const fileType = file.type;
        
        let previewHTML = `
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
        `;
        
        if (fileType.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewHTML += `<img src="${e.target.result}" alt="Preview" class="w-20 h-20 object-cover rounded-lg border">`;
                previewHTML += `
                    </div>
                    <div>
                        <p class="font-medium text-gray-900">${fileName}</p>
                        <p class="text-sm text-gray-500">Tamanho: ${fileSize} MB</p>
                        <p class="text-sm text-gray-500">Tipo: Imagem</p>
                    </div>
                </div>
                `;
                content.innerHTML = previewHTML;
            };
            reader.readAsDataURL(file);
        } else {
            const icon = fileType.includes('pdf') ? 'fa-file-pdf' : 'fa-file';
            previewHTML += `<i class="fas ${icon} text-4xl text-gray-400"></i>`;
            previewHTML += `
                </div>
                <div>
                    <p class="font-medium text-gray-900">${fileName}</p>
                    <p class="text-sm text-gray-500">Tamanho: ${fileSize} MB</p>
                    <p class="text-sm text-gray-500">Tipo: Documento</p>
                </div>
            </div>
            `;
            content.innerHTML = previewHTML;
        }
    } else {
        container.classList.add('hidden');
    }
}

function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.querySelector('input[name="tipo_transacao"][value="conta_pagar"]').checked = true;
        carregarTransacoes('conta_pagar');
        document.getElementById('arquivo').value = '';
        document.getElementById('descricao').value = '';
        document.getElementById('preview-container').classList.add('hidden');
    }
}

// Carregar transações iniciais
document.addEventListener('DOMContentLoaded', function() {
    carregarTransacoes('conta_pagar');
});

// Validação de tamanho de arquivo
document.getElementById('arquivo').addEventListener('change', function() {
    const file = this.files[0];
    if (file && file.size > 10 * 1024 * 1024) { // 10MB
        alert('Arquivo muito grande! O tamanho máximo é 10MB.');
        this.value = '';
        document.getElementById('preview-container').classList.add('hidden');
    }
});
</script>
