<?php
// Buscar dados do comprovante
$comprovante = $db->fetchOne("
    SELECT cp.*, 
           u.nome as usuario_nome
    FROM comprovantes_pagamento cp
    LEFT JOIN usuarios u ON cp.usuario_aprovacao = u.id OR cp.usuario_rejeicao = u.id
    WHERE cp.id = ?
", [$id]);

if (!$comprovante) {
    header('Location: comprovantes.php?erro=comprovante_nao_encontrado');
    exit;
}

$extensao = strtolower(pathinfo($comprovante['nome_arquivo'], PATHINFO_EXTENSION));
$eh_imagem = in_array($extensao, ['jpg', 'jpeg', 'png', 'gif']);
?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-file-upload text-purple-500 mr-2"></i>
                Detalhes do Comprovante
            </h3>
            <div class="flex items-center space-x-2">
                <a href="comprovantes.php" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>
                <a href="<?php echo htmlspecialchars($comprovante['caminho_arquivo']); ?>" download
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-download mr-2"></i>
                    Download
                </a>
            </div>
        </div>
    </div>

    <!-- Informações do Comprovante -->
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados do Arquivo -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados do Arquivo
                </h4>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Nome do Arquivo</label>
                        <p class="text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($comprovante['nome_arquivo']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tamanho</label>
                        <p class="text-sm text-gray-900"><?php echo number_format($comprovante['tamanho_arquivo'] / 1024, 1); ?> KB</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tipo</label>
                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($comprovante['tipo_arquivo']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Data de Envio</label>
                        <p class="text-sm text-gray-900"><?php echo date('d/m/Y H:i', strtotime($comprovante['created_at'])); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tipo de Transação</label>
                        <?php
                        $tipo_config = [
                            'conta_pagar' => 'Conta a Pagar',
                            'conta_receber' => 'Conta a Receber',
                            'mensalidade' => 'Mensalidade'
                        ];
                        $tipo_texto = $tipo_config[$comprovante['tipo_transacao']] ?? $comprovante['tipo_transacao'];
                        ?>
                        <p class="text-sm text-gray-900"><?php echo $tipo_texto; ?></p>
                    </div>
                </div>
            </div>

            <!-- Status e Aprovação -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Status e Aprovação
                </h4>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <?php
                        $status_config = [
                            'pendente' => ['class' => 'bg-orange-100 text-orange-800', 'text' => 'Pendente'],
                            'aprovado' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Aprovado'],
                            'rejeitado' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Rejeitado']
                        ];
                        $status = $status_config[$comprovante['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $comprovante['status']];
                        ?>
                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full <?php echo $status['class']; ?>">
                            <?php echo $status['text']; ?>
                        </span>
                    </div>

                    <?php if ($comprovante['status'] === 'aprovado' && $comprovante['data_aprovacao']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Data de Aprovação</label>
                            <p class="text-sm text-gray-900"><?php echo date('d/m/Y H:i', strtotime($comprovante['data_aprovacao'])); ?></p>
                        </div>
                        <?php if ($comprovante['usuario_nome']): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Aprovado por</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($comprovante['usuario_nome']); ?></p>
                            </div>
                        <?php endif; ?>
                    <?php elseif ($comprovante['status'] === 'rejeitado' && $comprovante['data_rejeicao']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Data de Rejeição</label>
                            <p class="text-sm text-gray-900"><?php echo date('d/m/Y H:i', strtotime($comprovante['data_rejeicao'])); ?></p>
                        </div>
                        <?php if ($comprovante['usuario_nome']): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Rejeitado por</label>
                                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($comprovante['usuario_nome']); ?></p>
                            </div>
                        <?php endif; ?>
                        <?php if ($comprovante['motivo_rejeicao']): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Motivo da Rejeição</label>
                                <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p class="text-sm text-red-700"><?php echo htmlspecialchars($comprovante['motivo_rejeicao']); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Descrição -->
        <?php if ($comprovante['descricao']): ?>
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($comprovante['descricao'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Preview do Arquivo -->
        <div class="mt-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">Preview do Arquivo</label>
            
            <?php if ($eh_imagem): ?>
                <!-- Preview de Imagem -->
                <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                    <img src="<?php echo htmlspecialchars($comprovante['caminho_arquivo']); ?>" 
                         alt="Preview do comprovante" 
                         class="max-w-full h-auto max-h-96 mx-auto rounded-lg shadow-sm">
                </div>
            <?php else: ?>
                <!-- Preview de Documento -->
                <div class="border border-gray-300 rounded-lg p-8 bg-gray-50 text-center">
                    <?php
                    $icone_arquivo = $extensao === 'pdf' ? 'fa-file-pdf text-red-500' : 'fa-file text-gray-500';
                    ?>
                    <i class="fas <?php echo $icone_arquivo; ?> text-6xl mb-4"></i>
                    <p class="text-lg font-medium text-gray-900 mb-2"><?php echo htmlspecialchars($comprovante['nome_arquivo']); ?></p>
                    <p class="text-sm text-gray-600 mb-4">Clique no botão abaixo para visualizar o arquivo</p>
                    <a href="<?php echo htmlspecialchars($comprovante['caminho_arquivo']); ?>" target="_blank"
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Abrir Arquivo
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Ações -->
        <?php if ($comprovante['status'] === 'pendente'): ?>
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <h4 class="text-md font-semibold text-gray-800">Ações de Aprovação</h4>
                    <div class="flex items-center space-x-2">
                        <button onclick="aprovarComprovante(<?php echo $comprovante['id']; ?>)" 
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-check mr-2"></i>
                            Aprovar
                        </button>
                        <button onclick="rejeitarComprovante(<?php echo $comprovante['id']; ?>)" 
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i class="fas fa-times mr-2"></i>
                            Rejeitar
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function aprovarComprovante(id) {
    if (confirm('Tem certeza que deseja aprovar este comprovante?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `comprovantes.php?acao=aprovar&id=${id}`;
        document.body.appendChild(form);
        form.submit();
    }
}

function rejeitarComprovante(id) {
    const motivo = prompt('Motivo da rejeição:');
    if (motivo) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `comprovantes.php?acao=rejeitar&id=${id}`;
        form.innerHTML = `<input type="hidden" name="motivo" value="${motivo}">`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
