<!-- Informações sobre Integração Asaas -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Integração com Asaas</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>Configure aqui suas credenciais da API do Asaas para geração automática de boletos bancários.</p>
                <p class="mt-1"><strong>Importante:</strong> Use o ambiente Sandbox para testes e Produção apenas quando tiver as credenciais reais.</p>
            </div>
        </div>
    </div>
</div>

<!-- Status da Configuração -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Status da API -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Status da API</p>
                <p class="text-lg font-bold <?php echo ($config_asaas && !empty($config_asaas['api_key'])) ? 'text-green-600' : 'text-red-600'; ?>">
                    <?php echo ($config_asaas && !empty($config_asaas['api_key'])) ? 'Configurada' : 'Não Configurada'; ?>
                </p>
            </div>
            <div class="w-12 h-12 <?php echo ($config_asaas && !empty($config_asaas['api_key'])) ? 'bg-green-100' : 'bg-red-100'; ?> rounded-lg flex items-center justify-center">
                <i class="fas fa-key <?php echo ($config_asaas && !empty($config_asaas['api_key'])) ? 'text-green-600' : 'text-red-600'; ?> text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Ambiente -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Ambiente</p>
                <p class="text-lg font-bold <?php echo ($config_asaas && $config_asaas['ambiente'] === 'producao') ? 'text-blue-600' : 'text-orange-600'; ?>">
                    <?php echo $config_asaas ? ucfirst($config_asaas['ambiente']) : 'Sandbox'; ?>
                </p>
            </div>
            <div class="w-12 h-12 <?php echo ($config_asaas && $config_asaas['ambiente'] === 'producao') ? 'bg-blue-100' : 'bg-orange-100'; ?> rounded-lg flex items-center justify-center">
                <i class="fas fa-server <?php echo ($config_asaas && $config_asaas['ambiente'] === 'producao') ? 'text-blue-600' : 'text-orange-600'; ?> text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Status Geral -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Integração</p>
                <p class="text-lg font-bold <?php echo ($config_asaas && $config_asaas['ativo']) ? 'text-green-600' : 'text-gray-600'; ?>">
                    <?php echo ($config_asaas && $config_asaas['ativo']) ? 'Ativa' : 'Inativa'; ?>
                </p>
            </div>
            <div class="w-12 h-12 <?php echo ($config_asaas && $config_asaas['ativo']) ? 'bg-green-100' : 'bg-gray-100'; ?> rounded-lg flex items-center justify-center">
                <i class="fas fa-toggle-<?php echo ($config_asaas && $config_asaas['ativo']) ? 'on' : 'off'; ?> <?php echo ($config_asaas && $config_asaas['ativo']) ? 'text-green-600' : 'text-gray-600'; ?> text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Formulário de Configuração -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-cog text-blue-500 mr-2"></i>
            Configurações da API Asaas
        </h3>
    </div>
    
    <div class="p-6">
        <form method="POST" class="space-y-6">
            <input type="hidden" name="form_tipo" value="asaas">
            
            <!-- Configurações Básicas -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- API Key -->
                <div class="md:col-span-2">
                    <label for="api_key" class="block text-sm font-medium text-gray-700 mb-2">
                        API Key <span class="text-red-500">*</span>
                    </label>
                    <input type="password" id="api_key" name="api_key" 
                           value="<?php echo htmlspecialchars($config_asaas['api_key'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Sua API Key do Asaas">
                    <p class="mt-1 text-xs text-gray-500">Obtenha sua API Key no painel do Asaas em Configurações > Integrações</p>
                </div>

                <!-- Ambiente -->
                <div>
                    <label for="ambiente" class="block text-sm font-medium text-gray-700 mb-2">
                        Ambiente <span class="text-red-500">*</span>
                    </label>
                    <select id="ambiente" name="ambiente" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="sandbox" <?php echo ($config_asaas && $config_asaas['ambiente'] === 'sandbox') ? 'selected' : ''; ?>>Sandbox (Testes)</option>
                        <option value="producao" <?php echo ($config_asaas && $config_asaas['ambiente'] === 'producao') ? 'selected' : ''; ?>>Produção</option>
                    </select>
                </div>

                <!-- Status Ativo -->
                <div class="flex items-center">
                    <input type="checkbox" id="ativo" name="ativo" value="1" 
                           <?php echo ($config_asaas && $config_asaas['ativo']) ? 'checked' : ''; ?>
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="ativo" class="ml-2 block text-sm text-gray-900">
                        Integração Ativa
                    </label>
                </div>
            </div>

            <!-- Configurações de Cobrança -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Configurações de Cobrança</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Multa -->
                    <div>
                        <label for="multa_percentual" class="block text-sm font-medium text-gray-700 mb-2">
                            Multa por Atraso (%)
                        </label>
                        <input type="number" id="multa_percentual" name="multa_percentual" step="0.01" min="0" max="20"
                               value="<?php echo $config_asaas['multa_percentual'] ?? '2.00'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Juros -->
                    <div>
                        <label for="juros_percentual" class="block text-sm font-medium text-gray-700 mb-2">
                            Juros ao Mês (%)
                        </label>
                        <input type="number" id="juros_percentual" name="juros_percentual" step="0.01" min="0" max="20"
                               value="<?php echo $config_asaas['juros_percentual'] ?? '1.00'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Dias Vencimento -->
                    <div>
                        <label for="dias_vencimento_padrao" class="block text-sm font-medium text-gray-700 mb-2">
                            Dias para Vencimento
                        </label>
                        <input type="number" id="dias_vencimento_padrao" name="dias_vencimento_padrao" min="1" max="365"
                               value="<?php echo $config_asaas['dias_vencimento_padrao'] ?? '7'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Configurações de Desconto -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Desconto por Antecipação</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Dias para Desconto -->
                    <div>
                        <label for="desconto_antecipacao_dias" class="block text-sm font-medium text-gray-700 mb-2">
                            Dias de Antecipação
                        </label>
                        <input type="number" id="desconto_antecipacao_dias" name="desconto_antecipacao_dias" min="0" max="30"
                               value="<?php echo $config_asaas['desconto_antecipacao_dias'] ?? '0'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-xs text-gray-500">0 = sem desconto</p>
                    </div>

                    <!-- Percentual de Desconto -->
                    <div>
                        <label for="desconto_antecipacao_percentual" class="block text-sm font-medium text-gray-700 mb-2">
                            Percentual de Desconto (%)
                        </label>
                        <input type="number" id="desconto_antecipacao_percentual" name="desconto_antecipacao_percentual" step="0.01" min="0" max="50"
                               value="<?php echo $config_asaas['desconto_antecipacao_percentual'] ?? '0.00'; ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>

            <!-- Configurações de Webhook -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Webhook (Notificações Automáticas)</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- URL do Webhook -->
                    <div>
                        <label for="webhook_url" class="block text-sm font-medium text-gray-700 mb-2">
                            URL do Webhook
                        </label>
                        <input type="url" id="webhook_url" name="webhook_url"
                               value="<?php echo htmlspecialchars($config_asaas['webhook_url'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="https://seudominio.com/webhook/asaas">
                        <p class="mt-1 text-xs text-gray-500">URL que receberá notificações de status dos boletos</p>
                    </div>

                    <!-- Token do Webhook -->
                    <div>
                        <label for="webhook_token" class="block text-sm font-medium text-gray-700 mb-2">
                            Token do Webhook
                        </label>
                        <input type="text" id="webhook_token" name="webhook_token"
                               value="<?php echo htmlspecialchars($config_asaas['webhook_token'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Token para validação do webhook">
                    </div>
                </div>
            </div>

            <!-- Instruções do Boleto -->
            <div class="border-t border-gray-200 pt-6">
                <h4 class="text-md font-semibold text-gray-800 mb-4">Textos do Boleto</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Instruções -->
                    <div>
                        <label for="instrucoes_boleto" class="block text-sm font-medium text-gray-700 mb-2">
                            Instruções do Boleto
                        </label>
                        <textarea id="instrucoes_boleto" name="instrucoes_boleto" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Instruções que aparecerão no boleto"><?php echo htmlspecialchars($config_asaas['instrucoes_boleto'] ?? 'Após o vencimento, multa de 2% e juros de 1% ao mês.'); ?></textarea>
                    </div>

                    <!-- Observações -->
                    <div>
                        <label for="observacoes_boleto" class="block text-sm font-medium text-gray-700 mb-2">
                            Observações do Boleto
                        </label>
                        <textarea id="observacoes_boleto" name="observacoes_boleto" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Observações adicionais"><?php echo htmlspecialchars($config_asaas['observacoes_boleto'] ?? 'Boleto gerado automaticamente pelo sistema Faciência ERP.'); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Botões -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <button type="button" onclick="testarConexao()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-plug mr-2"></i>
                    Testar Conexão
                </button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    Salvar Configurações
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Logs da API -->
<?php
try {
    $logs_recentes = $db->fetchAll("
        SELECT * FROM logs_asaas 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    
    if (!empty($logs_recentes)):
?>
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-history text-gray-500 mr-2"></i>
            Logs Recentes da API
        </h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operação</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID Asaas</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Erro</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php foreach ($logs_recentes as $log): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo date('d/m/Y H:i:s', strtotime($log['created_at'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo ucfirst(str_replace('_', ' ', $log['tipo_operacao'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?php echo $log['cobranca_id_asaas'] ?? '-'; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $log['sucesso'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo $log['sucesso'] ? 'Sucesso' : 'Erro'; ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            <?php echo $log['erro_mensagem'] ? substr($log['erro_mensagem'], 0, 50) . '...' : '-'; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>
<?php } catch (Exception $e) { /* Tabela pode não existir */ } ?>

<script>
function testarConexao() {
    const apiKey = document.getElementById('api_key').value;
    const ambiente = document.getElementById('ambiente').value;
    
    if (!apiKey) {
        alert('Por favor, insira a API Key antes de testar a conexão.');
        return;
    }
    
    // Aqui você pode implementar uma chamada AJAX para testar a conexão
    alert('Funcionalidade de teste será implementada em breve.');
}
</script>
