<!-- Formulário de Categoria -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-plus text-green-500 mr-2"></i>
        Cadastrar/Editar Categoria
    </h3>
    
    <form method="POST" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="form_tipo" value="categoria">
        <input type="hidden" id="categoria_id" name="id" value="">
        
        <!-- Nome -->
        <div>
            <label for="categoria_nome" class="block text-sm font-medium text-gray-700 mb-2">
                Nome da Categoria <span class="text-red-500">*</span>
            </label>
            <input type="text" id="categoria_nome" name="nome" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                   placeholder="Ex: Mensalidades, Energia Elétrica">
        </div>

        <!-- Tipo -->
        <div>
            <label for="categoria_tipo" class="block text-sm font-medium text-gray-700 mb-2">
                Tipo <span class="text-red-500">*</span>
            </label>
            <select id="categoria_tipo" name="tipo" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Selecione o tipo</option>
                <option value="receita">Receita</option>
                <option value="despesa">Despesa</option>
                <option value="ambos">Ambos</option>
            </select>
        </div>

        <!-- Descrição -->
        <div>
            <label for="categoria_descricao" class="block text-sm font-medium text-gray-700 mb-2">
                Descrição
            </label>
            <input type="text" id="categoria_descricao" name="descricao"
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                   placeholder="Descrição opcional">
        </div>

        <!-- Status -->
        <div>
            <label for="categoria_status" class="block text-sm font-medium text-gray-700 mb-2">
                Status
            </label>
            <select id="categoria_status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="ativo">Ativo</option>
                <option value="inativo">Inativo</option>
            </select>
        </div>

        <!-- Botões -->
        <div class="md:col-span-4 flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
            <button type="button" onclick="limparFormulario('categoria')" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-eraser mr-2"></i>
                Limpar
            </button>
            <button type="submit" id="btn_categoria" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-save mr-2"></i>
                Cadastrar Categoria
            </button>
        </div>
    </form>
</div>

<!-- Resumo Estatísticas -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <?php
    // Calcular estatísticas
    $stats_receitas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE tipo = 'receita' AND status = 'ativo'")['total'] ?? 0;
    $stats_despesas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE tipo = 'despesa' AND status = 'ativo'")['total'] ?? 0;
    $stats_inativas = $db->fetchOne("SELECT COUNT(*) as total FROM categorias_financeiras WHERE status = 'inativo'")['total'] ?? 0;
    $stats_total = $stats_receitas + $stats_despesas + $stats_inativas;
    ?>

    <!-- Total de Categorias -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Categorias</p>
                <p class="text-2xl font-bold text-gray-800"><?php echo $stats_total; ?></p>
            </div>
            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-tags text-gray-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Categorias de Receita -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Receitas</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $stats_receitas; ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Categorias de Despesa -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Despesas</p>
                <p class="text-2xl font-bold text-red-600"><?php echo $stats_despesas; ?></p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Categorias Inativas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Inativas</p>
                <p class="text-2xl font-bold text-gray-600"><?php echo $stats_inativas; ?></p>
            </div>
            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-pause text-gray-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-filter text-blue-500 mr-2"></i>
            Filtros
        </h3>
    </div>
    <div class="p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <input type="hidden" name="acao" value="categorias">

            <!-- Busca -->
            <div>
                <label for="busca" class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
                <input type="text" id="busca" name="busca" value="<?php echo htmlspecialchars($busca); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                       placeholder="Nome ou descrição...">
            </div>

            <!-- Tipo -->
            <div>
                <label for="filtro_tipo" class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                <select id="filtro_tipo" name="filtro_tipo" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Todos</option>
                    <option value="receita" <?php echo $filtro_tipo === 'receita' ? 'selected' : ''; ?>>Receita</option>
                    <option value="despesa" <?php echo $filtro_tipo === 'despesa' ? 'selected' : ''; ?>>Despesa</option>
                </select>
            </div>

            <!-- Status -->
            <div>
                <label for="filtro_status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="filtro_status" name="filtro_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Todos</option>
                    <option value="ativo" <?php echo $filtro_status === 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                    <option value="inativo" <?php echo $filtro_status === 'inativo' ? 'selected' : ''; ?>>Inativo</option>
                </select>
            </div>

            <!-- Botões -->
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Filtrar
                </button>
                <a href="?acao=categorias" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Limpar
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Categorias -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-green-500 mr-2"></i>
                Categorias Cadastradas (<?php echo $total_registros; ?> total)
            </h3>
            <div class="text-sm text-gray-500">
                Página <?php echo $page; ?> de <?php echo $total_paginas; ?>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($categorias)): ?>
            <div class="text-center py-12">
                <i class="fas fa-tags text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma categoria cadastrada</h3>
                <p class="text-gray-500 mb-6">Cadastre a primeira categoria para organizar suas transações financeiras.</p>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nome</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Criação</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($categorias as $categoria): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $tipo_config = [
                                    'receita' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Receita'],
                                    'despesa' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Despesa'],
                                    'ambos' => ['class' => 'bg-blue-100 text-blue-800', 'text' => 'Ambos']
                                ];
                                $config = $tipo_config[$categoria['tipo']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $categoria['tipo']];
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $config['class']; ?>">
                                    <?php echo $config['text']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <?php echo htmlspecialchars($categoria['descricao'] ?? ''); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo date('d/m/Y', strtotime($categoria['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button onclick="editarItem(<?php echo $categoria['id']; ?>, '<?php echo addslashes($categoria['nome']); ?>', 'categoria', '<?php echo addslashes($categoria['descricao'] ?? ''); ?>', '<?php echo $categoria['tipo']; ?>')" 
                                            class="text-indigo-600 hover:text-indigo-900" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="confirmarExclusao(<?php echo $categoria['id']; ?>, '<?php echo addslashes($categoria['nome']); ?>', 'categoria')" 
                                            class="text-red-600 hover:text-red-900" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <!-- Paginação -->
    <?php if ($total_paginas > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Mostrando <?php echo min($offset + 1, $total_registros); ?> a <?php echo min($offset + $limit, $total_registros); ?> de <?php echo $total_registros; ?> registros
                </div>

                <nav class="flex items-center space-x-2">
                    <!-- Primeira página -->
                    <?php if ($page > 1): ?>
                        <a href="?acao=categorias&page=1<?php echo $busca ? '&busca=' . urlencode($busca) : ''; ?><?php echo $filtro_tipo ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo $filtro_status ? '&filtro_status=' . urlencode($filtro_status) : ''; ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Página anterior -->
                    <?php if ($page > 1): ?>
                        <a href="?acao=categorias&page=<?php echo $page - 1; ?><?php echo $busca ? '&busca=' . urlencode($busca) : ''; ?><?php echo $filtro_tipo ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo $filtro_status ? '&filtro_status=' . urlencode($filtro_status) : ''; ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Páginas numeradas -->
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_paginas, $page + 2);

                    for ($i = $start_page; $i <= $end_page; $i++):
                        $is_current = ($i == $page);
                        $class = $is_current
                            ? 'px-3 py-2 text-sm font-medium text-white bg-green-600 border border-green-600'
                            : 'px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50';
                    ?>
                        <?php if ($is_current): ?>
                            <span class="<?php echo $class; ?>"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?acao=categorias&page=<?php echo $i; ?><?php echo $busca ? '&busca=' . urlencode($busca) : ''; ?><?php echo $filtro_tipo ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo $filtro_status ? '&filtro_status=' . urlencode($filtro_status) : ''; ?>"
                               class="<?php echo $class; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <!-- Próxima página -->
                    <?php if ($page < $total_paginas): ?>
                        <a href="?acao=categorias&page=<?php echo $page + 1; ?><?php echo $busca ? '&busca=' . urlencode($busca) : ''; ?><?php echo $filtro_tipo ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo $filtro_status ? '&filtro_status=' . urlencode($filtro_status) : ''; ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    <?php endif; ?>

                    <!-- Última página -->
                    <?php if ($page < $total_paginas): ?>
                        <a href="?acao=categorias&page=<?php echo $total_paginas; ?><?php echo $busca ? '&busca=' . urlencode($busca) : ''; ?><?php echo $filtro_tipo ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo $filtro_status ? '&filtro_status=' . urlencode($filtro_status) : ''; ?>"
                           class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Informações sobre Categorias -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Sobre as Categorias Financeiras</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li><strong>Receita:</strong> Categorias para entradas de dinheiro (mensalidades, taxas, etc.)</li>
                    <li><strong>Despesa:</strong> Categorias para saídas de dinheiro (energia, água, salários, etc.)</li>
                    <li><strong>Ambos:</strong> Categorias que podem ser usadas tanto para receitas quanto despesas</li>
                    <li>As categorias ajudam a organizar e gerar relatórios mais precisos</li>
                    <li>Recomenda-se criar categorias específicas para cada tipo de transação</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Categorias Sugeridas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            Categorias Sugeridas para Instituições de Ensino
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Receitas -->
            <div>
                <h4 class="text-md font-semibold text-green-800 mb-3">
                    <i class="fas fa-arrow-up text-green-600 mr-2"></i>
                    Receitas
                </h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-center"><i class="fas fa-graduation-cap text-green-500 mr-2"></i> Mensalidades</li>
                    <li class="flex items-center"><i class="fas fa-file-alt text-green-500 mr-2"></i> Taxas de Matrícula</li>
                    <li class="flex items-center"><i class="fas fa-certificate text-green-500 mr-2"></i> Certificados</li>
                    <li class="flex items-center"><i class="fas fa-book text-green-500 mr-2"></i> Material Didático</li>
                    <li class="flex items-center"><i class="fas fa-parking text-green-500 mr-2"></i> Estacionamento</li>
                    <li class="flex items-center"><i class="fas fa-utensils text-green-500 mr-2"></i> Cantina</li>
                </ul>
            </div>

            <!-- Despesas -->
            <div>
                <h4 class="text-md font-semibold text-red-800 mb-3">
                    <i class="fas fa-arrow-down text-red-600 mr-2"></i>
                    Despesas
                </h4>
                <ul class="space-y-2 text-sm text-gray-700">
                    <li class="flex items-center"><i class="fas fa-users text-red-500 mr-2"></i> Salários e Encargos</li>
                    <li class="flex items-center"><i class="fas fa-bolt text-red-500 mr-2"></i> Energia Elétrica</li>
                    <li class="flex items-center"><i class="fas fa-tint text-red-500 mr-2"></i> Água e Esgoto</li>
                    <li class="flex items-center"><i class="fas fa-phone text-red-500 mr-2"></i> Telefone e Internet</li>
                    <li class="flex items-center"><i class="fas fa-home text-red-500 mr-2"></i> Aluguel</li>
                    <li class="flex items-center"><i class="fas fa-tools text-red-500 mr-2"></i> Manutenção</li>
                    <li class="flex items-center"><i class="fas fa-clipboard-list text-red-500 mr-2"></i> Material de Escritório</li>
                    <li class="flex items-center"><i class="fas fa-shield-alt text-red-500 mr-2"></i> Seguros</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript para melhorar a experiência -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Busca em tempo real com debounce
    let searchTimeout;
    const searchInput = document.getElementById('busca');
    const form = searchInput.closest('form');

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                // Auto-submit após 500ms de inatividade
                if (searchInput.value.length >= 2 || searchInput.value.length === 0) {
                    form.submit();
                }
            }, 500);
        });
    }

    // Confirmar exclusão
    window.confirmarExclusao = function(id, nome, tipo) {
        if (confirm(`Tem certeza que deseja excluir a categoria "${nome}"?\n\nEsta ação não pode ser desfeita.`)) {
            // Criar form para enviar exclusão
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const inputAcao = document.createElement('input');
            inputAcao.type = 'hidden';
            inputAcao.name = 'acao';
            inputAcao.value = 'excluir';

            const inputId = document.createElement('input');
            inputId.type = 'hidden';
            inputId.name = 'id';
            inputId.value = id;

            const inputTipo = document.createElement('input');
            inputTipo.type = 'hidden';
            inputTipo.name = 'tipo';
            inputTipo.value = tipo;

            form.appendChild(inputAcao);
            form.appendChild(inputId);
            form.appendChild(inputTipo);

            document.body.appendChild(form);
            form.submit();
        }
    };

    // Editar categoria
    window.editarItem = function(id, nome, tipo, descricao, tipoCategoria, status) {
        document.getElementById('categoria_id').value = id;
        document.getElementById('categoria_nome').value = nome;
        document.getElementById('categoria_tipo').value = tipoCategoria;
        document.getElementById('categoria_descricao').value = descricao || '';
        document.getElementById('categoria_status').value = status || 'ativo';

        // Mudar texto do botão
        const btn = document.getElementById('btn_categoria');
        btn.innerHTML = '<i class="fas fa-save mr-2"></i>Atualizar Categoria';

        // Scroll para o formulário
        document.querySelector('form').scrollIntoView({ behavior: 'smooth' });
    };

    // Limpar formulário
    window.limparFormulario = function(tipo) {
        if (tipo === 'categoria') {
            document.getElementById('categoria_id').value = '';
            document.getElementById('categoria_nome').value = '';
            document.getElementById('categoria_tipo').value = '';
            document.getElementById('categoria_descricao').value = '';
            document.getElementById('categoria_status').value = 'ativo';

            // Restaurar texto do botão
            const btn = document.getElementById('btn_categoria');
            btn.innerHTML = '<i class="fas fa-save mr-2"></i>Cadastrar Categoria';
        }
    };

    // Highlight da busca
    const searchTerm = '<?php echo addslashes($busca); ?>';
    if (searchTerm) {
        const cells = document.querySelectorAll('td');
        cells.forEach(cell => {
            if (cell.textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                cell.innerHTML = cell.innerHTML.replace(
                    new RegExp(`(${searchTerm})`, 'gi'),
                    '<mark class="bg-yellow-200">$1</mark>'
                );
            }
        });
    }
});
</script>
