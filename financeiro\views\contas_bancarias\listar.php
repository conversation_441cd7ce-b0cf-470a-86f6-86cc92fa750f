<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                <option value="">Todos</option>
                <option value="ativa" <?php echo ($_GET['status'] ?? '') === 'ativa' ? 'selected' : ''; ?>>Ativa</option>
                <option value="inativa" <?php echo ($_GET['status'] ?? '') === 'inativa' ? 'selected' : ''; ?>>Inativa</option>
                <option value="encerrada" <?php echo ($_GET['status'] ?? '') === 'encerrada' ? 'selected' : ''; ?>>Encerrada</option>
            </select>
        </div>

        <!-- Banco -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Banco</label>
            <input type="text" name="banco" value="<?php echo htmlspecialchars($_GET['banco'] ?? ''); ?>" 
                   placeholder="Nome do banco..."
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
        </div>

        <!-- Busca -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
            <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>" 
                   placeholder="Nome, banco, agência..." 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
        </div>

        <!-- Botão Filtrar -->
        <div class="flex items-end">
            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filtrar
            </button>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_contas = count($contas);
$contas_ativas = 0;
$saldo_total = 0;

foreach ($contas as $conta) {
    if ($conta['status'] === 'ativa') {
        $contas_ativas++;
    }
    $saldo_atual = $conta['saldo_inicial'] + $conta['saldo_movimentacoes'];
    $saldo_total += $saldo_atual;
}
?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total de Contas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Contas</p>
                <p class="text-2xl font-bold text-blue-600"><?php echo $total_contas; ?></p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-university text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Contas Ativas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Contas Ativas</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $contas_ativas; ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Saldo Total -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Saldo Total</p>
                <?php 
                $cor_saldo = $saldo_total >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_saldo; ?>">
                    R$ <?php echo number_format($saldo_total, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-wallet text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Contas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-green-500 mr-2"></i>
                Contas Bancárias (<?php echo count($contas); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <a href="relatorios.php?tipo=contas_bancarias&formato=excel" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($contas)): ?>
            <div class="text-center py-12">
                <i class="fas fa-university text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma conta bancária encontrada</h3>
                <p class="text-gray-500 mb-6">Cadastre a primeira conta bancária para começar a controlar as movimentações financeiras.</p>
                <a href="contas_bancarias.php?acao=nova" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Cadastrar Primeira Conta
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conta</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banco</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agência/Conta</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo Atual</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($contas as $conta): ?>
                        <?php
                        $saldo_atual = $conta['saldo_inicial'] + $conta['saldo_movimentacoes'];
                        $cor_saldo = $saldo_atual >= 0 ? 'text-green-600' : 'text-red-600';
                        
                        $status_config = [
                            'ativa' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Ativa'],
                            'inativa' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => 'Inativa'],
                            'encerrada' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Encerrada']
                        ];
                        $status = $status_config[$conta['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $conta['status']];
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($conta['nome']); ?>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <?php echo htmlspecialchars($conta['banco']); ?> - <?php echo htmlspecialchars($conta['tipo']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($conta['banco']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>Ag: <?php echo htmlspecialchars($conta['agencia']); ?></div>
                                <div class="text-xs text-gray-500">CC: <?php echo htmlspecialchars($conta['conta']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo ucfirst($conta['tipo_conta']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium <?php echo $cor_saldo; ?>">
                                R$ <?php echo number_format($saldo_atual, 2, ',', '.'); ?>
                                <div class="text-xs text-gray-500">
                                    <?php echo $conta['total_transacoes']; ?> transações
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                                    <?php echo $status['text']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="contas_bancarias.php?acao=visualizar&id=<?php echo $conta['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="contas_bancarias.php?acao=editar&id=<?php echo $conta['id']; ?>" 
                                       class="text-indigo-600 hover:text-indigo-900" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>" 
                                       class="text-green-600 hover:text-green-900" title="Movimentações">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                    <button onclick="confirmarExclusao(<?php echo $conta['id']; ?>, '<?php echo addslashes($conta['nome']); ?>')" 
                                            class="text-red-600 hover:text-red-900" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>
