<?php
// Buscar movimentações da conta
$movimentacoes = $db->fetchAll("
    SELECT tf.*, c.nome as categoria_nome 
    FROM transacoes_financeiras tf 
    LEFT JOIN categorias_financeiras c ON tf.categoria_id = c.id 
    WHERE tf.conta_bancaria_id = ? 
    ORDER BY tf.data_transacao DESC 
    LIMIT 20
", [$conta['id']]);

// Calcular saldo atual
$saldo_movimentacoes = $db->fetchOne("
    SELECT COALESCE(SUM(CASE WHEN tipo = 'receita' THEN valor ELSE -valor END), 0) as saldo 
    FROM transacoes_financeiras 
    WHERE conta_bancaria_id = ?
", [$conta['id']]);

$saldo_atual = $conta['saldo_inicial'] + $saldo_movimentacoes['saldo'];
?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-university text-green-500 mr-2"></i>
                Detalhes da Conta Bancária
            </h3>
            <div class="flex items-center space-x-2">
                <a href="contas_bancarias.php" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>
                <a href="contas_bancarias.php?acao=editar&id=<?php echo $conta['id']; ?>" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Editar
                </a>
            </div>
        </div>
    </div>

    <!-- Informações da Conta -->
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados Básicos -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados da Conta
                </h4>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Nome da Conta</label>
                        <p class="text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($conta['nome']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Banco</label>
                        <p class="text-sm text-gray-900"><?php echo htmlspecialchars($conta['banco']); ?></p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Agência</label>
                            <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($conta['agencia']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Conta</label>
                            <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($conta['conta']); ?></p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tipo de Conta</label>
                        <p class="text-sm text-gray-900"><?php echo ucfirst($conta['tipo_conta']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <?php
                        $status_config = [
                            'ativa' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Ativa'],
                            'inativa' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => 'Inativa'],
                            'encerrada' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Encerrada']
                        ];
                        $status = $status_config[$conta['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $conta['status']];
                        ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                            <?php echo $status['text']; ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Saldos -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Saldos
                </h4>
                
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <label class="block text-sm font-medium text-blue-700">Saldo Inicial</label>
                        <p class="text-xl font-bold text-blue-800">
                            R$ <?php echo number_format($conta['saldo_inicial'], 2, ',', '.'); ?>
                        </p>
                        <p class="text-xs text-blue-600 mt-1">
                            Cadastrado em: <?php echo date('d/m/Y', strtotime($conta['created_at'])); ?>
                        </p>
                    </div>

                    <div class="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                        <label class="block text-sm font-medium text-purple-700">Movimentações</label>
                        <p class="text-xl font-bold <?php echo $saldo_movimentacoes['saldo'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php if ($saldo_movimentacoes['saldo'] >= 0): ?>
                                + R$ <?php echo number_format($saldo_movimentacoes['saldo'], 2, ',', '.'); ?>
                            <?php else: ?>
                                - R$ <?php echo number_format(abs($saldo_movimentacoes['saldo']), 2, ',', '.'); ?>
                            <?php endif; ?>
                        </p>
                    </div>

                    <div class="p-4 <?php echo $saldo_atual >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'; ?> border rounded-lg">
                        <label class="block text-sm font-medium <?php echo $saldo_atual >= 0 ? 'text-green-700' : 'text-red-700'; ?>">
                            Saldo Atual
                        </label>
                        <p class="text-2xl font-bold <?php echo $saldo_atual >= 0 ? 'text-green-800' : 'text-red-800'; ?>">
                            R$ <?php echo number_format($saldo_atual, 2, ',', '.'); ?>
                        </p>
                        <p class="text-xs <?php echo $saldo_atual >= 0 ? 'text-green-600' : 'text-red-600'; ?> mt-1">
                            Atualizado em tempo real
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Observações -->
        <?php if ($conta['observacoes']): ?>
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($conta['observacoes'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Últimas Movimentações -->
        <div class="mt-8">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-800">Últimas Movimentações</h4>
                <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>" 
                   class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    Ver todas
                </a>
            </div>

            <?php if (empty($movimentacoes)): ?>
                <div class="text-center py-8 bg-gray-50 border border-gray-200 rounded-lg">
                    <i class="fas fa-exchange-alt text-gray-300 text-4xl mb-3"></i>
                    <p class="text-gray-500">Nenhuma movimentação registrada</p>
                    <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>" 
                       class="text-blue-600 hover:text-blue-800 text-sm mt-2 inline-block">
                        Registrar primeira movimentação
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Data</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descrição</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tipo</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($movimentacoes as $mov): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('d/m/Y', strtotime($mov['data_transacao'])); ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-900">
                                        <?php echo htmlspecialchars($mov['descricao']); ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-500">
                                        <?php echo htmlspecialchars($mov['categoria_nome'] ?? 'Sem categoria'); ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                     <?php echo $mov['tipo'] === 'receita' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $mov['tipo'] === 'receita' ? 'Entrada' : 'Saída'; ?>
                                        </span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium">
                                        <span class="<?php echo $mov['tipo'] === 'receita' ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo $mov['tipo'] === 'receita' ? '+' : '-'; ?> 
                                            R$ <?php echo number_format($mov['valor'], 2, ',', '.'); ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Ações Rápidas -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Ações Rápidas</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>&acao=entrada" 
                   class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-green-700">Nova Entrada</span>
                </a>
                <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>&acao=saida" 
                   class="flex flex-col items-center p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-minus text-red-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-red-700">Nova Saída</span>
                </a>
                <a href="tesouraria.php?conta_id=<?php echo $conta['id']; ?>" 
                   class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-list text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-blue-700">Ver Extrato</span>
                </a>
                <a href="contas_bancarias.php?acao=editar&id=<?php echo $conta['id']; ?>" 
                   class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-edit text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-purple-700">Editar Conta</span>
                </a>
            </div>
        </div>
    </div>
</div>
