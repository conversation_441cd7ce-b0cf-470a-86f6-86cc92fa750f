<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas <?php echo $acao === 'nova' ? 'fa-plus' : 'fa-edit'; ?> text-red-500 mr-2"></i>
                <?php echo $acao === 'nova' ? 'Nova Conta a Pagar' : 'Editar Conta a Pagar'; ?>
            </h3>
            <a href="contas_pagar.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="salvar">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Descrição -->
            <div class="md:col-span-2">
                <label for="descricao" class="block text-sm font-medium text-gray-700 mb-2">
                    Descrição <span class="text-red-500">*</span>
                </label>
                <input type="text" id="descricao" name="descricao" required
                       value="<?php echo htmlspecialchars($conta['descricao'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                       placeholder="Ex: Pagamento de energia elétrica">
            </div>

            <!-- Fornecedor -->
            <div>
                <label for="fornecedor_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Fornecedor <span class="text-red-500">*</span>
                </label>
                <select id="fornecedor_id" name="fornecedor_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <option value="">Selecione um fornecedor</option>
                    <?php foreach ($fornecedores as $fornecedor): ?>
                        <option value="<?php echo $fornecedor['id']; ?>"
                                <?php echo ($conta['fornecedor_id'] ?? '') == $fornecedor['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($fornecedor['nome']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <p class="text-xs text-gray-500 mt-1">
                    <a href="fornecedores.php?acao=novo" target="_blank" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-plus mr-1"></i>Cadastrar novo fornecedor
                    </a>
                </p>
            </div>

            <!-- Categoria -->
            <div>
                <label for="categoria_id" class="block text-sm font-medium text-gray-700 mb-2">
                    Categoria <span class="text-red-500">*</span>
                </label>
                <select id="categoria_id" name="categoria_id" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                    <option value="">Selecione uma categoria</option>
                    <?php foreach ($categorias as $categoria): ?>
                        <option value="<?php echo $categoria['id']; ?>" 
                                <?php echo ($conta['categoria_id'] ?? '') == $categoria['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($categoria['nome']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Valor -->
            <div>
                <label for="valor" class="block text-sm font-medium text-gray-700 mb-2">
                    Valor <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="valor" name="valor" required
                           value="<?php echo isset($conta['valor']) ? number_format($conta['valor'], 2, ',', '.') : ''; ?>"
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                           placeholder="0,00"
                           onkeyup="formatarMoeda(this)">
                </div>
            </div>

            <!-- Data de Vencimento -->
            <div>
                <label for="data_vencimento" class="block text-sm font-medium text-gray-700 mb-2">
                    Data de Vencimento <span class="text-red-500">*</span>
                </label>
                <input type="date" id="data_vencimento" name="data_vencimento" required
                       value="<?php echo $conta['data_vencimento'] ?? ''; ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          placeholder="Informações adicionais sobre a conta..."><?php echo htmlspecialchars($conta['observacoes'] ?? ''); ?></textarea>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="contas_pagar.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    <?php echo $acao === 'nova' ? 'Cadastrar' : 'Atualizar'; ?>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Informações Adicionais -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Dicas para Cadastro</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Preencha uma descrição clara e detalhada da conta</li>
                    <li>Selecione a categoria correta para facilitar os relatórios</li>
                    <li>Verifique a data de vencimento para evitar atrasos</li>
                    <li>Use as observações para informações importantes</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.getElementById('descricao').value = '';
        document.getElementById('fornecedor_id').value = '';
        document.getElementById('categoria_id').value = '';
        document.getElementById('valor').value = '';
        document.getElementById('data_vencimento').value = '';
        document.getElementById('observacoes').value = '';
        document.getElementById('descricao').focus();
    }
}

// Auto-complete para fornecedores (pode ser implementado futuramente)
document.addEventListener('DOMContentLoaded', function() {
    // Definir data padrão como hoje + 30 dias se for nova conta
    <?php if ($acao === 'nova'): ?>
        const dataVencimento = document.getElementById('data_vencimento');
        if (!dataVencimento.value) {
            const hoje = new Date();
            hoje.setDate(hoje.getDate() + 30);
            dataVencimento.value = hoje.toISOString().split('T')[0];
        }
    <?php endif; ?>
    
    // Foco no primeiro campo
    document.getElementById('descricao').focus();
});
</script>
