<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas <?php echo $acao === 'novo' ? 'fa-plus' : 'fa-edit'; ?> text-orange-500 mr-2"></i>
                <?php echo $acao === 'novo' ? 'Novo Fornecedor' : 'Editar Fornecedor'; ?>
            </h3>
            <a href="fornecedores.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="salvar">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Nome -->
            <div class="md:col-span-2">
                <label for="nome" class="block text-sm font-medium text-gray-700 mb-2">
                    Nome/Razão Social <span class="text-red-500">*</span>
                </label>
                <input type="text" id="nome" name="nome" required
                       value="<?php echo htmlspecialchars($fornecedor['nome'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="Nome completo ou razão social">
            </div>

            <!-- Tipo de Pessoa -->
            <div>
                <label for="tipo_pessoa" class="block text-sm font-medium text-gray-700 mb-2">
                    Tipo de Pessoa <span class="text-red-500">*</span>
                </label>
                <select id="tipo_pessoa" name="tipo_pessoa" required onchange="alterarTipoPessoa()"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <option value="">Selecione o tipo</option>
                    <option value="fisica" <?php echo ($fornecedor['tipo_pessoa'] ?? '') === 'fisica' ? 'selected' : ''; ?>>Pessoa Física</option>
                    <option value="juridica" <?php echo ($fornecedor['tipo_pessoa'] ?? '') === 'juridica' ? 'selected' : ''; ?>>Pessoa Jurídica</option>
                </select>
            </div>

            <!-- CPF/CNPJ -->
            <div>
                <label for="cpf_cnpj" class="block text-sm font-medium text-gray-700 mb-2">
                    <span id="label_documento">CPF/CNPJ</span> <span class="text-red-500">*</span>
                </label>
                <input type="text" id="cpf_cnpj" name="cpf_cnpj" required
                       value="<?php echo htmlspecialchars($fornecedor['cpf_cnpj'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="000.000.000-00"
                       onkeyup="formatarCpfCnpj(this)">
            </div>

            <!-- Email -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                    Email
                </label>
                <input type="email" id="email" name="email"
                       value="<?php echo htmlspecialchars($fornecedor['email'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="<EMAIL>">
            </div>

            <!-- Telefone -->
            <div>
                <label for="telefone" class="block text-sm font-medium text-gray-700 mb-2">
                    Telefone
                </label>
                <input type="text" id="telefone" name="telefone"
                       value="<?php echo htmlspecialchars($fornecedor['telefone'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="(11) 99999-9999"
                       onkeyup="formatarTelefone(this)">
            </div>

            <!-- Endereço -->
            <div class="md:col-span-2">
                <label for="endereco" class="block text-sm font-medium text-gray-700 mb-2">
                    Endereço
                </label>
                <input type="text" id="endereco" name="endereco"
                       value="<?php echo htmlspecialchars($fornecedor['endereco'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="Rua, número, bairro">
            </div>

            <!-- Cidade -->
            <div>
                <label for="cidade" class="block text-sm font-medium text-gray-700 mb-2">
                    Cidade
                </label>
                <input type="text" id="cidade" name="cidade"
                       value="<?php echo htmlspecialchars($fornecedor['cidade'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="Nome da cidade">
            </div>

            <!-- Estado -->
            <div>
                <label for="estado" class="block text-sm font-medium text-gray-700 mb-2">
                    Estado
                </label>
                <select id="estado" name="estado"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <option value="">Selecione o estado</option>
                    <option value="AC" <?php echo ($fornecedor['estado'] ?? '') === 'AC' ? 'selected' : ''; ?>>Acre</option>
                    <option value="AL" <?php echo ($fornecedor['estado'] ?? '') === 'AL' ? 'selected' : ''; ?>>Alagoas</option>
                    <option value="AP" <?php echo ($fornecedor['estado'] ?? '') === 'AP' ? 'selected' : ''; ?>>Amapá</option>
                    <option value="AM" <?php echo ($fornecedor['estado'] ?? '') === 'AM' ? 'selected' : ''; ?>>Amazonas</option>
                    <option value="BA" <?php echo ($fornecedor['estado'] ?? '') === 'BA' ? 'selected' : ''; ?>>Bahia</option>
                    <option value="CE" <?php echo ($fornecedor['estado'] ?? '') === 'CE' ? 'selected' : ''; ?>>Ceará</option>
                    <option value="DF" <?php echo ($fornecedor['estado'] ?? '') === 'DF' ? 'selected' : ''; ?>>Distrito Federal</option>
                    <option value="ES" <?php echo ($fornecedor['estado'] ?? '') === 'ES' ? 'selected' : ''; ?>>Espírito Santo</option>
                    <option value="GO" <?php echo ($fornecedor['estado'] ?? '') === 'GO' ? 'selected' : ''; ?>>Goiás</option>
                    <option value="MA" <?php echo ($fornecedor['estado'] ?? '') === 'MA' ? 'selected' : ''; ?>>Maranhão</option>
                    <option value="MT" <?php echo ($fornecedor['estado'] ?? '') === 'MT' ? 'selected' : ''; ?>>Mato Grosso</option>
                    <option value="MS" <?php echo ($fornecedor['estado'] ?? '') === 'MS' ? 'selected' : ''; ?>>Mato Grosso do Sul</option>
                    <option value="MG" <?php echo ($fornecedor['estado'] ?? '') === 'MG' ? 'selected' : ''; ?>>Minas Gerais</option>
                    <option value="PA" <?php echo ($fornecedor['estado'] ?? '') === 'PA' ? 'selected' : ''; ?>>Pará</option>
                    <option value="PB" <?php echo ($fornecedor['estado'] ?? '') === 'PB' ? 'selected' : ''; ?>>Paraíba</option>
                    <option value="PR" <?php echo ($fornecedor['estado'] ?? '') === 'PR' ? 'selected' : ''; ?>>Paraná</option>
                    <option value="PE" <?php echo ($fornecedor['estado'] ?? '') === 'PE' ? 'selected' : ''; ?>>Pernambuco</option>
                    <option value="PI" <?php echo ($fornecedor['estado'] ?? '') === 'PI' ? 'selected' : ''; ?>>Piauí</option>
                    <option value="RJ" <?php echo ($fornecedor['estado'] ?? '') === 'RJ' ? 'selected' : ''; ?>>Rio de Janeiro</option>
                    <option value="RN" <?php echo ($fornecedor['estado'] ?? '') === 'RN' ? 'selected' : ''; ?>>Rio Grande do Norte</option>
                    <option value="RS" <?php echo ($fornecedor['estado'] ?? '') === 'RS' ? 'selected' : ''; ?>>Rio Grande do Sul</option>
                    <option value="RO" <?php echo ($fornecedor['estado'] ?? '') === 'RO' ? 'selected' : ''; ?>>Rondônia</option>
                    <option value="RR" <?php echo ($fornecedor['estado'] ?? '') === 'RR' ? 'selected' : ''; ?>>Roraima</option>
                    <option value="SC" <?php echo ($fornecedor['estado'] ?? '') === 'SC' ? 'selected' : ''; ?>>Santa Catarina</option>
                    <option value="SP" <?php echo ($fornecedor['estado'] ?? '') === 'SP' ? 'selected' : ''; ?>>São Paulo</option>
                    <option value="SE" <?php echo ($fornecedor['estado'] ?? '') === 'SE' ? 'selected' : ''; ?>>Sergipe</option>
                    <option value="TO" <?php echo ($fornecedor['estado'] ?? '') === 'TO' ? 'selected' : ''; ?>>Tocantins</option>
                </select>
            </div>

            <!-- CEP -->
            <div>
                <label for="cep" class="block text-sm font-medium text-gray-700 mb-2">
                    CEP
                </label>
                <input type="text" id="cep" name="cep"
                       value="<?php echo htmlspecialchars($fornecedor['cep'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                       placeholder="00000-000"
                       onkeyup="formatarCep(this)">
            </div>

            <!-- Status -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                    Status <span class="text-red-500">*</span>
                </label>
                <select id="status" name="status" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    <option value="ativo" <?php echo ($fornecedor['status'] ?? 'ativo') === 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                    <option value="inativo" <?php echo ($fornecedor['status'] ?? '') === 'inativo' ? 'selected' : ''; ?>>Inativo</option>
                </select>
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                          placeholder="Informações adicionais sobre o fornecedor..."><?php echo htmlspecialchars($fornecedor['observacoes'] ?? ''); ?></textarea>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="fornecedores.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="limparFormulario()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eraser mr-2"></i>
                    Limpar
                </button>
                <button type="submit" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    <?php echo $acao === 'novo' ? 'Cadastrar' : 'Atualizar'; ?>
                </button>
            </div>
        </div>
    </form>
</div>

<script>
function limparFormulario() {
    if (confirm('Tem certeza que deseja limpar todos os campos?')) {
        document.getElementById('nome').value = '';
        document.getElementById('tipo_pessoa').value = '';
        document.getElementById('cpf_cnpj').value = '';
        document.getElementById('email').value = '';
        document.getElementById('telefone').value = '';
        document.getElementById('endereco').value = '';
        document.getElementById('cidade').value = '';
        document.getElementById('estado').value = '';
        document.getElementById('cep').value = '';
        document.getElementById('status').value = 'ativo';
        document.getElementById('observacoes').value = '';
        document.getElementById('nome').focus();
    }
}

function formatarTelefone(input) {
    let valor = input.value.replace(/\D/g, '');
    
    if (valor.length <= 10) {
        // Telefone fixo: (11) 1234-5678
        valor = valor.replace(/(\d{2})(\d)/, '($1) $2');
        valor = valor.replace(/(\d{4})(\d)/, '$1-$2');
    } else {
        // Celular: (11) 99999-9999
        valor = valor.replace(/(\d{2})(\d)/, '($1) $2');
        valor = valor.replace(/(\d{5})(\d)/, '$1-$2');
    }
    
    input.value = valor;
}

function formatarCep(input) {
    let valor = input.value.replace(/\D/g, '');
    valor = valor.replace(/(\d{5})(\d)/, '$1-$2');
    input.value = valor;
}

// Auto-focus no primeiro campo
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('nome').focus();
    
    // Configurar tipo de pessoa inicial
    alterarTipoPessoa();
});
</script>
