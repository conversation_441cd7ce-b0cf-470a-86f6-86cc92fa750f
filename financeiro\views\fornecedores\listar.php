<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Todos</option>
                <option value="ativo" <?php echo ($_GET['status'] ?? '') === 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                <option value="inativo" <?php echo ($_GET['status'] ?? '') === 'inativo' ? 'selected' : ''; ?>>Inativo</option>
            </select>
        </div>

        <!-- Tipo de Pessoa -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
            <select name="tipo_pessoa" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <option value="">Todos</option>
                <option value="fisica" <?php echo ($_GET['tipo_pessoa'] ?? '') === 'fisica' ? 'selected' : ''; ?>>Pessoa Física</option>
                <option value="juridica" <?php echo ($_GET['tipo_pessoa'] ?? '') === 'juridica' ? 'selected' : ''; ?>>Pessoa Jurídica</option>
            </select>
        </div>

        <!-- Busca -->
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
            <div class="flex">
                <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>" 
                       placeholder="Nome, CPF/CNPJ ou email..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                <button type="submit" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_fornecedores = count($fornecedores);
$fornecedores_ativos = 0;

foreach ($fornecedores as $fornecedor) {
    if ($fornecedor['status'] === 'ativo') {
        $fornecedores_ativos++;
    }
}

// Buscar total pendente de contas a pagar
try {
    $total_pendente_result = $db->fetchOne("SELECT COALESCE(SUM(valor), 0) as total FROM contas_pagar WHERE status = 'pendente'");
    $total_pendente = $total_pendente_result['total'] ?? 0;
} catch (Exception $e) {
    $total_pendente = 0;
}
?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total de Fornecedores -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Fornecedores</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo $total_fornecedores; ?></p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-truck text-orange-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Fornecedores Ativos -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Ativos</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $fornecedores_ativos; ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Valor Pendente -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Valor Pendente</p>
                <p class="text-2xl font-bold text-red-600">
                    R$ <?php echo number_format($total_pendente, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Fornecedores -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-orange-500 mr-2"></i>
                Fornecedores (<?php echo count($fornecedores); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <a href="relatorios.php?tipo=fornecedores&formato=excel" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($fornecedores)): ?>
            <div class="text-center py-12">
                <i class="fas fa-truck text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum fornecedor encontrado</h3>
                <p class="text-gray-500 mb-6">Cadastre o primeiro fornecedor para começar a gerenciar suas contas a pagar.</p>
                <a href="fornecedores.php?acao=novo" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Cadastrar Primeiro Fornecedor
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fornecedor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPF/CNPJ</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contato</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contas</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($fornecedores as $fornecedor): ?>
                        <?php
                        $status_config = [
                            'ativo' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Ativo'],
                            'inativo' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Inativo']
                        ];
                        $status = $status_config[$fornecedor['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $fornecedor['status']];
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($fornecedor['nome']); ?>
                                </div>
                                <?php if ($fornecedor['cidade']): ?>
                                    <div class="text-sm text-gray-500">
                                        <?php echo htmlspecialchars($fornecedor['cidade']); ?>
                                        <?php if ($fornecedor['estado']): ?>
                                            - <?php echo htmlspecialchars($fornecedor['estado']); ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $fornecedor['tipo_pessoa'] === 'fisica' ? 'Pessoa Física' : 'Pessoa Jurídica'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($fornecedor['cpf_cnpj']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php if ($fornecedor['email']): ?>
                                    <div><?php echo htmlspecialchars($fornecedor['email']); ?></div>
                                <?php endif; ?>
                                <?php if ($fornecedor['telefone']): ?>
                                    <div class="text-gray-500"><?php echo htmlspecialchars($fornecedor['telefone']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="font-medium"><?php echo $fornecedor['total_contas']; ?> contas</div>
                                <?php if ($fornecedor['valor_pendente'] > 0): ?>
                                    <div class="text-red-600 text-xs">
                                        R$ <?php echo number_format($fornecedor['valor_pendente'], 2, ',', '.'); ?> pendente
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                                    <?php echo $status['text']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="fornecedores.php?acao=visualizar&id=<?php echo $fornecedor['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="fornecedores.php?acao=editar&id=<?php echo $fornecedor['id']; ?>" 
                                       class="text-indigo-600 hover:text-indigo-900" title="Editar">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="contas_pagar.php?fornecedor_id=<?php echo $fornecedor['id']; ?>" 
                                       class="text-green-600 hover:text-green-900" title="Ver Contas">
                                        <i class="fas fa-file-invoice-dollar"></i>
                                    </a>
                                    <button onclick="confirmarExclusao(<?php echo $fornecedor['id']; ?>, '<?php echo addslashes($fornecedor['nome']); ?>')" 
                                            class="text-red-600 hover:text-red-900" title="Excluir">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<script>
// Auto-submit quando mudar filtros
document.addEventListener('DOMContentLoaded', function() {
    const filtros = document.querySelectorAll('.filtro-auto');
    filtros.forEach(filtro => {
        filtro.addEventListener('change', function() {
            document.getElementById('form-filtros').submit();
        });
    });
});
</script>
