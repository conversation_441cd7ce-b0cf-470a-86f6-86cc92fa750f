<?php
// Buscar histórico de contas do fornecedor
$historico_contas = $db->fetchAll("
    SELECT cp.*, c.nome as categoria_nome 
    FROM contas_pagar cp 
    LEFT JOIN categorias_financeiras c ON cp.categoria_id = c.id 
    WHERE cp.fornecedor_id = ? 
    ORDER BY cp.data_vencimento DESC 
    LIMIT 20
", [$fornecedor['id']]);

// Calcular estatísticas
$estatisticas = $db->fetchOne("
    SELECT 
        COUNT(*) as total_contas,
        COALESCE(SUM(valor), 0) as valor_total,
        COALESCE(SUM(CASE WHEN status = 'pendente' THEN valor ELSE 0 END), 0) as valor_pendente,
        COALESCE(SUM(CASE WHEN status = 'pago' THEN valor ELSE 0 END), 0) as valor_pago
    FROM contas_pagar 
    WHERE fornecedor_id = ?
", [$fornecedor['id']]);
?>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-truck text-orange-500 mr-2"></i>
                Detalhes do Fornecedor
            </h3>
            <div class="flex items-center space-x-2">
                <a href="fornecedores.php" class="text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>
                <a href="fornecedores.php?acao=editar&id=<?php echo $fornecedor['id']; ?>" 
                   class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-edit mr-2"></i>
                    Editar
                </a>
            </div>
        </div>
    </div>

    <!-- Informações do Fornecedor -->
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Dados Básicos -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados Básicos
                </h4>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Nome/Razão Social</label>
                        <p class="text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($fornecedor['nome']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Tipo de Pessoa</label>
                        <p class="text-sm text-gray-900">
                            <?php echo $fornecedor['tipo_pessoa'] === 'fisica' ? 'Pessoa Física' : 'Pessoa Jurídica'; ?>
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">
                            <?php echo $fornecedor['tipo_pessoa'] === 'fisica' ? 'CPF' : 'CNPJ'; ?>
                        </label>
                        <p class="text-sm text-gray-900 font-mono"><?php echo htmlspecialchars($fornecedor['cpf_cnpj']); ?></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Status</label>
                        <?php
                        $status_config = [
                            'ativo' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Ativo'],
                            'inativo' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Inativo']
                        ];
                        $status = $status_config[$fornecedor['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $fornecedor['status']];
                        ?>
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status['class']; ?>">
                            <?php echo $status['text']; ?>
                        </span>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Data de Cadastro</label>
                        <p class="text-sm text-gray-900"><?php echo date('d/m/Y', strtotime($fornecedor['created_at'])); ?></p>
                    </div>
                </div>
            </div>

            <!-- Dados de Contato -->
            <div class="space-y-4">
                <h4 class="text-md font-semibold text-gray-800 border-b border-gray-200 pb-2">
                    Dados de Contato
                </h4>
                
                <div class="space-y-3">
                    <?php if ($fornecedor['email']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-sm text-gray-900">
                                <a href="mailto:<?php echo htmlspecialchars($fornecedor['email']); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    <?php echo htmlspecialchars($fornecedor['email']); ?>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($fornecedor['telefone']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Telefone</label>
                            <p class="text-sm text-gray-900">
                                <a href="tel:<?php echo htmlspecialchars($fornecedor['telefone']); ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    <?php echo htmlspecialchars($fornecedor['telefone']); ?>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($fornecedor['endereco'] || $fornecedor['cidade'] || $fornecedor['estado']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Endereço</label>
                            <div class="text-sm text-gray-900">
                                <?php if ($fornecedor['endereco']): ?>
                                    <p><?php echo htmlspecialchars($fornecedor['endereco']); ?></p>
                                <?php endif; ?>
                                <?php if ($fornecedor['cidade'] || $fornecedor['estado']): ?>
                                    <p>
                                        <?php echo htmlspecialchars($fornecedor['cidade']); ?>
                                        <?php if ($fornecedor['cidade'] && $fornecedor['estado']): ?> - <?php endif; ?>
                                        <?php echo htmlspecialchars($fornecedor['estado']); ?>
                                    </p>
                                <?php endif; ?>
                                <?php if ($fornecedor['cep']): ?>
                                    <p>CEP: <?php echo htmlspecialchars($fornecedor['cep']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Observações -->
        <?php if ($fornecedor['observacoes']): ?>
            <div class="mt-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <p class="text-sm text-gray-900"><?php echo nl2br(htmlspecialchars($fornecedor['observacoes'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Estatísticas Financeiras -->
        <div class="mt-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Estatísticas Financeiras</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Total de Contas -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-blue-600">Total de Contas</p>
                            <p class="text-xl font-bold text-blue-800"><?php echo $estatisticas['total_contas']; ?></p>
                        </div>
                        <i class="fas fa-file-invoice text-blue-400 text-2xl"></i>
                    </div>
                </div>

                <!-- Valor Total -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-purple-600">Valor Total</p>
                            <p class="text-xl font-bold text-purple-800">
                                R$ <?php echo number_format($estatisticas['valor_total'], 2, ',', '.'); ?>
                            </p>
                        </div>
                        <i class="fas fa-calculator text-purple-400 text-2xl"></i>
                    </div>
                </div>

                <!-- Valor Pendente -->
                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-orange-600">Valor Pendente</p>
                            <p class="text-xl font-bold text-orange-800">
                                R$ <?php echo number_format($estatisticas['valor_pendente'], 2, ',', '.'); ?>
                            </p>
                        </div>
                        <i class="fas fa-clock text-orange-400 text-2xl"></i>
                    </div>
                </div>

                <!-- Valor Pago -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-green-600">Valor Pago</p>
                            <p class="text-xl font-bold text-green-800">
                                R$ <?php echo number_format($estatisticas['valor_pago'], 2, ',', '.'); ?>
                            </p>
                        </div>
                        <i class="fas fa-check-circle text-green-400 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Histórico de Contas -->
        <div class="mt-8">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-md font-semibold text-gray-800">Histórico de Contas</h4>
                <a href="contas_pagar.php?fornecedor_id=<?php echo $fornecedor['id']; ?>" 
                   class="text-orange-600 hover:text-orange-800 text-sm">
                    <i class="fas fa-external-link-alt mr-1"></i>
                    Ver todas
                </a>
            </div>

            <?php if (empty($historico_contas)): ?>
                <div class="text-center py-8 bg-gray-50 border border-gray-200 rounded-lg">
                    <i class="fas fa-file-invoice text-gray-300 text-4xl mb-3"></i>
                    <p class="text-gray-500">Nenhuma conta registrada para este fornecedor</p>
                    <a href="contas_pagar.php?acao=nova&fornecedor_id=<?php echo $fornecedor['id']; ?>" 
                       class="text-orange-600 hover:text-orange-800 text-sm mt-2 inline-block">
                        Cadastrar primeira conta
                    </a>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Descrição</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Categoria</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Vencimento</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Valor</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($historico_contas as $conta): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-3 text-sm text-gray-900">
                                        <?php echo htmlspecialchars($conta['descricao']); ?>
                                    </td>
                                    <td class="px-4 py-3 text-sm text-gray-500">
                                        <?php echo htmlspecialchars($conta['categoria_nome'] ?? 'Sem categoria'); ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('d/m/Y', strtotime($conta['data_vencimento'])); ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                        R$ <?php echo number_format($conta['valor'], 2, ',', '.'); ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?php
                                        $status_conta_config = [
                                            'pendente' => ['class' => 'bg-orange-100 text-orange-800', 'text' => 'Pendente'],
                                            'pago' => ['class' => 'bg-green-100 text-green-800', 'text' => 'Pago'],
                                            'vencido' => ['class' => 'bg-red-100 text-red-800', 'text' => 'Vencido']
                                        ];
                                        $status_conta = $status_conta_config[$conta['status']] ?? ['class' => 'bg-gray-100 text-gray-800', 'text' => $conta['status']];
                                        ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_conta['class']; ?>">
                                            <?php echo $status_conta['text']; ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Ações Rápidas -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Ações Rápidas</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="contas_pagar.php?acao=nova&fornecedor_id=<?php echo $fornecedor['id']; ?>" 
                   class="flex flex-col items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-plus text-orange-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-orange-700">Nova Conta</span>
                </a>
                <a href="contas_pagar.php?fornecedor_id=<?php echo $fornecedor['id']; ?>" 
                   class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-list text-blue-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-blue-700">Ver Contas</span>
                </a>
                <a href="fornecedores.php?acao=editar&id=<?php echo $fornecedor['id']; ?>" 
                   class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-edit text-purple-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-purple-700">Editar Dados</span>
                </a>
                <a href="mailto:<?php echo htmlspecialchars($fornecedor['email']); ?>" 
                   class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer text-center">
                    <i class="fas fa-envelope text-green-600 text-2xl mb-2"></i>
                    <span class="text-sm font-medium text-green-700">Enviar Email</span>
                </a>
            </div>
        </div>
    </div>
</div>
