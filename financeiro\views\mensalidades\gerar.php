<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-plus text-purple-500 mr-2"></i>
                Gerar Mensalidades
            </h3>
            <a href="mensalidades.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="gerar_mensalidades">
        
        <!-- Informações do Período -->
        <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Período de Geração</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Mês -->
                <div>
                    <label for="mes" class="block text-sm font-medium text-gray-700 mb-2">
                        Mês <span class="text-red-500">*</span>
                    </label>
                    <select id="mes" name="mes" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Selecione o mês</option>
                        <?php for ($m = 1; $m <= 12; $m++): ?>
                            <option value="<?php echo sprintf('%02d', $m); ?>" <?php echo date('m') == sprintf('%02d', $m) ? 'selected' : ''; ?>>
                                <?php echo ucfirst(strftime('%B', mktime(0, 0, 0, $m, 1))); ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>

                <!-- Ano -->
                <div>
                    <label for="ano" class="block text-sm font-medium text-gray-700 mb-2">
                        Ano <span class="text-red-500">*</span>
                    </label>
                    <select id="ano" name="ano" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Selecione o ano</option>
                        <?php for ($a = date('Y'); $a <= date('Y') + 1; $a++): ?>
                            <option value="<?php echo $a; ?>" <?php echo date('Y') == $a ? 'selected' : ''; ?>>
                                <?php echo $a; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
            </div>
        </div>

        <!-- Filtros de Geração -->
        <div class="mb-8">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Filtros de Geração</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Polo -->
                <div>
                    <label for="polo_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Polo (Opcional)
                    </label>
                    <select id="polo_id" name="polo_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Todos os polos</option>
                        <?php foreach ($polos as $polo): ?>
                            <option value="<?php echo $polo['id']; ?>">
                                <?php echo htmlspecialchars($polo['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Deixe em branco para gerar para todos os polos</p>
                </div>

                <!-- Curso -->
                <div>
                    <label for="curso_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Curso (Opcional)
                    </label>
                    <select id="curso_id" name="curso_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Todos os cursos</option>
                        <?php foreach ($cursos as $curso): ?>
                            <option value="<?php echo $curso['id']; ?>">
                                <?php echo htmlspecialchars($curso['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">Deixe em branco para gerar para todos os cursos</p>
                </div>
            </div>
        </div>

        <!-- Resumo da Geração -->
        <div class="mb-8 p-4 bg-purple-50 border border-purple-200 rounded-lg">
            <h4 class="text-md font-semibold text-purple-800 mb-2">Como Funciona a Geração</h4>
            <div class="text-sm text-purple-700 space-y-2">
                <p><strong>1. Busca de Matrículas:</strong> O sistema buscará todas as matrículas ativas que correspondam aos filtros selecionados.</p>
                <p><strong>2. Verificação de Duplicatas:</strong> Mensalidades já existentes para o período não serão duplicadas.</p>
                <p><strong>3. Valores:</strong> Serão utilizados os valores de mensalidade configurados em cada curso.</p>
                <p><strong>4. Data de Vencimento:</strong> Será definida como dia 10 do mês selecionado.</p>
                <p><strong>5. Status Inicial:</strong> Todas as mensalidades serão criadas com status "Pendente".</p>
            </div>
        </div>

        <!-- Prévia da Geração -->
        <div id="previa-geracao" class="mb-8" style="display: none;">
            <h4 class="text-md font-semibold text-gray-800 mb-4">Prévia da Geração</h4>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div id="previa-conteudo" class="text-sm text-gray-600">
                    <!-- Conteúdo será carregado via JavaScript -->
                </div>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="mensalidades.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <div class="flex items-center space-x-4">
                <button type="button" onclick="visualizarPrevia()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-eye mr-2"></i>
                    Visualizar Prévia
                </button>
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Gerar Mensalidades
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Alerta Importante -->
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Atenção</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Certifique-se de que os valores de mensalidade estão corretos nos cursos</li>
                    <li>A geração não criará duplicatas para o mesmo aluno/período</li>
                    <li>Mensalidades já existentes não serão alteradas</li>
                    <li>Recomenda-se fazer uma prévia antes de gerar</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function visualizarPrevia() {
    const mes = document.getElementById('mes').value;
    const ano = document.getElementById('ano').value;
    const polo_id = document.getElementById('polo_id').value;
    const curso_id = document.getElementById('curso_id').value;
    
    if (!mes || !ano) {
        alert('Por favor, selecione o mês e ano antes de visualizar a prévia.');
        return;
    }
    
    // Aqui seria feita uma requisição AJAX para buscar a prévia
    // Por enquanto, vamos simular
    const previaDiv = document.getElementById('previa-geracao');
    const conteudoDiv = document.getElementById('previa-conteudo');
    
    conteudoDiv.innerHTML = `
        <div class="flex items-center mb-2">
            <i class="fas fa-spinner fa-spin text-purple-600 mr-2"></i>
            <span>Carregando prévia...</span>
        </div>
    `;
    
    previaDiv.style.display = 'block';
    
    // Simular carregamento
    setTimeout(() => {
        conteudoDiv.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="text-center p-3 bg-white rounded border">
                    <div class="text-2xl font-bold text-purple-600">150</div>
                    <div class="text-sm text-gray-600">Matrículas Encontradas</div>
                </div>
                <div class="text-center p-3 bg-white rounded border">
                    <div class="text-2xl font-bold text-green-600">120</div>
                    <div class="text-sm text-gray-600">Mensalidades a Gerar</div>
                </div>
                <div class="text-center p-3 bg-white rounded border">
                    <div class="text-2xl font-bold text-orange-600">30</div>
                    <div class="text-sm text-gray-600">Já Existentes</div>
                </div>
            </div>
            <div class="text-sm text-gray-600">
                <p><strong>Período:</strong> ${mes}/${ano}</p>
                <p><strong>Polo:</strong> ${polo_id ? 'Filtro aplicado' : 'Todos os polos'}</p>
                <p><strong>Curso:</strong> ${curso_id ? 'Filtro aplicado' : 'Todos os cursos'}</p>
                <p><strong>Data de Vencimento:</strong> 10/${mes}/${ano}</p>
            </div>
        `;
    }, 1500);
}

// Confirmação antes de gerar
document.querySelector('form').addEventListener('submit', function(e) {
    const mes = document.getElementById('mes').value;
    const ano = document.getElementById('ano').value;
    
    if (!confirm(`Confirma a geração de mensalidades para ${mes}/${ano}?\n\nEsta ação criará mensalidades para todos os alunos com matrículas ativas.`)) {
        e.preventDefault();
    }
});

// Definir mês atual como padrão
document.addEventListener('DOMContentLoaded', function() {
    const mesAtual = new Date().getMonth() + 1;
    const proximoMes = mesAtual === 12 ? 1 : mesAtual + 1;
    
    // Sugerir próximo mês para geração
    document.getElementById('mes').value = String(proximoMes).padStart(2, '0');
    
    if (proximoMes === 1) {
        // Se for janeiro, incrementar o ano
        const anoAtual = new Date().getFullYear();
        document.getElementById('ano').value = anoAtual + 1;
    }
});
</script>
