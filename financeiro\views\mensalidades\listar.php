<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <input type="hidden" name="acao" value="listar">
        
        <!-- Status -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select name="status" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <option value="pendente" <?php echo ($_GET['status'] ?? '') === 'pendente' ? 'selected' : ''; ?>>Pendente</option>
                <option value="pago" <?php echo ($_GET['status'] ?? '') === 'pago' ? 'selected' : ''; ?>>Pago</option>
            </select>
        </div>

        <!-- Polo -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Polo</label>
            <select name="polo" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <?php foreach ($polos as $polo): ?>
                    <option value="<?php echo $polo['id']; ?>" <?php echo ($_GET['polo'] ?? '') == $polo['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($polo['nome']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Curso -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Curso</label>
            <select name="curso" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <?php foreach ($cursos as $curso): ?>
                    <option value="<?php echo $curso['id']; ?>" <?php echo ($_GET['curso'] ?? '') == $curso['id'] ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($curso['nome']); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Mês -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Mês</label>
            <select name="mes" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <?php for ($m = 1; $m <= 12; $m++): ?>
                    <option value="<?php echo sprintf('%02d', $m); ?>" <?php echo ($_GET['mes'] ?? '') == sprintf('%02d', $m) ? 'selected' : ''; ?>>
                        <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>

        <!-- Ano -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Ano</label>
            <select name="ano" class="filtro-auto w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <option value="">Todos</option>
                <?php for ($a = date('Y'); $a >= date('Y') - 3; $a--): ?>
                    <option value="<?php echo $a; ?>" <?php echo ($_GET['ano'] ?? '') == $a ? 'selected' : ''; ?>>
                        <?php echo $a; ?>
                    </option>
                <?php endfor; ?>
            </select>
        </div>

        <!-- Busca -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Buscar Aluno</label>
            <div class="flex">
                <input type="text" name="busca" value="<?php echo htmlspecialchars($_GET['busca'] ?? ''); ?>" 
                       placeholder="Nome do aluno..." 
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-r-lg transition-colors">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Resumo -->
<?php
$total_valor = 0;
$total_pago = 0;
$total_pendente = 0;
$count_total = count($mensalidades);
$count_pago = 0;
$count_pendente = 0;

foreach ($mensalidades as $mensalidade) {
    $total_valor += $mensalidade['valor'];
    if ($mensalidade['status'] === 'pago') {
        $total_pago += $mensalidade['valor_pago'] ?? $mensalidade['valor'];
        $count_pago++;
    } else {
        $total_pendente += $mensalidade['valor'];
        $count_pendente++;
    }
}
?>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Mensalidades</p>
                <p class="text-2xl font-bold text-gray-800"><?php echo $count_total; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_valor, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-graduation-cap text-gray-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Pagas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Mensalidades Pagas</p>
                <p class="text-2xl font-bold text-green-600"><?php echo $count_pago; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_pago, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Pendentes -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Mensalidades Pendentes</p>
                <p class="text-2xl font-bold text-orange-600"><?php echo $count_pendente; ?></p>
                <p class="text-sm text-gray-500 mt-1">
                    R$ <?php echo number_format($total_pendente, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Taxa de Recebimento -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Taxa de Recebimento</p>
                <p class="text-2xl font-bold text-blue-600">
                    <?php echo $count_total > 0 ? number_format(($count_pago / $count_total) * 100, 1) : 0; ?>%
                </p>
                <p class="text-sm text-gray-500 mt-1">
                    <?php echo $count_pago; ?> de <?php echo $count_total; ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-percentage text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Lista de Mensalidades -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-purple-500 mr-2"></i>
                Mensalidades (<?php echo count($mensalidades); ?>)
            </h3>
            <div class="flex items-center space-x-2">
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <a href="relatorios.php?tipo=mensalidades&formato=excel" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </a>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($mensalidades)): ?>
            <div class="text-center py-12">
                <i class="fas fa-graduation-cap text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma mensalidade encontrada</h3>
                <p class="text-gray-500 mb-6">Não há mensalidades que correspondam aos filtros aplicados.</p>
                <a href="mensalidades.php?acao=gerar" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Gerar Mensalidades
                </a>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aluno</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Curso</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Polo</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Período</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vencimento</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($mensalidades as $mensalidade): ?>
                        <?php
                        $status_class = $mensalidade['status'] === 'pago' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800';
                        $status_text = $mensalidade['status'] === 'pago' ? 'Pago' : 'Pendente';
                        ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($mensalidade['aluno_nome']); ?>
                                </div>
                                <div class="text-sm text-gray-500">
                                    CPF: <?php echo htmlspecialchars($mensalidade['cpf']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($mensalidade['curso_nome']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($mensalidade['polo_nome'] ?? 'N/A'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo sprintf('%02d/%04d', $mensalidade['mes'], $mensalidade['ano']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?>
                                <?php if ($mensalidade['status'] === 'pago' && $mensalidade['valor_pago'] != $mensalidade['valor']): ?>
                                    <br><span class="text-xs text-green-600">
                                        Pago: R$ <?php echo number_format($mensalidade['valor_pago'], 2, ',', '.'); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y', strtotime($mensalidade['data_vencimento'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <?php if ($mensalidade['status'] === 'pendente'): ?>
                                        <a href="mensalidades.php?acao=receber&id=<?php echo $mensalidade['id']; ?>" 
                                           class="text-green-600 hover:text-green-900" title="Receber">
                                            <i class="fas fa-dollar-sign"></i>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="boletos.php?mensalidade_id=<?php echo $mensalidade['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-900" title="Gerar Boleto">
                                        <i class="fas fa-barcode"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>
