<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-dollar-sign text-green-500 mr-2"></i>
                Receber Mensalidade
            </h3>
            <a href="mensalidades.php" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-times text-xl"></i>
            </a>
        </div>
    </div>

    <!-- Informações da Mensalidade -->
    <div class="p-6 bg-gray-50 border-b border-gray-200">
        <h4 class="text-md font-semibold text-gray-800 mb-4">Informações da Mensalidade</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-600">Aluno</label>
                <p class="text-sm text-gray-900 font-medium"><?php echo htmlspecialchars($mensalidade['aluno_nome']); ?></p>
                <p class="text-xs text-gray-500">CPF: <?php echo htmlspecialchars($mensalidade['cpf']); ?></p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Curso</label>
                <p class="text-sm text-gray-900"><?php echo htmlspecialchars($mensalidade['curso_nome']); ?></p>
                <?php if ($mensalidade['polo_nome']): ?>
                    <p class="text-xs text-gray-500">Polo: <?php echo htmlspecialchars($mensalidade['polo_nome']); ?></p>
                <?php endif; ?>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Período</label>
                <p class="text-sm text-gray-900 font-semibold">
                    <?php echo sprintf('%02d/%04d', $mensalidade['mes'], $mensalidade['ano']); ?>
                </p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-600">Valor Original</label>
                <p class="text-sm text-gray-900 font-semibold">R$ <?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?></p>
                <p class="text-xs text-gray-500">
                    Vence em: <?php echo date('d/m/Y', strtotime($mensalidade['data_vencimento'])); ?>
                    <?php if ($mensalidade['data_vencimento'] < date('Y-m-d')): ?>
                        <span class="text-red-600">(Vencida)</span>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Formulário de Recebimento -->
    <form method="POST" class="p-6">
        <input type="hidden" name="acao" value="receber_mensalidade">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Valor Recebido -->
            <div>
                <label for="valor_recebido" class="block text-sm font-medium text-gray-700 mb-2">
                    Valor Recebido <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-2 text-gray-500">R$</span>
                    <input type="text" id="valor_recebido" name="valor_recebido" required
                           value="<?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?>"
                           class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                           onkeyup="formatarMoeda(this); calcularDiferenca()">
                </div>
                <div id="diferenca-info" class="mt-1 text-sm"></div>
            </div>

            <!-- Data do Pagamento -->
            <div>
                <label for="data_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                    Data do Pagamento <span class="text-red-500">*</span>
                </label>
                <input type="date" id="data_pagamento" name="data_pagamento" required
                       value="<?php echo date('Y-m-d'); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
            </div>

            <!-- Forma de Pagamento -->
            <div>
                <label for="forma_pagamento" class="block text-sm font-medium text-gray-700 mb-2">
                    Forma de Pagamento <span class="text-red-500">*</span>
                </label>
                <select id="forma_pagamento" name="forma_pagamento" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">Selecione a forma</option>
                    <option value="dinheiro">Dinheiro</option>
                    <option value="pix">PIX</option>
                    <option value="transferencia">Transferência Bancária</option>
                    <option value="debito">Cartão de Débito</option>
                    <option value="credito">Cartão de Crédito</option>
                    <option value="cheque">Cheque</option>
                    <option value="boleto">Boleto Bancário</option>
                    <option value="carnê">Carnê</option>
                    <option value="desconto_folha">Desconto em Folha</option>
                </select>
            </div>

            <!-- Desconto/Multa -->
            <div>
                <label for="desconto_multa" class="block text-sm font-medium text-gray-700 mb-2">
                    Desconto/Multa
                </label>
                <div class="flex space-x-2">
                    <select id="tipo_ajuste" onchange="calcularAjuste()"
                            class="w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        <option value="">Nenhum</option>
                        <option value="desconto">Desconto</option>
                        <option value="multa">Multa</option>
                        <option value="juros">Juros</option>
                    </select>
                    <div class="relative flex-1">
                        <span class="absolute left-3 top-2 text-gray-500">R$</span>
                        <input type="text" id="valor_ajuste" onkeyup="formatarMoeda(this); calcularAjuste()"
                               class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="0,00">
                    </div>
                </div>
            </div>

            <!-- Observações -->
            <div class="md:col-span-2">
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações do Pagamento
                </label>
                <textarea id="observacoes" name="observacoes" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="Informações adicionais sobre o pagamento..."></textarea>
            </div>
        </div>

        <!-- Resumo do Pagamento -->
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 class="text-md font-semibold text-green-800 mb-2">Resumo do Pagamento</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                    <span class="text-green-600">Valor da Mensalidade:</span>
                    <span class="font-semibold text-green-800">R$ <?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?></span>
                </div>
                <div id="ajuste-resumo" style="display: none;">
                    <span id="ajuste-label" class="text-green-600"></span>
                    <span id="ajuste-valor" class="font-semibold"></span>
                </div>
                <div>
                    <span class="text-green-600">Total a Receber:</span>
                    <span id="total-receber" class="font-semibold text-green-800">R$ <?php echo number_format($mensalidade['valor'], 2, ',', '.'); ?></span>
                </div>
                <div>
                    <span class="text-green-600">Status:</span>
                    <span class="font-semibold text-green-800">Será marcado como PAGO</span>
                </div>
            </div>
        </div>

        <!-- Botões -->
        <div class="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
            <a href="mensalidades.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar
            </a>
            
            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                <i class="fas fa-check mr-2"></i>
                Confirmar Recebimento
            </button>
        </div>
    </form>
</div>

<!-- Histórico de Pagamentos (se houver) -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-history text-blue-500 mr-2"></i>
            Histórico do Aluno
        </h3>
    </div>
    <div class="p-6">
        <p class="text-gray-500 text-center py-4">
            <i class="fas fa-info-circle mr-2"></i>
            Histórico de pagamentos do aluno será exibido aqui
        </p>
    </div>
</div>

<script>
const valorOriginal = <?php echo $mensalidade['valor']; ?>;

function calcularDiferenca() {
    const valorRecebidoInput = document.getElementById('valor_recebido');
    const valorRecebidoStr = valorRecebidoInput.value.replace(/\./g, '').replace(',', '.');
    const valorRecebido = parseFloat(valorRecebidoStr) || 0;
    
    const diferenca = valorRecebido - valorOriginal;
    const diferencaInfo = document.getElementById('diferenca-info');
    
    if (diferenca > 0) {
        diferencaInfo.textContent = `Valor R$ ${diferenca.toFixed(2).replace('.', ',')} maior que o original`;
        diferencaInfo.className = 'mt-1 text-sm text-blue-600';
    } else if (diferenca < 0) {
        diferencaInfo.textContent = `Valor R$ ${Math.abs(diferenca).toFixed(2).replace('.', ',')} menor que o original`;
        diferencaInfo.className = 'mt-1 text-sm text-orange-600';
    } else {
        diferencaInfo.textContent = 'Valor igual ao original';
        diferencaInfo.className = 'mt-1 text-sm text-green-600';
    }
    
    atualizarResumo();
}

function calcularAjuste() {
    const tipo = document.getElementById('tipo_ajuste').value;
    const valorAjusteStr = document.getElementById('valor_ajuste').value.replace(/\./g, '').replace(',', '.');
    const valorAjuste = parseFloat(valorAjusteStr) || 0;
    
    const ajusteResumo = document.getElementById('ajuste-resumo');
    const ajusteLabel = document.getElementById('ajuste-label');
    const ajusteValor = document.getElementById('ajuste-valor');
    
    if (tipo && valorAjuste > 0) {
        ajusteResumo.style.display = 'block';
        ajusteLabel.textContent = tipo.charAt(0).toUpperCase() + tipo.slice(1) + ':';
        
        if (tipo === 'desconto') {
            ajusteValor.textContent = '- R$ ' + valorAjuste.toFixed(2).replace('.', ',');
            ajusteValor.className = 'font-semibold text-green-600';
        } else {
            ajusteValor.textContent = '+ R$ ' + valorAjuste.toFixed(2).replace('.', ',');
            ajusteValor.className = 'font-semibold text-red-600';
        }
        
        // Atualizar valor recebido
        let novoValor = valorOriginal;
        if (tipo === 'desconto') {
            novoValor -= valorAjuste;
        } else {
            novoValor += valorAjuste;
        }
        
        document.getElementById('valor_recebido').value = novoValor.toFixed(2).replace('.', ',');
        calcularDiferenca();
    } else {
        ajusteResumo.style.display = 'none';
        document.getElementById('valor_recebido').value = valorOriginal.toFixed(2).replace('.', ',');
        calcularDiferenca();
    }
}

function atualizarResumo() {
    const valorRecebidoStr = document.getElementById('valor_recebido').value.replace(/\./g, '').replace(',', '.');
    const valorRecebido = parseFloat(valorRecebidoStr) || 0;
    
    document.getElementById('total-receber').textContent = 'R$ ' + valorRecebido.toFixed(2).replace('.', ',');
}

// Confirmação antes de enviar
document.querySelector('form').addEventListener('submit', function(e) {
    const valorRecebido = document.getElementById('valor_recebido').value;
    const formaPagamento = document.getElementById('forma_pagamento').value;
    const aluno = '<?php echo addslashes($mensalidade['aluno_nome']); ?>';
    const periodo = '<?php echo sprintf('%02d/%04d', $mensalidade['mes'], $mensalidade['ano']); ?>';
    
    if (!confirm(`Confirma o recebimento da mensalidade?\n\nAluno: ${aluno}\nPeríodo: ${periodo}\nValor: R$ ${valorRecebido}\nForma: ${formaPagamento}`)) {
        e.preventDefault();
    }
});

// Calcular diferença inicial
calcularDiferenca();
</script>
