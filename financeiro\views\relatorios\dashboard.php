<!-- <PERSON>ltros de Período -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-calendar text-blue-500 mr-2"></i>
        Período para Análise
    </h3>
    
    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="tipo" value="dashboard">
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo $data_inicio; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo $data_fim; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Atualizar
            </button>
        </div>
        
        <div class="flex items-end">
            <button type="button" onclick="definirPeriodoRapido('mes')" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                Este Mês
            </button>
        </div>
    </form>
</div>

<!-- Resumo Financeiro -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Receitas do Período</p>
                <p class="text-2xl font-bold text-green-600">
                    R$ <?php echo number_format($dados_relatorio['resumo_mes']['total_receitas'] ?? 0, 2, ',', '.'); ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo number_format($dados_relatorio['resumo_mes']['qtd_receitas'] ?? 0); ?> transações
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Despesas do Período</p>
                <p class="text-2xl font-bold text-red-600">
                    R$ <?php echo number_format($dados_relatorio['resumo_mes']['total_despesas'] ?? 0, 2, ',', '.'); ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo number_format($dados_relatorio['resumo_mes']['qtd_despesas'] ?? 0); ?> transações
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Resultado do Período</p>
                <?php 
                $resultado = ($dados_relatorio['resumo_mes']['total_receitas'] ?? 0) - ($dados_relatorio['resumo_mes']['total_despesas'] ?? 0);
                $cor_resultado = $resultado >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_resultado; ?>">
                    R$ <?php echo number_format($resultado, 2, ',', '.'); ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo $resultado >= 0 ? 'Lucro' : 'Prejuízo'; ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-line text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Margem de Lucro</p>
                <?php 
                $margem = ($dados_relatorio['resumo_mes']['total_receitas'] ?? 0) > 0 
                    ? ($resultado / ($dados_relatorio['resumo_mes']['total_receitas'] ?? 1)) * 100 
                    : 0;
                ?>
                <p class="text-2xl font-bold text-purple-600">
                    <?php echo number_format($margem, 1); ?>%
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-percentage text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Relatórios Disponíveis -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <h3 class="text-lg font-semibold text-gray-800 mb-6">
        <i class="fas fa-chart-bar text-indigo-500 mr-2"></i>
        Relatórios Disponíveis
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- DRE -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-pie text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">DRE</h4>
                    <p class="text-sm text-gray-600">Demonstrativo de Resultados</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Análise detalhada de receitas e despesas por categoria, com apuração do resultado líquido do período.</p>
            <a href="relatorios.php?tipo=dre&data_inicio=<?php echo $data_inicio; ?>&data_fim=<?php echo $data_fim; ?>" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors inline-block">
                <i class="fas fa-eye mr-2"></i>
                Visualizar
            </a>
        </div>

        <!-- Fluxo de Caixa -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">Fluxo de Caixa</h4>
                    <p class="text-sm text-gray-600">Movimentação Diária</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Acompanhamento das entradas e saídas de recursos por dia, com saldo acumulado.</p>
            <a href="relatorios.php?tipo=fluxo_caixa&data_inicio=<?php echo $data_inicio; ?>&data_fim=<?php echo $data_fim; ?>" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors inline-block">
                <i class="fas fa-eye mr-2"></i>
                Visualizar
            </a>
        </div>

        <!-- Contas a Pagar -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-file-invoice text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">Contas a Pagar</h4>
                    <p class="text-sm text-gray-600">Obrigações</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Relatório completo das contas a pagar, com status de pagamento e análise de vencimentos.</p>
            <a href="relatorios.php?tipo=contas_pagar&data_inicio=<?php echo $data_inicio; ?>&data_fim=<?php echo $data_fim; ?>" 
               class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors inline-block">
                <i class="fas fa-eye mr-2"></i>
                Visualizar
            </a>
        </div>

        <!-- Contas a Receber -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-hand-holding-usd text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">Contas a Receber</h4>
                    <p class="text-sm text-gray-600">Direitos</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Relatório das contas a receber, com análise de inadimplência e projeções de recebimento.</p>
            <a href="relatorios.php?tipo=contas_receber&data_inicio=<?php echo $data_inicio; ?>&data_fim=<?php echo $data_fim; ?>" 
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors inline-block">
                <i class="fas fa-eye mr-2"></i>
                Visualizar
            </a>
        </div>

        <!-- Mensalidades -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">Mensalidades</h4>
                    <p class="text-sm text-gray-600">Receitas Acadêmicas</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Análise das mensalidades por curso, polo e período, com taxa de recebimento.</p>
            <a href="relatorios.php?tipo=mensalidades&mes=<?php echo date('m'); ?>&ano=<?php echo date('Y'); ?>" 
               class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors inline-block">
                <i class="fas fa-eye mr-2"></i>
                Visualizar
            </a>
        </div>

        <!-- Personalizado -->
        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-filter text-indigo-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="text-lg font-semibold text-gray-800">Personalizado</h4>
                    <p class="text-sm text-gray-600">Filtros Avançados</p>
                </div>
            </div>
            <p class="text-gray-600 mb-4">Crie relatórios personalizados com filtros específicos por categoria, período e tipo.</p>
            <button onclick="alert('Funcionalidade em desenvolvimento')" 
                    class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-cog mr-2"></i>
                Configurar
            </button>
        </div>
    </div>
</div>

<!-- Dicas -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-lightbulb text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Dicas para Análise Financeira</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Compare períodos similares para identificar tendências</li>
                    <li>Analise a margem de lucro para avaliar a eficiência operacional</li>
                    <li>Monitore o fluxo de caixa para garantir liquidez</li>
                    <li>Acompanhe as contas a receber para reduzir inadimplência</li>
                    <li>Use os relatórios para tomada de decisões estratégicas</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function definirPeriodoRapido(tipo) {
    const hoje = new Date();
    let dataInicio, dataFim;
    
    if (tipo === 'mes') {
        dataInicio = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
        dataFim = hoje;
    }
    
    document.querySelector('input[name="data_inicio"]').value = dataInicio.toISOString().split('T')[0];
    document.querySelector('input[name="data_fim"]').value = dataFim.toISOString().split('T')[0];
    
    // Submit automático
    document.querySelector('form').submit();
}
</script>
