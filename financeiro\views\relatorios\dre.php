<!-- <PERSON>ltros de Período -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-calendar text-blue-500 mr-2"></i>
        Período do Relatório
    </h3>
    
    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="tipo" value="dre">
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo $data_inicio; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo $data_fim; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Atualizar
            </button>
        </div>
        
        <div class="flex items-end">
            <button type="button" onclick="definirPeriodoMes()" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                Este Mês
            </button>
        </div>
    </form>
</div>

<!-- Cabeçalho do DRE -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-6 text-center border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-800">DEMONSTRATIVO DE RESULTADOS DO EXERCÍCIO</h1>
        <h2 class="text-lg text-gray-600 mt-2">Instituto de Ensino Pesquisa e Gestão S/S LTDA</h2>
        <p class="text-sm text-gray-500 mt-1">
            Período: <?php echo date('d/m/Y', strtotime($data_inicio)); ?> a <?php echo date('d/m/Y', strtotime($data_fim)); ?>
        </p>
        <p class="text-xs text-gray-400 mt-1">
            Gerado em: <?php echo date('d/m/Y H:i'); ?>
        </p>
    </div>
</div>

<!-- DRE -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="overflow-x-auto">
        <table class="min-w-full">
            <tbody class="divide-y divide-gray-200">
                <!-- RECEITAS OPERACIONAIS -->
                <tr class="bg-green-50">
                    <td class="px-6 py-4 text-sm font-bold text-green-800 uppercase">
                        RECEITAS OPERACIONAIS
                    </td>
                    <td class="px-6 py-4 text-sm font-bold text-green-800 text-right">
                        R$ <?php echo number_format($dados_relatorio['total_receitas'], 2, ',', '.'); ?>
                    </td>
                </tr>
                
                <?php if (!empty($dados_relatorio['receitas'])): ?>
                    <?php foreach ($dados_relatorio['receitas'] as $receita): ?>
                        <tr>
                            <td class="px-6 py-3 text-sm text-gray-700 pl-12">
                                <?php echo htmlspecialchars($receita['categoria'] ?? 'Sem categoria'); ?>
                            </td>
                            <td class="px-6 py-3 text-sm text-gray-900 text-right">
                                R$ <?php echo number_format($receita['total'], 2, ',', '.'); ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td class="px-6 py-3 text-sm text-gray-500 pl-12 italic">
                            Nenhuma receita no período
                        </td>
                        <td class="px-6 py-3 text-sm text-gray-500 text-right">
                            R$ 0,00
                        </td>
                    </tr>
                <?php endif; ?>

                <!-- RECEITA OPERACIONAL LÍQUIDA -->
                <tr class="bg-green-100 border-t-2 border-green-200">
                    <td class="px-6 py-4 text-sm font-bold text-green-800">
                        RECEITA OPERACIONAL LÍQUIDA
                    </td>
                    <td class="px-6 py-4 text-sm font-bold text-green-800 text-right">
                        R$ <?php echo number_format($dados_relatorio['total_receitas'], 2, ',', '.'); ?>
                    </td>
                </tr>

                <!-- DESPESAS OPERACIONAIS -->
                <tr class="bg-red-50 border-t-4 border-gray-300">
                    <td class="px-6 py-4 text-sm font-bold text-red-800 uppercase">
                        (-) DESPESAS OPERACIONAIS
                    </td>
                    <td class="px-6 py-4 text-sm font-bold text-red-800 text-right">
                        (R$ <?php echo number_format($dados_relatorio['total_despesas'], 2, ',', '.'); ?>)
                    </td>
                </tr>

                <?php if (!empty($dados_relatorio['despesas'])): ?>
                    <?php foreach ($dados_relatorio['despesas'] as $despesa): ?>
                        <tr>
                            <td class="px-6 py-3 text-sm text-gray-700 pl-12">
                                <?php echo htmlspecialchars($despesa['categoria'] ?? 'Sem categoria'); ?>
                            </td>
                            <td class="px-6 py-3 text-sm text-gray-900 text-right">
                                (R$ <?php echo number_format($despesa['total'], 2, ',', '.'); ?>)
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td class="px-6 py-3 text-sm text-gray-500 pl-12 italic">
                            Nenhuma despesa no período
                        </td>
                        <td class="px-6 py-3 text-sm text-gray-500 text-right">
                            R$ 0,00
                        </td>
                    </tr>
                <?php endif; ?>

                <!-- RESULTADO OPERACIONAL -->
                <?php 
                $resultado_operacional = $dados_relatorio['total_receitas'] - $dados_relatorio['total_despesas'];
                $cor_resultado = $resultado_operacional >= 0 ? 'text-green-800 bg-green-100' : 'text-red-800 bg-red-100';
                ?>
                <tr class="<?php echo $cor_resultado; ?> border-t-2 border-gray-300">
                    <td class="px-6 py-4 text-sm font-bold">
                        RESULTADO OPERACIONAL
                    </td>
                    <td class="px-6 py-4 text-sm font-bold text-right">
                        <?php if ($resultado_operacional < 0): ?>
                            (R$ <?php echo number_format(abs($resultado_operacional), 2, ',', '.'); ?>)
                        <?php else: ?>
                            R$ <?php echo number_format($resultado_operacional, 2, ',', '.'); ?>
                        <?php endif; ?>
                    </td>
                </tr>

                <!-- RESULTADO LÍQUIDO DO EXERCÍCIO -->
                <tr class="<?php echo $cor_resultado; ?> border-t-4 border-gray-400">
                    <td class="px-6 py-5 text-lg font-bold">
                        RESULTADO LÍQUIDO DO EXERCÍCIO
                    </td>
                    <td class="px-6 py-5 text-lg font-bold text-right">
                        <?php if ($dados_relatorio['resultado'] < 0): ?>
                            (R$ <?php echo number_format(abs($dados_relatorio['resultado']), 2, ',', '.'); ?>)
                        <?php else: ?>
                            R$ <?php echo number_format($dados_relatorio['resultado'], 2, ',', '.'); ?>
                        <?php endif; ?>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Análise e Indicadores -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
    <!-- Margem Operacional -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Margem Operacional</p>
                <?php 
                $margem_operacional = $dados_relatorio['total_receitas'] > 0 
                    ? ($resultado_operacional / $dados_relatorio['total_receitas']) * 100 
                    : 0;
                ?>
                <p class="text-2xl font-bold text-blue-600">
                    <?php echo number_format($margem_operacional, 1); ?>%
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-percentage text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Participação das Despesas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Despesas/Receitas</p>
                <?php 
                $participacao_despesas = $dados_relatorio['total_receitas'] > 0 
                    ? ($dados_relatorio['total_despesas'] / $dados_relatorio['total_receitas']) * 100 
                    : 0;
                ?>
                <p class="text-2xl font-bold text-red-600">
                    <?php echo number_format($participacao_despesas, 1); ?>%
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-pie text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Resultado por Dia -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Resultado/Dia</p>
                <?php 
                $dias_periodo = (strtotime($data_fim) - strtotime($data_inicio)) / (60 * 60 * 24) + 1;
                $resultado_dia = $dias_periodo > 0 ? $dados_relatorio['resultado'] / $dias_periodo : 0;
                $cor_dia = $resultado_dia >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_dia; ?>">
                    R$ <?php echo number_format(abs($resultado_dia), 0, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-calendar-day text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Observações -->
<div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Observações sobre o DRE</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Este demonstrativo considera apenas as transações registradas no sistema</li>
                    <li>Valores entre parênteses representam despesas ou prejuízos</li>
                    <li>A margem operacional indica a eficiência da operação</li>
                    <li>Para análises mais detalhadas, consulte os relatórios por categoria</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function definirPeriodoMes() {
    const hoje = new Date();
    const dataInicio = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
    const dataFim = hoje;
    
    document.querySelector('input[name="data_inicio"]').value = dataInicio.toISOString().split('T')[0];
    document.querySelector('input[name="data_fim"]').value = dataFim.toISOString().split('T')[0];
    
    // Submit automático
    document.querySelector('form').submit();
}

// Imprimir apenas o DRE
function imprimirDRE() {
    window.print();
}
</script>

<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .bg-white {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
</style>
