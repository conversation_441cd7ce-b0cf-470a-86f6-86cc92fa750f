<!-- Filtros de Período -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-calendar text-blue-500 mr-2"></i>
        Período do Fluxo de Caixa
    </h3>
    
    <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <input type="hidden" name="tipo" value="fluxo_caixa">
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo $data_inicio; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo $data_fim; ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        
        <div class="flex items-end">
            <button type="submit" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Atualizar
            </button>
        </div>
        
        <div class="flex items-end">
            <button type="button" onclick="definirPeriodo30Dias()" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                Últimos 30 Dias
            </button>
        </div>
    </form>
</div>

<!-- Resumo do Período -->
<?php
$total_receitas = 0;
$total_despesas = 0;
$melhor_dia = null;
$pior_dia = null;

if (!empty($dados_relatorio['fluxo'])) {
    foreach ($dados_relatorio['fluxo'] as $dia) {
        $total_receitas += $dia['receitas'];
        $total_despesas += $dia['despesas'];
        
        if ($melhor_dia === null || $dia['saldo_dia'] > $melhor_dia['saldo_dia']) {
            $melhor_dia = $dia;
        }
        if ($pior_dia === null || $dia['saldo_dia'] < $pior_dia['saldo_dia']) {
            $pior_dia = $dia;
        }
    }
}
?>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Receitas</p>
                <p class="text-2xl font-bold text-green-600">
                    R$ <?php echo number_format($total_receitas, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Despesas</p>
                <p class="text-2xl font-bold text-red-600">
                    R$ <?php echo number_format($total_despesas, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Melhor Dia</p>
                <p class="text-lg font-bold text-green-600">
                    <?php if ($melhor_dia): ?>
                        R$ <?php echo number_format($melhor_dia['saldo_dia'], 2, ',', '.'); ?>
                    <?php else: ?>
                        R$ 0,00
                    <?php endif; ?>
                </p>
                <p class="text-xs text-gray-500">
                    <?php echo $melhor_dia ? date('d/m/Y', strtotime($melhor_dia['data'])) : 'N/A'; ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-trophy text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Saldo Final</p>
                <?php 
                $saldo_final = !empty($dados_relatorio['fluxo']) ? end($dados_relatorio['fluxo'])['saldo_acumulado'] : 0;
                $cor_saldo = $saldo_final >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_saldo; ?>">
                    R$ <?php echo number_format($saldo_final, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-wallet text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Gráfico de Fluxo de Caixa -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
            Evolução do Fluxo de Caixa
        </h3>
    </div>
    <div class="p-6">
        <canvas id="fluxoCaixaChart" width="400" height="200"></canvas>
    </div>
</div>

<!-- Tabela Detalhada -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-table text-blue-500 mr-2"></i>
                Fluxo de Caixa Detalhado
            </h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">
                    Período: <?php echo date('d/m/Y', strtotime($data_inicio)); ?> a <?php echo date('d/m/Y', strtotime($data_fim)); ?>
                </span>
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($dados_relatorio['fluxo'])): ?>
            <div class="text-center py-12">
                <i class="fas fa-chart-line text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma movimentação encontrada</h3>
                <p class="text-gray-500 mb-6">Não há transações no período selecionado.</p>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receitas</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Despesas</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo do Dia</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo Acumulado</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variação</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php 
                    $saldo_anterior = 0;
                    foreach ($dados_relatorio['fluxo'] as $index => $dia): 
                        $variacao = $index > 0 ? $dia['saldo_acumulado'] - $saldo_anterior : 0;
                        $saldo_anterior = $dia['saldo_acumulado'];
                    ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <?php echo date('d/m/Y', strtotime($dia['data'])); ?>
                                <div class="text-xs text-gray-500">
                                    <?php echo ucfirst(strftime('%A', strtotime($dia['data']))); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                                R$ <?php echo number_format($dia['receitas'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                                R$ <?php echo number_format($dia['despesas'], 2, ',', '.'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php 
                                $cor_saldo_dia = $dia['saldo_dia'] >= 0 ? 'text-green-600' : 'text-red-600';
                                ?>
                                <span class="<?php echo $cor_saldo_dia; ?>">
                                    <?php if ($dia['saldo_dia'] < 0): ?>
                                        - R$ <?php echo number_format(abs($dia['saldo_dia']), 2, ',', '.'); ?>
                                    <?php else: ?>
                                        R$ <?php echo number_format($dia['saldo_dia'], 2, ',', '.'); ?>
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold">
                                <?php 
                                $cor_saldo_acum = $dia['saldo_acumulado'] >= 0 ? 'text-green-600' : 'text-red-600';
                                ?>
                                <span class="<?php echo $cor_saldo_acum; ?>">
                                    R$ <?php echo number_format($dia['saldo_acumulado'], 2, ',', '.'); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <?php if ($index > 0): ?>
                                    <?php 
                                    $cor_variacao = $variacao >= 0 ? 'text-green-600' : 'text-red-600';
                                    $icone_variacao = $variacao >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                                    ?>
                                    <span class="<?php echo $cor_variacao; ?>">
                                        <i class="fas <?php echo $icone_variacao; ?> mr-1"></i>
                                        R$ <?php echo number_format(abs($variacao), 2, ',', '.'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-gray-50">
                    <tr>
                        <td class="px-6 py-4 text-sm font-bold text-gray-900">TOTAIS</td>
                        <td class="px-6 py-4 text-sm font-bold text-green-600">
                            R$ <?php echo number_format($total_receitas, 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4 text-sm font-bold text-red-600">
                            R$ <?php echo number_format($total_despesas, 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4 text-sm font-bold">
                            <?php 
                            $saldo_total = $total_receitas - $total_despesas;
                            $cor_total = $saldo_total >= 0 ? 'text-green-600' : 'text-red-600';
                            ?>
                            <span class="<?php echo $cor_total; ?>">
                                R$ <?php echo number_format($saldo_total, 2, ',', '.'); ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-bold <?php echo $cor_saldo; ?>">
                            R$ <?php echo number_format($saldo_final, 2, ',', '.'); ?>
                        </td>
                        <td class="px-6 py-4"></td>
                    </tr>
                </tfoot>
            </table>
        <?php endif; ?>
    </div>
</div>

<script>
// Gráfico de Fluxo de Caixa
const ctx = document.getElementById('fluxoCaixaChart').getContext('2d');
const fluxoData = <?php echo json_encode($dados_relatorio['fluxo'] ?? []); ?>;

const labels = fluxoData.map(item => {
    const date = new Date(item.data);
    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
});

const receitas = fluxoData.map(item => parseFloat(item.receitas) || 0);
const despesas = fluxoData.map(item => parseFloat(item.despesas) || 0);
const saldoAcumulado = fluxoData.map(item => parseFloat(item.saldo_acumulado) || 0);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: 'Receitas',
            data: receitas,
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1,
            fill: false
        }, {
            label: 'Despesas',
            data: despesas,
            borderColor: 'rgb(239, 68, 68)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.1,
            fill: false
        }, {
            label: 'Saldo Acumulado',
            data: saldoAcumulado,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1,
            fill: true,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                position: 'left',
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                grid: {
                    drawOnChartArea: false,
                },
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': R$ ' + context.parsed.y.toLocaleString('pt-BR');
                    }
                }
            }
        }
    }
});

function definirPeriodo30Dias() {
    const hoje = new Date();
    const dataInicio = new Date(hoje.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.querySelector('input[name="data_inicio"]').value = dataInicio.toISOString().split('T')[0];
    document.querySelector('input[name="data_fim"]').value = hoje.toISOString().split('T')[0];
    
    // Submit automático
    document.querySelector('form').submit();
}
</script>
