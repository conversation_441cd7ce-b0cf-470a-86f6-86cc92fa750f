<!-- Saldos por Conta -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
    <?php
    $total_geral = 0;
    $contas_padrao = [
        'dinheiro' => ['nome' => 'Caixa', 'icon' => 'fa-coins', 'color' => 'green'],
        'pix' => ['nome' => 'PIX', 'icon' => 'fa-mobile-alt', 'color' => 'blue'],
        'transferencia' => ['nome' => 'Transferência', 'icon' => 'fa-exchange-alt', 'color' => 'purple'],
        'debito' => ['nome' => 'Cartão Débito', 'icon' => 'fa-credit-card', 'color' => 'indigo'],
        'credito' => ['nome' => 'Cartão Crédito', 'icon' => 'fa-credit-card', 'color' => 'pink']
    ];
    
    $saldos_organizados = [];
    foreach ($saldos as $saldo) {
        $saldos_organizados[$saldo['forma_pagamento']] = $saldo['saldo'];
        $total_geral += $saldo['saldo'];
    }
    ?>
    
    <?php foreach ($contas_padrao as $forma => $config): ?>
        <?php $saldo = $saldos_organizados[$forma] ?? 0; ?>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600"><?php echo $config['nome']; ?></p>
                    <p class="text-2xl font-bold text-<?php echo $config['color']; ?>-600">
                        R$ <?php echo number_format($saldo, 2, ',', '.'); ?>
                    </p>
                </div>
                <div class="w-12 h-12 bg-<?php echo $config['color']; ?>-100 rounded-lg flex items-center justify-center">
                    <i class="fas <?php echo $config['icon']; ?> text-<?php echo $config['color']; ?>-600 text-xl"></i>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- Resumo do Mês -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total em Caixa</p>
                <p class="text-2xl font-bold text-gray-800">
                    R$ <?php echo number_format($total_geral, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-wallet text-gray-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Receitas do Mês</p>
                <p class="text-2xl font-bold text-green-600">
                    R$ <?php echo number_format($resumo_mes['total_receitas'] ?? 0, 2, ',', '.'); ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo number_format($resumo_mes['qtd_receitas'] ?? 0); ?> transações
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Despesas do Mês</p>
                <p class="text-2xl font-bold text-red-600">
                    R$ <?php echo number_format($resumo_mes['total_despesas'] ?? 0, 2, ',', '.'); ?>
                </p>
                <p class="text-xs text-gray-500 mt-1">
                    <?php echo number_format($resumo_mes['qtd_despesas'] ?? 0); ?> transações
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Resultado do Mês</p>
                <?php 
                $resultado = ($resumo_mes['total_receitas'] ?? 0) - ($resumo_mes['total_despesas'] ?? 0);
                $cor_resultado = $resultado >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_resultado; ?>">
                    R$ <?php echo number_format($resultado, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-chart-line text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-bolt text-yellow-500 mr-2"></i>
        Ações Rápidas
    </h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button onclick="abrirModal('entrada')" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-green-700">Registrar Entrada</span>
        </button>
        <button onclick="abrirModal('saida')" class="flex flex-col items-center p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-minus text-red-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-red-700">Registrar Saída</span>
        </button>
        <button onclick="abrirModal('transferencia')" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-exchange-alt text-blue-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-blue-700">Transferência</span>
        </button>
        <a href="tesouraria.php?acao=extrato" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
            <i class="fas fa-list text-purple-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-purple-700">Ver Extrato</span>
        </a>
    </div>
</div>

<!-- Movimentações de Hoje -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-history text-blue-500 mr-2"></i>
            Movimentações de Hoje
        </h3>
    </div>
    <div class="p-6">
        <?php if (empty($movimentacoes_hoje)): ?>
            <div class="text-center py-8">
                <i class="fas fa-receipt text-gray-300 text-4xl mb-4"></i>
                <p class="text-gray-500">Nenhuma movimentação registrada hoje</p>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($movimentacoes_hoje as $mov): ?>
                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg flex items-center justify-center <?php echo $mov['tipo'] == 'receita' ? 'bg-green-100' : 'bg-red-100'; ?>">
                                <i class="fas <?php echo $mov['tipo'] == 'receita' ? 'fa-plus text-green-600' : 'fa-minus text-red-600'; ?>"></i>
                            </div>
                            <div class="ml-4">
                                <p class="font-medium text-gray-800"><?php echo htmlspecialchars($mov['descricao']); ?></p>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($mov['forma_pagamento']); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold <?php echo $mov['tipo'] == 'receita' ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $mov['tipo'] == 'receita' ? '+' : '-'; ?>
                                R$ <?php echo number_format($mov['valor'], 2, ',', '.'); ?>
                            </p>
                            <p class="text-xs text-gray-500"><?php echo $mov['hora']; ?></p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para Movimentações -->
<div id="modal-movimentacao" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="modal-titulo" class="text-lg font-semibold text-gray-800"></h3>
                    <button onclick="fecharModal()" class="text-gray-600 hover:text-gray-800">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <form method="POST" class="p-6">
                <input type="hidden" id="modal-acao" name="acao" value="">
                <input type="hidden" id="modal-tipo" name="tipo" value="">
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                        <input type="text" name="descricao" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Descrição da movimentação">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Valor</label>
                        <div class="relative">
                            <span class="absolute left-3 top-2 text-gray-500">R$</span>
                            <input type="text" name="valor" required
                                   class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="0,00"
                                   onkeyup="formatarMoeda(this)">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Forma de Pagamento</label>
                        <select name="forma_pagamento" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Selecione</option>
                            <option value="dinheiro">Dinheiro</option>
                            <option value="pix">PIX</option>
                            <option value="transferencia">Transferência</option>
                            <option value="debito">Cartão Débito</option>
                            <option value="credito">Cartão Crédito</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Categoria</label>
                        <select name="categoria_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Selecione uma categoria</option>
                            <?php foreach ($categorias as $categoria): ?>
                                <option value="<?php echo $categoria['id']; ?>">
                                    <?php echo htmlspecialchars($categoria['nome']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Observações</label>
                        <textarea name="observacoes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Observações adicionais..."></textarea>
                    </div>
                </div>
                
                <div class="flex items-center justify-end mt-6 pt-6 border-t border-gray-200 space-x-4">
                    <button type="button" onclick="fecharModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                        Cancelar
                    </button>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                        Registrar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function abrirModal(tipo) {
    const modal = document.getElementById('modal-movimentacao');
    const titulo = document.getElementById('modal-titulo');
    const acao = document.getElementById('modal-acao');
    const tipoInput = document.getElementById('modal-tipo');
    
    if (tipo === 'entrada') {
        titulo.textContent = 'Registrar Entrada';
        acao.value = 'movimentacao';
        tipoInput.value = 'entrada';
    } else if (tipo === 'saida') {
        titulo.textContent = 'Registrar Saída';
        acao.value = 'movimentacao';
        tipoInput.value = 'saida';
    }
    
    modal.classList.remove('hidden');
}

function fecharModal() {
    document.getElementById('modal-movimentacao').classList.add('hidden');
}

// Fechar modal ao clicar fora
document.getElementById('modal-movimentacao').addEventListener('click', function(e) {
    if (e.target === this) {
        fecharModal();
    }
});
</script>
