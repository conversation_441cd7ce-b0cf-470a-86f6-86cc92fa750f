<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-filter text-blue-500 mr-2"></i>
        Filtros do Extrato
    </h3>
    
    <form id="form-filtros" method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <input type="hidden" name="acao" value="extrato">
        
        <!-- Data Início -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
            <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($data_inicio); ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        </div>

        <!-- Data Fim -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
            <input type="date" name="data_fim" value="<?php echo htmlspecialchars($data_fim); ?>" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
        </div>

        <!-- Forma de Pagamento -->
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Forma de Pagamento</label>
            <select name="forma_pagamento" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Todas</option>
                <option value="dinheiro" <?php echo ($forma_pagamento ?? '') === 'dinheiro' ? 'selected' : ''; ?>>Dinheiro</option>
                <option value="pix" <?php echo ($forma_pagamento ?? '') === 'pix' ? 'selected' : ''; ?>>PIX</option>
                <option value="transferencia" <?php echo ($forma_pagamento ?? '') === 'transferencia' ? 'selected' : ''; ?>>Transferência</option>
                <option value="debito" <?php echo ($forma_pagamento ?? '') === 'debito' ? 'selected' : ''; ?>>Cartão Débito</option>
                <option value="credito" <?php echo ($forma_pagamento ?? '') === 'credito' ? 'selected' : ''; ?>>Cartão Crédito</option>
            </select>
        </div>

        <!-- Botão Filtrar -->
        <div class="flex items-end">
            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-search mr-2"></i>
                Filtrar
            </button>
        </div>
    </form>
</div>

<!-- Resumo do Período -->
<?php
$total_receitas = 0;
$total_despesas = 0;
$saldo_inicial = 0;

if (!empty($extrato)) {
    foreach ($extrato as $transacao) {
        if ($transacao['tipo'] === 'receita') {
            $total_receitas += $transacao['valor'];
        } else {
            $total_despesas += $transacao['valor'];
        }
    }
    $saldo_final = end($extrato)['saldo_acumulado'] ?? 0;
}
?>

<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Receitas</p>
                <p class="text-2xl font-bold text-green-600">
                    R$ <?php echo number_format($total_receitas, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Despesas</p>
                <p class="text-2xl font-bold text-red-600">
                    R$ <?php echo number_format($total_despesas, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Saldo do Período</p>
                <?php 
                $saldo_periodo = $total_receitas - $total_despesas;
                $cor_saldo = $saldo_periodo >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_saldo; ?>">
                    R$ <?php echo number_format($saldo_periodo, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-balance-scale text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Saldo Final</p>
                <?php 
                $cor_final = ($saldo_final ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';
                ?>
                <p class="text-2xl font-bold <?php echo $cor_final; ?>">
                    R$ <?php echo number_format($saldo_final ?? 0, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-wallet text-purple-600 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Extrato -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-blue-500 mr-2"></i>
                Extrato de Movimentações
            </h3>
            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">
                    Período: <?php echo date('d/m/Y', strtotime($data_inicio)); ?> a <?php echo date('d/m/Y', strtotime($data_fim)); ?>
                </span>
                <button onclick="window.print()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-print mr-2"></i>
                    Imprimir
                </button>
                <button onclick="exportarExtrato()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    Excel
                </button>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <?php if (empty($extrato)): ?>
            <div class="text-center py-12">
                <i class="fas fa-receipt text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma movimentação encontrada</h3>
                <p class="text-gray-500 mb-6">Não há transações no período selecionado.</p>
                <button onclick="abrirModal('entrada')" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    Registrar Primeira Movimentação
                </button>
            </div>
        <?php else: ?>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Forma</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entrada</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saída</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Saldo</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($extrato as $transacao): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y H:i', strtotime($transacao['data_transacao'])); ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="font-medium"><?php echo htmlspecialchars($transacao['descricao']); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    <?php echo ucfirst($transacao['forma_pagamento']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php if ($transacao['tipo'] === 'receita'): ?>
                                    <span class="text-green-600">
                                        + R$ <?php echo number_format($transacao['valor'], 2, ',', '.'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php if ($transacao['tipo'] === 'despesa'): ?>
                                    <span class="text-red-600">
                                        - R$ <?php echo number_format($transacao['valor'], 2, ',', '.'); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php 
                                $saldo = $transacao['saldo_acumulado'];
                                $cor_saldo = $saldo >= 0 ? 'text-green-600' : 'text-red-600';
                                ?>
                                <span class="<?php echo $cor_saldo; ?>">
                                    R$ <?php echo number_format($saldo, 2, ',', '.'); ?>
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">
        <i class="fas fa-bolt text-yellow-500 mr-2"></i>
        Ações Rápidas
    </h3>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <button onclick="abrirModal('entrada')" class="flex flex-col items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-plus text-green-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-green-700">Nova Entrada</span>
        </button>
        <button onclick="abrirModal('saida')" class="flex flex-col items-center p-4 bg-red-50 hover:bg-red-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-minus text-red-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-red-700">Nova Saída</span>
        </button>
        <button onclick="definirPeriodoRapido('hoje')" class="flex flex-col items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-calendar-day text-blue-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-blue-700">Hoje</span>
        </button>
        <button onclick="definirPeriodoRapido('mes')" class="flex flex-col items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors cursor-pointer">
            <i class="fas fa-calendar text-purple-600 text-2xl mb-2"></i>
            <span class="text-sm font-medium text-purple-700">Este Mês</span>
        </button>
    </div>
</div>

<script>
function definirPeriodoRapido(tipo) {
    const hoje = new Date();
    let dataInicio, dataFim;
    
    if (tipo === 'hoje') {
        dataInicio = dataFim = hoje;
    } else if (tipo === 'mes') {
        dataInicio = new Date(hoje.getFullYear(), hoje.getMonth(), 1);
        dataFim = hoje;
    }
    
    document.querySelector('input[name="data_inicio"]').value = dataInicio.toISOString().split('T')[0];
    document.querySelector('input[name="data_fim"]').value = dataFim.toISOString().split('T')[0];
    
    // Submit automático
    document.getElementById('form-filtros').submit();
}

function exportarExtrato() {
    const params = new URLSearchParams(window.location.search);
    params.set('formato', 'excel');
    window.location.href = 'tesouraria.php?' + params.toString();
}

function abrirModal(tipo) {
    // Redirecionar para o dashboard com modal
    window.location.href = 'tesouraria.php?acao=dashboard&modal=' + tipo;
}

// Auto-submit quando mudar as datas
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="date"], select[name="forma_pagamento"]');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Delay para permitir múltiplas mudanças
            clearTimeout(this.submitTimeout);
            this.submitTimeout = setTimeout(() => {
                document.getElementById('form-filtros').submit();
            }, 500);
        });
    });
});
</script>
