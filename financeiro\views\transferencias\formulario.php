<!-- Formulário de Transferência -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-plus text-blue-500 mr-2"></i>
            <?php echo isset($transferencia) ? 'Editar Transferência' : 'Nova Transferência'; ?>
        </h3>
    </div>
    
    <div class="p-6">
        <form method="POST" class="space-y-6">
            <input type="hidden" name="form_tipo" value="transferencia">
            <input type="hidden" name="id" value="<?php echo $transferencia['id'] ?? ''; ?>">
            
            <!-- Tipo de Operação -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="tipo_operacao" class="block text-sm font-medium text-gray-700 mb-2">
                        Tipo de Operação <span class="text-red-500">*</span>
                    </label>
                    <select id="tipo_operacao" name="tipo_operacao" required onchange="ajustarCampos()"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione o tipo</option>
                        <option value="pix_enviado" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'pix_enviado' ? 'selected' : ''; ?>>PIX Enviado</option>
                        <option value="pix_recebido" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'pix_recebido' ? 'selected' : ''; ?>>PIX Recebido</option>
                        <option value="ted_enviado" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'ted_enviado' ? 'selected' : ''; ?>>TED Enviado</option>
                        <option value="ted_recebido" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'ted_recebido' ? 'selected' : ''; ?>>TED Recebido</option>
                        <option value="doc_enviado" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'doc_enviado' ? 'selected' : ''; ?>>DOC Enviado</option>
                        <option value="doc_recebido" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'doc_recebido' ? 'selected' : ''; ?>>DOC Recebido</option>
                        <option value="transferencia" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'transferencia' ? 'selected' : ''; ?>>Transferência entre Contas</option>
                        <option value="deposito" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'deposito' ? 'selected' : ''; ?>>Depósito</option>
                        <option value="saque" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'saque' ? 'selected' : ''; ?>>Saque</option>
                        <option value="tarifa" <?php echo ($transferencia['tipo_operacao'] ?? '') === 'tarifa' ? 'selected' : ''; ?>>Tarifa Bancária</option>
                    </select>
                </div>

                <!-- Valor -->
                <div>
                    <label for="valor" class="block text-sm font-medium text-gray-700 mb-2">
                        Valor <span class="text-red-500">*</span>
                    </label>
                    <input type="number" id="valor" name="valor" step="0.01" min="0" required
                           value="<?php echo $transferencia['valor'] ?? ''; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0,00">
                </div>
            </div>

            <!-- Contas -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Conta Origem (para operações de saída) -->
                <div id="campo_conta_origem">
                    <label for="conta_origem_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <span id="label_conta_origem">Conta Origem</span>
                    </label>
                    <select id="conta_origem_id" name="conta_origem_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione a conta</option>
                        <?php foreach ($contas_bancarias as $conta): ?>
                            <option value="<?php echo $conta['id']; ?>" <?php echo ($transferencia['conta_origem_id'] ?? '') == $conta['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($conta['nome'] . ' - ' . $conta['banco']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p id="help_conta_origem" class="mt-1 text-xs text-gray-500"></p>
                </div>

                <!-- Conta Destino (apenas para transferências internas) -->
                <div id="campo_conta_destino">
                    <label for="conta_destino_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <span id="label_conta_destino">Conta Destino</span>
                    </label>
                    <select id="conta_destino_id" name="conta_destino_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione a conta</option>
                        <?php foreach ($contas_bancarias as $conta): ?>
                            <option value="<?php echo $conta['id']; ?>" <?php echo ($transferencia['conta_destino_id'] ?? '') == $conta['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($conta['nome'] . ' - ' . $conta['banco']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p id="help_conta_destino" class="mt-1 text-xs text-gray-500"></p>
                </div>
            </div>

            <!-- Descrição -->
            <div>
                <label for="descricao" class="block text-sm font-medium text-gray-700 mb-2">
                    Descrição <span class="text-red-500">*</span>
                </label>
                <input type="text" id="descricao" name="descricao" required
                       value="<?php echo htmlspecialchars($transferencia['descricao'] ?? ''); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Descrição da operação">
            </div>

            <!-- Data e Hora -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="data_operacao" class="block text-sm font-medium text-gray-700 mb-2">
                        Data da Operação <span class="text-red-500">*</span>
                    </label>
                    <input type="date" id="data_operacao" name="data_operacao" required
                           value="<?php echo isset($transferencia['data_operacao']) ? date('Y-m-d', strtotime($transferencia['data_operacao'])) : date('Y-m-d'); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>

                <div>
                    <label for="hora_operacao" class="block text-sm font-medium text-gray-700 mb-2">
                        Hora da Operação
                    </label>
                    <input type="time" id="hora_operacao" name="hora_operacao"
                           value="<?php echo isset($transferencia['data_operacao']) ? date('H:i', strtotime($transferencia['data_operacao'])) : date('H:i'); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>

            <!-- Campos específicos para PIX -->
            <div id="campos_pix" class="hidden">
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-semibold text-gray-800 mb-4">Dados do PIX</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="chave_pix" class="block text-sm font-medium text-gray-700 mb-2">
                                Chave PIX
                            </label>
                            <input type="text" id="chave_pix" name="chave_pix"
                                   value="<?php echo htmlspecialchars($transferencia['chave_pix'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="CPF, email, telefone ou chave aleatória">
                        </div>

                        <div>
                            <label for="nome_favorecido" class="block text-sm font-medium text-gray-700 mb-2">
                                Nome do Favorecido
                            </label>
                            <input type="text" id="nome_favorecido" name="nome_favorecido"
                                   value="<?php echo htmlspecialchars($transferencia['nome_favorecido'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nome completo">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campos específicos para TED/DOC -->
            <div id="campos_ted_doc" class="hidden">
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-semibold text-gray-800 mb-4">Dados Bancários</h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="banco_destino" class="block text-sm font-medium text-gray-700 mb-2">
                                Banco
                            </label>
                            <input type="text" id="banco_destino" name="banco_destino"
                                   value="<?php echo htmlspecialchars($transferencia['banco_destino'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nome do banco">
                        </div>

                        <div>
                            <label for="agencia_destino" class="block text-sm font-medium text-gray-700 mb-2">
                                Agência
                            </label>
                            <input type="text" id="agencia_destino" name="agencia_destino"
                                   value="<?php echo htmlspecialchars($transferencia['agencia_destino'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="1234-5">
                        </div>

                        <div>
                            <label for="conta_destino_numero" class="block text-sm font-medium text-gray-700 mb-2">
                                Conta
                            </label>
                            <input type="text" id="conta_destino_numero" name="conta_destino_numero"
                                   value="<?php echo htmlspecialchars($transferencia['conta_destino_numero'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="12345-6">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div>
                            <label for="nome_favorecido_ted" class="block text-sm font-medium text-gray-700 mb-2">
                                Nome do Favorecido
                            </label>
                            <input type="text" id="nome_favorecido_ted" name="nome_favorecido"
                                   value="<?php echo htmlspecialchars($transferencia['nome_favorecido'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nome completo">
                        </div>

                        <div>
                            <label for="cpf_cnpj_favorecido" class="block text-sm font-medium text-gray-700 mb-2">
                                CPF/CNPJ
                            </label>
                            <input type="text" id="cpf_cnpj_favorecido" name="cpf_cnpj_favorecido"
                                   value="<?php echo htmlspecialchars($transferencia['cpf_cnpj_favorecido'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="000.000.000-00">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campos adicionais -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Tarifa -->
                <div>
                    <label for="tarifa" class="block text-sm font-medium text-gray-700 mb-2">
                        Tarifa
                    </label>
                    <input type="number" id="tarifa" name="tarifa" step="0.01" min="0"
                           value="<?php echo $transferencia['tarifa'] ?? '0.00'; ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0,00">
                </div>

                <!-- Categoria -->
                <div>
                    <label for="categoria_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Categoria
                    </label>
                    <select id="categoria_id" name="categoria_id"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Selecione a categoria</option>
                        <?php foreach ($categorias as $categoria): ?>
                            <option value="<?php echo $categoria['id']; ?>" <?php echo ($transferencia['categoria_id'] ?? '') == $categoria['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($categoria['nome'] . ' (' . ucfirst($categoria['tipo']) . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Número do Documento -->
                <div>
                    <label for="numero_documento" class="block text-sm font-medium text-gray-700 mb-2">
                        Número do Documento
                    </label>
                    <input type="text" id="numero_documento" name="numero_documento"
                           value="<?php echo htmlspecialchars($transferencia['numero_documento'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Número de referência">
                </div>
            </div>

            <!-- Observações -->
            <div>
                <label for="observacoes" class="block text-sm font-medium text-gray-700 mb-2">
                    Observações
                </label>
                <textarea id="observacoes" name="observacoes" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Observações adicionais"><?php echo htmlspecialchars($transferencia['observacoes'] ?? ''); ?></textarea>
            </div>

            <!-- Botões -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="transferencias.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Cancelar
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    <?php echo isset($transferencia) ? 'Atualizar' : 'Salvar'; ?> Transferência
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function ajustarCampos() {
    const tipo = document.getElementById('tipo_operacao').value;
    const camposPix = document.getElementById('campos_pix');
    const camposTedDoc = document.getElementById('campos_ted_doc');
    const campoContaOrigem = document.getElementById('campo_conta_origem');
    const campoContaDestino = document.getElementById('campo_conta_destino');
    const contaOrigemSelect = document.getElementById('conta_origem_id');
    const contaDestinoSelect = document.getElementById('conta_destino_id');
    const labelContaOrigem = document.getElementById('label_conta_origem');
    const labelContaDestino = document.getElementById('label_conta_destino');
    const helpContaOrigem = document.getElementById('help_conta_origem');
    const helpContaDestino = document.getElementById('help_conta_destino');

    // Resetar campos
    camposPix.classList.add('hidden');
    camposTedDoc.classList.add('hidden');
    campoContaOrigem.classList.remove('hidden');
    campoContaDestino.classList.add('hidden');
    contaOrigemSelect.removeAttribute('required');
    contaDestinoSelect.removeAttribute('required');
    helpContaOrigem.textContent = '';
    helpContaDestino.textContent = '';

    // Ajustar campos baseado no tipo
    switch(tipo) {
        case 'pix_enviado':
            camposPix.classList.remove('hidden');
            contaOrigemSelect.setAttribute('required', 'required');
            labelContaOrigem.textContent = 'Conta que enviará o PIX';
            helpContaOrigem.textContent = 'Selecione a conta da qual o dinheiro sairá';
            break;

        case 'pix_recebido':
            camposPix.classList.remove('hidden');
            campoContaDestino.classList.remove('hidden');
            campoContaOrigem.classList.add('hidden');
            contaDestinoSelect.setAttribute('required', 'required');
            labelContaDestino.textContent = 'Conta que recebeu o PIX';
            helpContaDestino.textContent = 'Selecione a conta onde o dinheiro entrou';
            break;

        case 'ted_enviado':
        case 'doc_enviado':
            camposTedDoc.classList.remove('hidden');
            contaOrigemSelect.setAttribute('required', 'required');
            labelContaOrigem.textContent = 'Conta que enviará a transferência';
            helpContaOrigem.textContent = 'Selecione a conta da qual o dinheiro sairá';
            break;

        case 'ted_recebido':
        case 'doc_recebido':
            camposTedDoc.classList.remove('hidden');
            campoContaDestino.classList.remove('hidden');
            campoContaOrigem.classList.add('hidden');
            contaDestinoSelect.setAttribute('required', 'required');
            labelContaDestino.textContent = 'Conta que recebeu a transferência';
            helpContaDestino.textContent = 'Selecione a conta onde o dinheiro entrou';
            break;

        case 'transferencia':
            campoContaDestino.classList.remove('hidden');
            contaOrigemSelect.setAttribute('required', 'required');
            contaDestinoSelect.setAttribute('required', 'required');
            labelContaOrigem.textContent = 'Conta Origem';
            labelContaDestino.textContent = 'Conta Destino';
            helpContaOrigem.textContent = 'Conta da qual o dinheiro sairá';
            helpContaDestino.textContent = 'Conta para a qual o dinheiro irá';
            break;

        case 'deposito':
            campoContaDestino.classList.remove('hidden');
            campoContaOrigem.classList.add('hidden');
            contaDestinoSelect.setAttribute('required', 'required');
            labelContaDestino.textContent = 'Conta que recebeu o depósito';
            helpContaDestino.textContent = 'Selecione a conta onde o dinheiro entrou';
            break;

        case 'saque':
            contaOrigemSelect.setAttribute('required', 'required');
            labelContaOrigem.textContent = 'Conta do saque';
            helpContaOrigem.textContent = 'Selecione a conta da qual o dinheiro saiu';
            break;

        case 'tarifa':
            contaOrigemSelect.setAttribute('required', 'required');
            labelContaOrigem.textContent = 'Conta debitada';
            helpContaOrigem.textContent = 'Selecione a conta que foi debitada com a tarifa';
            break;

        default:
            helpContaOrigem.textContent = 'Selecione o tipo de operação primeiro';
    }
}

// Executar ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    ajustarCampos();
});
</script>
