<!-- Resu<PERSON> de Movimentações -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <?php
    // Calcular estatísticas do período
    $stats_sql = "
        SELECT 
            COUNT(*) as total_operacoes,
            COALESCE(SUM(CASE WHEN tipo_operacao IN ('pix_enviado', 'ted_enviado', 'doc_enviado', 'transferencia', 'saque') THEN valor + tarifa ELSE 0 END), 0) as total_saidas,
            COALESCE(SUM(CASE WHEN tipo_operacao IN ('pix_recebido', 'ted_recebido', 'doc_recebido', 'deposito') THEN valor ELSE 0 END), 0) as total_entradas,
            COALESCE(SUM(tarifa), 0) as total_tarifas
        FROM transferencias_bancarias 
        WHERE DATE(data_operacao) BETWEEN ? AND ? AND status = 'processada'
    ";
    $stats = $db->fetchOne($stats_sql, [$data_inicio, $data_fim]);
    $saldo_periodo = $stats['total_entradas'] - $stats['total_saidas'];
    ?>
    
    <!-- Total de Operações -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Operações</p>
                <p class="text-2xl font-bold text-gray-800"><?php echo number_format($stats['total_operacoes']); ?></p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-exchange-alt text-blue-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Total de Entradas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Entradas</p>
                <p class="text-2xl font-bold text-green-600">R$ <?php echo number_format($stats['total_entradas'], 2, ',', '.'); ?></p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-down text-green-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Total de Saídas -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Total de Saídas</p>
                <p class="text-2xl font-bold text-red-600">R$ <?php echo number_format($stats['total_saidas'], 2, ',', '.'); ?></p>
            </div>
            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-arrow-up text-red-600 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- Saldo do Período -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">Saldo do Período</p>
                <p class="text-2xl font-bold <?php echo $saldo_periodo >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                    R$ <?php echo number_format($saldo_periodo, 2, ',', '.'); ?>
                </p>
            </div>
            <div class="w-12 h-12 <?php echo $saldo_periodo >= 0 ? 'bg-green-100' : 'bg-red-100'; ?> rounded-lg flex items-center justify-center">
                <i class="fas fa-balance-scale <?php echo $saldo_periodo >= 0 ? 'text-green-600' : 'text-red-600'; ?> text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Filtros -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">
            <i class="fas fa-filter text-blue-500 mr-2"></i>
            Filtros
        </h3>
    </div>
    <div class="p-6">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <input type="hidden" name="acao" value="listar">
            
            <!-- Data Início -->
            <div>
                <label for="data_inicio" class="block text-sm font-medium text-gray-700 mb-2">Data Início</label>
                <input type="date" id="data_inicio" name="data_inicio" value="<?php echo $data_inicio; ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Data Fim -->
            <div>
                <label for="data_fim" class="block text-sm font-medium text-gray-700 mb-2">Data Fim</label>
                <input type="date" id="data_fim" name="data_fim" value="<?php echo $data_fim; ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Tipo de Operação -->
            <div>
                <label for="filtro_tipo" class="block text-sm font-medium text-gray-700 mb-2">Tipo de Operação</label>
                <select id="filtro_tipo" name="filtro_tipo" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Todas</option>
                    <option value="transferencia" <?php echo $filtro_tipo === 'transferencia' ? 'selected' : ''; ?>>Transferência</option>
                    <option value="pix_enviado" <?php echo $filtro_tipo === 'pix_enviado' ? 'selected' : ''; ?>>PIX Enviado</option>
                    <option value="pix_recebido" <?php echo $filtro_tipo === 'pix_recebido' ? 'selected' : ''; ?>>PIX Recebido</option>
                    <option value="ted_enviado" <?php echo $filtro_tipo === 'ted_enviado' ? 'selected' : ''; ?>>TED Enviado</option>
                    <option value="ted_recebido" <?php echo $filtro_tipo === 'ted_recebido' ? 'selected' : ''; ?>>TED Recebido</option>
                    <option value="deposito" <?php echo $filtro_tipo === 'deposito' ? 'selected' : ''; ?>>Depósito</option>
                    <option value="saque" <?php echo $filtro_tipo === 'saque' ? 'selected' : ''; ?>>Saque</option>
                    <option value="tarifa" <?php echo $filtro_tipo === 'tarifa' ? 'selected' : ''; ?>>Tarifa</option>
                </select>
            </div>

            <!-- Conta -->
            <div>
                <label for="filtro_conta" class="block text-sm font-medium text-gray-700 mb-2">Conta</label>
                <select id="filtro_conta" name="filtro_conta" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Todas</option>
                    <?php foreach ($contas_bancarias as $conta): ?>
                        <option value="<?php echo $conta['id']; ?>" <?php echo $filtro_conta == $conta['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($conta['nome'] . ' - ' . $conta['banco']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Busca -->
            <div>
                <label for="busca" class="block text-sm font-medium text-gray-700 mb-2">Buscar</label>
                <input type="text" id="busca" name="busca" value="<?php echo htmlspecialchars($busca); ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Descrição, favorecido...">
            </div>

            <!-- Botões -->
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-search mr-2"></i>
                    Filtrar
                </button>
                <a href="?acao=listar" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-times mr-2"></i>
                    Limpar
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Lista de Transferências -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-list text-blue-500 mr-2"></i>
                Transferências e Movimentações (<?php echo $total_registros; ?> total)
            </h3>
            <div class="text-sm text-gray-500">
                Página <?php echo $page; ?> de <?php echo $total_paginas; ?>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data/Hora</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tipo</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conta Origem</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Conta Destino</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($transferencias)): ?>
                    <tr>
                        <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                            <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                            <p class="text-lg font-medium">Nenhuma transferência encontrada</p>
                            <p class="text-sm">Registre sua primeira transferência ou ajuste os filtros</p>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($transferencias as $transferencia): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('d/m/Y H:i', strtotime($transferencia['data_operacao'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $tipo_classes = [
                                    'transferencia' => 'bg-blue-100 text-blue-800',
                                    'pix_enviado' => 'bg-purple-100 text-purple-800',
                                    'pix_recebido' => 'bg-green-100 text-green-800',
                                    'ted_enviado' => 'bg-orange-100 text-orange-800',
                                    'ted_recebido' => 'bg-green-100 text-green-800',
                                    'deposito' => 'bg-green-100 text-green-800',
                                    'saque' => 'bg-red-100 text-red-800',
                                    'tarifa' => 'bg-gray-100 text-gray-800'
                                ];
                                $tipo_nomes = [
                                    'transferencia' => 'Transferência',
                                    'pix_enviado' => 'PIX Enviado',
                                    'pix_recebido' => 'PIX Recebido',
                                    'ted_enviado' => 'TED Enviado',
                                    'ted_recebido' => 'TED Recebido',
                                    'deposito' => 'Depósito',
                                    'saque' => 'Saque',
                                    'tarifa' => 'Tarifa'
                                ];
                                $class = $tipo_classes[$transferencia['tipo_operacao']] ?? 'bg-gray-100 text-gray-800';
                                $nome = $tipo_nomes[$transferencia['tipo_operacao']] ?? $transferencia['tipo_operacao'];
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $class; ?>">
                                    <?php echo $nome; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                <div class="font-medium"><?php echo htmlspecialchars($transferencia['descricao']); ?></div>
                                <?php if ($transferencia['nome_favorecido']): ?>
                                    <div class="text-gray-500 text-xs">Para: <?php echo htmlspecialchars($transferencia['nome_favorecido']); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <?php if ($transferencia['conta_origem_nome']): ?>
                                    <div><?php echo htmlspecialchars($transferencia['conta_origem_nome']); ?></div>
                                    <div class="text-xs"><?php echo htmlspecialchars($transferencia['conta_origem_banco']); ?></div>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                <?php if ($transferencia['conta_destino_nome']): ?>
                                    <div><?php echo htmlspecialchars($transferencia['conta_destino_nome']); ?></div>
                                    <div class="text-xs"><?php echo htmlspecialchars($transferencia['conta_destino_banco']); ?></div>
                                <?php elseif ($transferencia['banco_destino']): ?>
                                    <div><?php echo htmlspecialchars($transferencia['banco_destino']); ?></div>
                                    <div class="text-xs"><?php echo htmlspecialchars($transferencia['conta_destino_numero']); ?></div>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="text-gray-900">R$ <?php echo number_format($transferencia['valor'], 2, ',', '.'); ?></div>
                                <?php if ($transferencia['tarifa'] > 0): ?>
                                    <div class="text-xs text-red-500">Tarifa: R$ <?php echo number_format($transferencia['tarifa'], 2, ',', '.'); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $status_classes = [
                                    'processada' => 'bg-green-100 text-green-800',
                                    'pendente' => 'bg-yellow-100 text-yellow-800',
                                    'cancelada' => 'bg-red-100 text-red-800',
                                    'estornada' => 'bg-gray-100 text-gray-800'
                                ];
                                $status_nomes = [
                                    'processada' => 'Processada',
                                    'pendente' => 'Pendente',
                                    'cancelada' => 'Cancelada',
                                    'estornada' => 'Estornada'
                                ];
                                $status_class = $status_classes[$transferencia['status']] ?? 'bg-gray-100 text-gray-800';
                                $status_nome = $status_nomes[$transferencia['status']] ?? $transferencia['status'];
                                ?>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $status_class; ?>">
                                    <?php echo $status_nome; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="?acao=visualizar&id=<?php echo $transferencia['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-900" title="Visualizar">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php if (usuarioTemPermissao('financeiro', 'editar')): ?>
                                        <a href="?acao=editar&id=<?php echo $transferencia['id']; ?>" 
                                           class="text-indigo-600 hover:text-indigo-900" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Paginação -->
    <?php if ($total_paginas > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Mostrando <?php echo min($offset + 1, $total_registros); ?> a <?php echo min($offset + $limit, $total_registros); ?> de <?php echo $total_registros; ?> registros
                </div>
                
                <nav class="flex items-center space-x-2">
                    <!-- Navegação de páginas -->
                    <?php
                    $params_url = http_build_query(array_filter([
                        'acao' => 'listar',
                        'data_inicio' => $data_inicio,
                        'data_fim' => $data_fim,
                        'filtro_tipo' => $filtro_tipo,
                        'filtro_conta' => $filtro_conta,
                        'busca' => $busca
                    ]));
                    ?>
                    
                    <?php if ($page > 1): ?>
                        <a href="?<?php echo $params_url; ?>&page=1" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="?<?php echo $params_url; ?>&page=<?php echo $page - 1; ?>" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_paginas, $page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                        $is_current = ($i == $page);
                        $class = $is_current 
                            ? 'px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600' 
                            : 'px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50';
                    ?>
                        <?php if ($is_current): ?>
                            <span class="<?php echo $class; ?>"><?php echo $i; ?></span>
                        <?php else: ?>
                            <a href="?<?php echo $params_url; ?>&page=<?php echo $i; ?>" class="<?php echo $class; ?>"><?php echo $i; ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_paginas): ?>
                        <a href="?<?php echo $params_url; ?>&page=<?php echo $page + 1; ?>" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?<?php echo $params_url; ?>&page=<?php echo $total_paginas; ?>" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    <?php endif; ?>
</div>
