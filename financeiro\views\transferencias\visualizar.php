<!-- Visualização da Transferência -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">
                <i class="fas fa-eye text-blue-500 mr-2"></i>
                Detalhes da Transferência
            </h3>
            <div class="flex items-center space-x-2">
                <?php if (usuarioTemPermissao('financeiro', 'editar')): ?>
                    <a href="?acao=editar&id=<?php echo $transferencia['id']; ?>" 
                       class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-edit mr-2"></i>
                        Editar
                    </a>
                <?php endif; ?>
                <a href="transferencias.php" 
                   class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Voltar
                </a>
            </div>
        </div>
    </div>
    
    <div class="p-6">
        <!-- Informações Principais -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- Tipo de Operação -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-600 mb-2">Tipo de Operação</h4>
                <?php
                $tipo_classes = [
                    'transferencia' => 'bg-blue-100 text-blue-800',
                    'pix_enviado' => 'bg-purple-100 text-purple-800',
                    'pix_recebido' => 'bg-green-100 text-green-800',
                    'ted_enviado' => 'bg-orange-100 text-orange-800',
                    'ted_recebido' => 'bg-green-100 text-green-800',
                    'deposito' => 'bg-green-100 text-green-800',
                    'saque' => 'bg-red-100 text-red-800',
                    'tarifa' => 'bg-gray-100 text-gray-800'
                ];
                $tipo_nomes = [
                    'transferencia' => 'Transferência',
                    'pix_enviado' => 'PIX Enviado',
                    'pix_recebido' => 'PIX Recebido',
                    'ted_enviado' => 'TED Enviado',
                    'ted_recebido' => 'TED Recebido',
                    'deposito' => 'Depósito',
                    'saque' => 'Saque',
                    'tarifa' => 'Tarifa'
                ];
                $class = $tipo_classes[$transferencia['tipo_operacao']] ?? 'bg-gray-100 text-gray-800';
                $nome = $tipo_nomes[$transferencia['tipo_operacao']] ?? $transferencia['tipo_operacao'];
                ?>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full <?php echo $class; ?>">
                    <?php echo $nome; ?>
                </span>
            </div>

            <!-- Valor -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-600 mb-2">Valor</h4>
                <p class="text-2xl font-bold text-gray-900">
                    R$ <?php echo number_format($transferencia['valor'], 2, ',', '.'); ?>
                </p>
                <?php if ($transferencia['tarifa'] > 0): ?>
                    <p class="text-sm text-red-600 mt-1">
                        + Tarifa: R$ <?php echo number_format($transferencia['tarifa'], 2, ',', '.'); ?>
                    </p>
                    <p class="text-sm font-medium text-gray-700">
                        Total: R$ <?php echo number_format($transferencia['valor'] + $transferencia['tarifa'], 2, ',', '.'); ?>
                    </p>
                <?php endif; ?>
            </div>

            <!-- Status -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-600 mb-2">Status</h4>
                <?php
                $status_classes = [
                    'processada' => 'bg-green-100 text-green-800',
                    'pendente' => 'bg-yellow-100 text-yellow-800',
                    'cancelada' => 'bg-red-100 text-red-800',
                    'estornada' => 'bg-gray-100 text-gray-800'
                ];
                $status_nomes = [
                    'processada' => 'Processada',
                    'pendente' => 'Pendente',
                    'cancelada' => 'Cancelada',
                    'estornada' => 'Estornada'
                ];
                $status_class = $status_classes[$transferencia['status']] ?? 'bg-gray-100 text-gray-800';
                $status_nome = $status_nomes[$transferencia['status']] ?? $transferencia['status'];
                ?>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full <?php echo $status_class; ?>">
                    <?php echo $status_nome; ?>
                </span>
            </div>
        </div>

        <!-- Detalhes da Operação -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Informações Gerais -->
            <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Informações Gerais</h4>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Descrição:</span>
                        <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['descricao']); ?></p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">Data/Hora da Operação:</span>
                        <p class="text-gray-900"><?php echo date('d/m/Y H:i:s', strtotime($transferencia['data_operacao'])); ?></p>
                    </div>
                    
                    <?php if ($transferencia['numero_documento']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Número do Documento:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['numero_documento']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['categoria_nome']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Categoria:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['categoria_nome']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">Usuário:</span>
                        <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['usuario_nome'] ?? 'Sistema'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Contas Envolvidas -->
            <div>
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Contas Envolvidas</h4>
                <div class="space-y-4">
                    <?php if ($transferencia['conta_origem_nome']): ?>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                            <h5 class="text-sm font-medium text-red-800 mb-2">
                                <i class="fas fa-arrow-up mr-1"></i>
                                Conta Origem (Débito)
                            </h5>
                            <p class="text-red-900 font-medium"><?php echo htmlspecialchars($transferencia['conta_origem_nome']); ?></p>
                            <p class="text-red-700 text-sm"><?php echo htmlspecialchars($transferencia['conta_origem_banco']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['conta_destino_nome']): ?>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h5 class="text-sm font-medium text-green-800 mb-2">
                                <i class="fas fa-arrow-down mr-1"></i>
                                Conta Destino (Crédito)
                            </h5>
                            <p class="text-green-900 font-medium"><?php echo htmlspecialchars($transferencia['conta_destino_nome']); ?></p>
                            <p class="text-green-700 text-sm"><?php echo htmlspecialchars($transferencia['conta_destino_banco']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Dados Específicos -->
        <?php if (in_array($transferencia['tipo_operacao'], ['pix_enviado', 'pix_recebido']) && ($transferencia['chave_pix'] || $transferencia['nome_favorecido'])): ?>
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Dados do PIX</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <?php if ($transferencia['chave_pix']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Chave PIX:</span>
                            <p class="text-gray-900 font-mono"><?php echo htmlspecialchars($transferencia['chave_pix']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['nome_favorecido']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Nome do Favorecido:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['nome_favorecido']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['cpf_cnpj_favorecido']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">CPF/CNPJ:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['cpf_cnpj_favorecido']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (in_array($transferencia['tipo_operacao'], ['ted_enviado', 'ted_recebido', 'doc_enviado', 'doc_recebido']) && ($transferencia['banco_destino'] || $transferencia['nome_favorecido'])): ?>
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Dados Bancários</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <?php if ($transferencia['banco_destino']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Banco:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['banco_destino']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['agencia_destino']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Agência:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['agencia_destino']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($transferencia['conta_destino_numero']): ?>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Conta:</span>
                            <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['conta_destino_numero']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if ($transferencia['nome_favorecido'] || $transferencia['cpf_cnpj_favorecido']): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <?php if ($transferencia['nome_favorecido']): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-600">Nome do Favorecido:</span>
                                <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['nome_favorecido']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($transferencia['cpf_cnpj_favorecido']): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-600">CPF/CNPJ:</span>
                                <p class="text-gray-900"><?php echo htmlspecialchars($transferencia['cpf_cnpj_favorecido']); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Observações -->
        <?php if ($transferencia['observacoes']): ?>
            <div class="border-t border-gray-200 pt-6 mb-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Observações</h4>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-900"><?php echo nl2br(htmlspecialchars($transferencia['observacoes'])); ?></p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Informações do Sistema -->
        <div class="border-t border-gray-200 pt-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Informações do Sistema</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <span class="text-sm font-medium text-gray-600">Data de Criação:</span>
                    <p class="text-gray-900"><?php echo date('d/m/Y H:i:s', strtotime($transferencia['created_at'])); ?></p>
                </div>
                
                <?php if ($transferencia['updated_at']): ?>
                    <div>
                        <span class="text-sm font-medium text-gray-600">Última Atualização:</span>
                        <p class="text-gray-900"><?php echo date('d/m/Y H:i:s', strtotime($transferencia['updated_at'])); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
