<?php
/**
 * Webhook para receber notificações do Asaas
 */

// Não exigir login para webhook
require_once '../secretaria/includes/Database.php';
require_once 'includes/AsaasAPI.php';

// Configurar headers para API
header('Content-Type: application/json');

// Log de debug (remover em produção)
$debug_log = "webhook_debug_" . date('Y-m-d') . ".log";
file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Webhook recebido\n", FILE_APPEND);

try {
    // Verificar se é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        exit;
    }
    
    // Obter dados do webhook
    $input = file_get_contents('php://input');
    $dados = json_decode($input, true);
    
    // Log dos dados recebidos
    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Dados: " . $input . "\n", FILE_APPEND);
    
    if (!$dados) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        exit;
    }
    
    // Conectar ao banco
    $db = Database::getInstance();
    
    // Verificar configurações do webhook
    $config = $db->fetchOne("SELECT webhook_token FROM configuracoes_asaas WHERE ativo = 1 ORDER BY id DESC LIMIT 1");
    
    // Validar token se configurado
    if ($config && !empty($config['webhook_token'])) {
        $token_recebido = $_SERVER['HTTP_ASAAS_ACCESS_TOKEN'] ?? '';
        if ($token_recebido !== $config['webhook_token']) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized']);
            exit;
        }
    }
    
    // Processar webhook
    $asaasAPI = new AsaasAPI($db);
    $resultado = $asaasAPI->processarWebhook($dados);
    
    if ($resultado) {
        // Processar diferentes tipos de eventos
        $evento = $dados['event'] ?? '';
        $payment = $dados['payment'] ?? [];
        
        switch ($evento) {
            case 'PAYMENT_CREATED':
                // Boleto criado
                file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto criado: " . ($payment['id'] ?? 'N/A') . "\n", FILE_APPEND);
                break;
                
            case 'PAYMENT_UPDATED':
                // Boleto atualizado
                file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto atualizado: " . ($payment['id'] ?? 'N/A') . "\n", FILE_APPEND);
                break;
                
            case 'PAYMENT_CONFIRMED':
            case 'PAYMENT_RECEIVED':
                // Boleto pago
                $asaas_id = $payment['id'] ?? '';
                if ($asaas_id) {
                    // Atualizar boleto local
                    $db->query("
                        UPDATE boletos 
                        SET status = 'pago', 
                            data_pagamento = NOW(), 
                            asaas_status = ?, 
                            webhook_recebido = 1,
                            updated_at = NOW() 
                        WHERE asaas_id = ?
                    ", [$payment['status'] ?? 'RECEIVED', $asaas_id]);
                    
                    // Buscar boleto para atualizar conta a receber se existir
                    $boleto = $db->fetchOne("
                        SELECT mensalidade_id, entidade_id, tipo_entidade 
                        FROM boletos 
                        WHERE asaas_id = ?
                    ", [$asaas_id]);
                    
                    if ($boleto) {
                        // Se for mensalidade, atualizar status
                        if ($boleto['mensalidade_id']) {
                            $db->query("
                                UPDATE mensalidades_alunos 
                                SET status = 'pago', 
                                    data_pagamento = NOW(), 
                                    forma_pagamento = 'boleto',
                                    updated_at = NOW() 
                                WHERE id = ?
                            ", [$boleto['mensalidade_id']]);
                        }
                        
                        // Criar entrada na tesouraria/caixa
                        $valor_pago = $payment['value'] ?? 0;
                        if ($valor_pago > 0) {
                            // Buscar categoria de receita padrão
                            $categoria = $db->fetchOne("
                                SELECT id FROM categorias_financeiras 
                                WHERE tipo = 'receita' AND status = 'ativo' 
                                ORDER BY id ASC LIMIT 1
                            ");
                            
                            if ($categoria) {
                                // Inserir como conta recebida
                                $db->query("
                                    INSERT INTO contas_receber 
                                    (descricao, valor, data_vencimento, data_recebimento, categoria_id, 
                                     cliente_nome, status, forma_pagamento, observacoes, created_at, usuario_id) 
                                    VALUES (?, ?, ?, NOW(), ?, ?, 'recebido', 'boleto', ?, NOW(), 1)
                                ", [
                                    'Pagamento de boleto - ' . ($payment['description'] ?? 'Cobrança'),
                                    $valor_pago,
                                    $payment['dueDate'] ?? date('Y-m-d'),
                                    $categoria['id'],
                                    $payment['customer']['name'] ?? 'Cliente',
                                    'Pagamento via Asaas - ID: ' . $asaas_id
                                ]);
                            }
                        }
                    }
                    
                    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto pago processado: $asaas_id\n", FILE_APPEND);
                }
                break;
                
            case 'PAYMENT_OVERDUE':
                // Boleto vencido
                $asaas_id = $payment['id'] ?? '';
                if ($asaas_id) {
                    $db->query("
                        UPDATE boletos 
                        SET status = 'vencido', 
                            asaas_status = 'OVERDUE', 
                            webhook_recebido = 1,
                            updated_at = NOW() 
                        WHERE asaas_id = ?
                    ", [$asaas_id]);
                    
                    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto vencido: $asaas_id\n", FILE_APPEND);
                }
                break;
                
            case 'PAYMENT_DELETED':
                // Boleto cancelado
                $asaas_id = $payment['id'] ?? '';
                if ($asaas_id) {
                    $db->query("
                        UPDATE boletos 
                        SET status = 'cancelado', 
                            asaas_status = 'DELETED', 
                            webhook_recebido = 1,
                            updated_at = NOW() 
                        WHERE asaas_id = ?
                    ", [$asaas_id]);
                    
                    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto cancelado: $asaas_id\n", FILE_APPEND);
                }
                break;
                
            case 'PAYMENT_REFUNDED':
                // Boleto estornado
                $asaas_id = $payment['id'] ?? '';
                if ($asaas_id) {
                    $db->query("
                        UPDATE boletos 
                        SET status = 'estornado', 
                            asaas_status = 'REFUNDED', 
                            webhook_recebido = 1,
                            updated_at = NOW() 
                        WHERE asaas_id = ?
                    ", [$asaas_id]);
                    
                    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Boleto estornado: $asaas_id\n", FILE_APPEND);
                }
                break;
                
            default:
                file_put_contents($debug_log, date('Y-m-d H:i:s') . " - Evento não tratado: $evento\n", FILE_APPEND);
        }
        
        // Resposta de sucesso
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Webhook processado com sucesso',
            'event' => $evento,
            'payment_id' => $payment['id'] ?? null
        ]);
        
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Erro ao processar webhook']);
    }
    
} catch (Exception $e) {
    // Log do erro
    file_put_contents($debug_log, date('Y-m-d H:i:s') . " - ERRO: " . $e->getMessage() . "\n", FILE_APPEND);
    
    http_response_code(500);
    echo json_encode([
        'error' => 'Internal server error',
        'message' => $e->getMessage()
    ]);
}
?>
