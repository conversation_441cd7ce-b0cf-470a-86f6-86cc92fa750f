<?php
// Importar variáveis necessárias dos GLOBALs
$aluno_id = $GLOBALS['aluno_id'] ?? null;
$curso_id = $GLOBALS['curso_id'] ?? null;
$matricula_id = $GLOBALS['matricula_id'] ?? null;
$aluno = $GLOBALS['aluno'] ?? null;
$matriculas_aluno = $GLOBALS['matriculas_aluno'] ?? [];
$matricula_atual = $GLOBALS['matricula_atual'] ?? null;
$notas_aluno = $GLOBALS['notas_aluno'] ?? [];
$media_geral = $GLOBALS['media_geral'] ?? 0;
$total_aprovadas = $GLOBALS['total_aprovadas'] ?? 0;
$total_reprovadas = $GLOBALS['total_reprovadas'] ?? 0;
$total_em_andamento = $GLOBALS['total_em_andamento'] ?? 0;

// Debug - remover em produção
if (isset($_GET['debug'])) {
    echo '<pre>';
    echo 'Total de notas: ' . count($notas_aluno) . PHP_EOL;
    echo 'Primeira nota: ';
    print_r($notas_aluno[0] ?? 'Nenhuma nota');
    echo '</pre>';
}
?>
<div class="bg-white rounded-xl shadow-sm overflow-hidden print:shadow-none">
    <?php if (empty($matriculas_aluno)): ?>
    <div class="p-6 text-center">
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>Este aluno não possui matrículas ou notas no sistema.</strong>
                    </p>
                    <p class="text-xs text-yellow-600 mt-1">
                        Foram verificadas: matrículas ativas, concluídas, trancadas e notas diretas.
                    </p>
                </div>
            </div>
        </div>
        <a href="notas.php?action=listar" class="btn-secondary print:hidden">
            <i class="fas fa-arrow-left mr-2"></i> Voltar à Listagem
        </a>
    </div>
    <?php else: ?>
    <!-- Cabeçalho do Boletim -->
    <div class="p-6 border-b border-gray-200">
        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
            <div>
                <h2 class="text-xl font-bold text-gray-800">Boletim Escolar</h2>
                <p class="text-sm text-gray-600">Ano Letivo: <?php echo date('Y'); ?></p>
            </div>
            <div class="mt-4 md:mt-0">
                <img src="img/logo.png" alt="Logo da Instituição" class="h-12" onerror="this.style.display='none'">
            </div>
        </div>
    </div>

    <!-- Informações do Aluno -->
    <div class="p-6 border-b border-gray-200 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-sm font-medium text-gray-500">Aluno</h3>
                <p class="text-lg font-semibold text-gray-800"><?php echo htmlspecialchars($aluno['nome']); ?></p>
                <?php if (!empty($aluno['cpf'])): ?>
                <p class="text-sm text-gray-600">CPF: <?php echo htmlspecialchars($aluno['cpf']); ?></p>
                <?php endif; ?>
            </div>

            <!-- Seletor de Matrícula/Curso (apenas na visualização, não na impressão) -->
            <div class="print:hidden">
                <h3 class="text-sm font-medium text-gray-500">Curso/Matrícula</h3>
                <div class="mt-1">
                    <select id="matricula_id" class="form-select w-full" onchange="window.location.href = 'notas.php?action=boletim&aluno_id=<?php echo $aluno_id; ?>&matricula_id=' + this.value">
                        <?php foreach ($matriculas_aluno as $matricula): ?>
                        <option value="<?php echo $matricula['id']; ?>" <?php echo ($matricula_id ?? 0) == $matricula['id'] ? 'selected' : ''; ?>>
                            <?php 
                            echo htmlspecialchars($matricula['curso_nome']);
                            if ($matricula['turma_nome'] && $matricula['turma_nome'] != 'N/A') {
                                echo ' - ' . htmlspecialchars($matricula['turma_nome']);
                            }
                            
                            // Mostrar status se não for ativo
                            if ($matricula['status'] != 'ativo') {
                                $status_label = [
                                    'concluído' => '(Concluído)',
                                    'trancado' => '(Trancado)', 
                                    'cancelado' => '(Cancelado)',
                                    'virtual' => '(Baseado em notas)'
                                ];
                                echo ' ' . ($status_label[$matricula['status']] ?? '(' . ucfirst($matricula['status']) . ')');
                            }
                            ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <!-- Informações da Matrícula (na impressão) -->
            <div class="hidden print:block">
                <h3 class="text-sm font-medium text-gray-500">Matrícula</h3>
                <p class="text-lg font-semibold text-gray-800">
                    <?php echo htmlspecialchars($matricula_atual['curso_nome'] ?? 'N/A'); ?> - <?php echo htmlspecialchars($matricula_atual['turma_nome'] ?? 'N/A'); ?>
                </p>
            </div>
        </div>
    </div>

    <!-- Estatísticas do Boletim -->
    <div class="p-6 border-b border-gray-200">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 rounded-lg p-3 text-center">
                <h4 class="text-sm font-medium text-blue-700">Média Geral</h4>
                <p class="text-2xl font-bold text-blue-800"><?php echo $media_geral > 0 ? number_format($media_geral, 1, ',', '.') : '-'; ?></p>
            </div>

            <div class="bg-green-50 rounded-lg p-3 text-center">
                <h4 class="text-sm font-medium text-green-700">Aprovadas</h4>
                <p class="text-2xl font-bold text-green-800"><?php echo $total_aprovadas; ?></p>
            </div>

            <div class="bg-red-50 rounded-lg p-3 text-center">
                <h4 class="text-sm font-medium text-red-700">Reprovadas</h4>
                <p class="text-2xl font-bold text-red-800"><?php echo $total_reprovadas; ?></p>
            </div>

            <div class="bg-yellow-50 rounded-lg p-3 text-center">
                <h4 class="text-sm font-medium text-yellow-700">Em Andamento</h4>
                <p class="text-2xl font-bold text-yellow-800"><?php echo $total_em_andamento; ?></p>
            </div>
        </div>
    </div>

    <!-- Tabela de Notas -->
    <div class="p-6">
        <?php if (empty($notas_aluno)): ?>
        <div class="text-center text-gray-500 py-6">
            <i class="fas fa-info-circle text-4xl mb-4"></i>
            <p class="text-lg">Não há notas registradas para este aluno nesta matrícula.</p>
            <p class="text-sm mt-2">Curso ID: <?php echo $curso_id; ?> | Aluno ID: <?php echo $aluno_id; ?></p>
        </div>
        <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disciplina</th>
                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Nota</th>
                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Freq. (%)</th>
                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Horas Aula</th>
                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider print:hidden">Data Lançamento</th>
                        <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Situação</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($notas_aluno as $nota): ?>
                    <?php 
                    // Determinar qual nota exibir
                    $nota_exibir = null;
                    if (!empty($nota['nota']) && $nota['nota'] > 0) {
                        $nota_exibir = $nota['nota'];
                    } elseif (!empty($nota['media_final']) && $nota['media_final'] > 0) {
                        $nota_exibir = $nota['media_final'];
                    } elseif (!empty($nota['nota_1']) && $nota['nota_1'] > 0) {
                        $nota_exibir = $nota['nota_1'];
                    }
                    ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo htmlspecialchars($nota['disciplina_nome']); ?>
                                <?php if (!empty($nota['disciplina_codigo'])): ?>
                                <span class="text-xs text-gray-500">(<?php echo htmlspecialchars($nota['disciplina_codigo']); ?>)</span>
                                <?php endif; ?>
                            </div>
                            <?php if (!empty($nota['professor_nome'])): ?>
                            <div class="text-xs text-gray-500">Prof.: <?php echo htmlspecialchars($nota['professor_nome']); ?></div>
                            <?php endif; ?>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-center font-medium">
                            <div class="text-sm <?php echo $nota_exibir !== null && $nota_exibir >= 6 ? 'text-green-600' : ($nota_exibir !== null ? 'text-red-600' : 'text-gray-500'); ?>">
                                <?php echo $nota_exibir !== null ? number_format($nota_exibir, 1, ',', '.') : '-'; ?>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-center">
                            <div class="text-sm <?php echo !empty($nota['frequencia']) && $nota['frequencia'] >= 75 ? 'text-green-600' : (!empty($nota['frequencia']) && $nota['frequencia'] > 0 ? 'text-red-600' : 'text-gray-500'); ?>">
                                <?php echo !empty($nota['frequencia']) && $nota['frequencia'] > 0 ? number_format($nota['frequencia'], 1, ',', '.') . '%' : '-'; ?>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-center">
                            <div class="text-sm text-gray-900">
                                <?php echo !empty($nota['horas_aula']) ? $nota['horas_aula'] : '-'; ?>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-center print:hidden">
                            <div class="text-sm text-gray-900">
                                <?php 
                                if (!empty($nota['data_lancamento'])) {
                                    $data = strtotime($nota['data_lancamento']);
                                    if ($data !== false) {
                                        echo date('d/m/Y', $data);
                                    } else {
                                        echo '-';
                                    }
                                } else {
                                    echo '-';
                                }
                                ?>
                            </div>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-center">
                            <?php if (!empty($nota['situacao'])): ?>
                                <?php 
                                $situacao_labels = [
                                    'cursando' => ['text' => 'Cursando', 'bg' => 'bg-yellow-100', 'color' => 'text-yellow-800'],
                                    'aprovado' => ['text' => 'Aprovado', 'bg' => 'bg-green-100', 'color' => 'text-green-800'],
                                    'reprovado' => ['text' => 'Reprovado', 'bg' => 'bg-red-100', 'color' => 'text-red-800'],
                                    'trancado' => ['text' => 'Trancado', 'bg' => 'bg-gray-100', 'color' => 'text-gray-800']
                                ];
                                $situacao_info = $situacao_labels[$nota['situacao']] ?? $situacao_labels['cursando'];
                                ?>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $situacao_info['bg'] . ' ' . $situacao_info['color']; ?>">
                                    <?php echo $situacao_info['text']; ?>
                                </span>
                            <?php else: ?>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Cursando
                            </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>

    <!-- Observações e Assinaturas (apenas na impressão) -->
    <div class="hidden print:block p-6 border-t border-gray-200">
        <div class="mt-8">
            <h3 class="text-sm font-medium text-gray-700 mb-2">Observações:</h3>
            <div class="border-b border-gray-300 h-16"></div>
        </div>

        <div class="mt-12 grid grid-cols-3 gap-8">
            <div class="text-center">
                <div class="border-t border-gray-300 pt-2">
                    <p class="text-sm text-gray-700">Coordenador(a)</p>
                </div>
            </div>

            <div class="text-center">
                <div class="border-t border-gray-300 pt-2">
                    <p class="text-sm text-gray-700">Secretário(a)</p>
                </div>
            </div>

            <div class="text-center">
                <div class="border-t border-gray-300 pt-2">
                    <p class="text-sm text-gray-700">Diretor(a)</p>
                </div>
            </div>
        </div>

        <div class="mt-8 text-center text-xs text-gray-500">
            <p>Documento emitido em <?php echo date('d/m/Y H:i:s'); ?></p>
        </div>
    </div>

    <!-- Botões de Ação (apenas na visualização, não na impressão) -->
    <div class="p-6 border-t border-gray-200 print:hidden">
        <div class="flex justify-between items-center">
            <div class="text-xs text-gray-500">
                Total de disciplinas: <?php echo count($notas_aluno); ?>
            </div>
            <div class="flex space-x-2">
                <a href="notas.php?action=listar" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Voltar à Listagem
                </a>
                <button onclick="window.print()" class="btn-primary">
                    <i class="fas fa-print mr-2"></i> Imprimir Boletim
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
    @media print {
        body {
            font-size: 12pt;
            color: #000;
            background-color: #fff;
        }

        .print\:hidden {
            display: none !important;
        }

        .print\:block {
            display: block !important;
        }

        .print\:shadow-none {
            box-shadow: none !important;
        }

        @page {
            size: A4;
            margin: 1cm;
        }
    }
</style>