<?php
// Importar variáveis necessárias dos GLOBALs
$curso_id = $GLOBALS['curso_id'] ?? null;
$turma_id = $GLOBALS['turma_id'] ?? null;
$aluno_id = $GLOBALS['aluno_id'] ?? null;
$curso = $GLOBALS['curso'] ?? null;
$turma = $GLOBALS['turma'] ?? null;
$aluno = $GLOBALS['aluno'] ?? null;
$disciplinas = $GLOBALS['disciplinas'] ?? [];
$notas_existentes = $GLOBALS['notas_existentes'] ?? [];
?>
<!-- Fluxo de Lançamento de Notas: Lançar Todas as Disciplinas -->
<div class="space-y-6">
    <!-- Breadcrumb com Status -->
    <div class="bg-white rounded-xl shadow-sm p-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div class="flex items-center">
                        <span class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm">
                            <i class="fas fa-check"></i>
                        </span>
                        <span class="ml-2 text-sm font-medium text-green-600">Curso e Turma</span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm">
                            <i class="fas fa-check"></i>
                        </span>
                        <span class="ml-2 text-sm font-medium text-green-600">Aluno Selecionado</span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">3</span>
                        <span class="ml-2 text-sm font-medium text-blue-600">Lançar Notas</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Informações Completas da Sessão -->
    <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl shadow-sm p-6 border border-green-200">
        <div class="mb-4">
            <h2 class="text-xl font-bold text-gray-900 mb-2">
                <i class="fas fa-clipboard-list mr-2 text-blue-600"></i>
                Lançamento de Notas - Todas as Disciplinas
            </h2>
            <p class="text-sm text-gray-600">Terceiro passo: lance as notas de todas as disciplinas do aluno de uma vez.</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center md:text-left">
                <h3 class="text-sm font-medium text-blue-700 mb-1">
                    <i class="fas fa-graduation-cap mr-1"></i>
                    Curso
                </h3>
                <p class="text-lg font-semibold text-blue-900"><?php echo htmlspecialchars($curso['nome']); ?></p>
            </div>
            <div class="text-center md:text-left">
                <h3 class="text-sm font-medium text-blue-700 mb-1">
                    <i class="fas fa-users mr-1"></i>
                    Turma
                </h3>
                <p class="text-lg font-semibold text-blue-900"><?php echo htmlspecialchars($turma['nome']); ?></p>
            </div>
            <div class="text-center md:text-left">
                <h3 class="text-sm font-medium text-green-700 mb-1">
                    <i class="fas fa-user mr-1"></i>
                    Aluno Selecionado
                </h3>
                <p class="text-lg font-semibold text-green-900"><?php echo htmlspecialchars($aluno['nome']); ?></p>
            </div>
        </div>
        
        <!-- Botões de Navegação -->
        <div class="mt-6 pt-4 border-t border-green-200 flex justify-between items-center">
            <a href="notas.php?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>" 
               class="inline-flex items-center text-sm text-blue-700 hover:text-blue-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Voltar para seleção de aluno
            </a>
            
            <div class="text-sm text-gray-600">
                <i class="fas fa-info-circle mr-1"></i>
                <?php echo count($disciplinas); ?> disciplina(s) para lançar notas
            </div>
        </div>
    </div>

    <!-- Formulário de Lançamento -->
    <?php if (empty($disciplinas)): ?>
    <div class="bg-white rounded-xl shadow-sm p-6 text-center">
        <div class="mb-4">
            <i class="fas fa-book-open text-4xl text-gray-300"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhuma disciplina encontrada</h3>
        <p class="text-gray-500 mb-4">Este curso não possui disciplinas cadastradas.</p>
        <a href="notas.php?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar
        </a>
    </div>
    <?php else: ?>

    <form id="formNotas" method="POST" action="notas.php" class="bg-white rounded-xl shadow-sm overflow-hidden">
        <input type="hidden" name="action" value="salvar_lancamento">
        <input type="hidden" name="curso_id" value="<?php echo $curso_id; ?>">
        <input type="hidden" name="turma_id" value="<?php echo $turma_id; ?>">
        <input type="hidden" name="aluno_id" value="<?php echo $aluno['id']; ?>">

        <!-- Cabeçalho do Formulário -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Lançamento de Notas</h3>
                    <p class="text-sm text-gray-500 mt-1"><?php echo count($disciplinas); ?> disciplina(s) do curso</p>
                </div>
                <div class="mt-3 sm:mt-0 flex space-x-2">
                    <button type="button" id="btnPreencherTodos" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-fill mr-1"></i>
                        Preencher Todos
                    </button>
                    <button type="button" id="btnLimparTodos" class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-eraser mr-1"></i>
                        Limpar Todos
                    </button>
                    <button type="button" id="btnDebugFormulario" class="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100">
                        <i class="fas fa-bug mr-1"></i>
                        Debug
                    </button>
                </div>
            </div>
        </div>

        <!-- Layout Desktop -->
        <div class="hidden lg:block">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disciplina</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Nota</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Freq. %</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-32">Situação</th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Observações</th>
                            <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Status</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($disciplinas as $index => $disciplina): ?>
                        <?php $nota_existente = $notas_existentes[$disciplina['id']] ?? null; ?>
                        <tr class="hover:bg-gray-50" data-disciplina-id="<?php echo $disciplina['id']; ?>">
                            <td class="px-4 py-3">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($disciplina['nome']); ?></div>
                            </td>
                            <td class="px-4 py-3 text-center">
                                <input type="number"
                                       id="nota_<?php echo $disciplina['id']; ?>"
                                       name="notas[<?php echo $disciplina['id']; ?>][nota_1]"
                                       value="<?php echo $nota_existente['nota_1'] !== null ? number_format($nota_existente['nota_1'], 1, '.', '') : ''; ?>"
                                       min="0"
                                       max="10"
                                       step="0.1"
                                       placeholder="0.0"
                                       class="input-nota w-full text-center rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm"
                                       data-index="<?php echo $index; ?>">
                            </td>
                            <td class="px-4 py-3 text-center">
                                <input type="number"
                                       id="freq_<?php echo $disciplina['id']; ?>"
                                       name="notas[<?php echo $disciplina['id']; ?>][frequencia]"
                                       value="<?php echo $nota_existente['frequencia'] !== null ? number_format($nota_existente['frequencia'], 1, '.', '') : ''; ?>"
                                       min="0"
                                       max="100"
                                       step="0.1"
                                       placeholder="0.0"
                                       class="input-frequencia w-full text-center rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm"
                                       data-index="<?php echo $index; ?>">
                            </td>
                            <td class="px-4 py-3 text-center">
                                <select id="situacao_<?php echo $disciplina['id']; ?>"
                                        name="notas[<?php echo $disciplina['id']; ?>][situacao]"
                                        class="select-situacao w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm"
                                        data-index="<?php echo $index; ?>">
                                    <option value="cursando" <?php echo ($nota_existente['situacao'] ?? 'cursando') === 'cursando' ? 'selected' : ''; ?>>Cursando</option>
                                    <option value="aprovado" <?php echo ($nota_existente['situacao'] ?? '') === 'aprovado' ? 'selected' : ''; ?>>Aprovado</option>
                                    <option value="reprovado" <?php echo ($nota_existente['situacao'] ?? '') === 'reprovado' ? 'selected' : ''; ?>>Reprovado</option>
                                </select>
                            </td>
                            <td class="px-4 py-3">
                                <input type="text"
                                       id="obs_<?php echo $disciplina['id']; ?>"
                                       name="notas[<?php echo $disciplina['id']; ?>][observacoes]"
                                       value="<?php echo htmlspecialchars($nota_existente['observacoes'] ?? ''); ?>"
                                       placeholder="Observações..."
                                       class="input-observacoes w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm"
                                       data-index="<?php echo $index; ?>">
                            </td>
                            <td class="px-4 py-3 text-center">
                                <?php if ($nota_existente): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check mr-1"></i>
                                        Lançada
                                    </span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        <i class="fas fa-minus mr-1"></i>
                                        Pendente
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Layout Mobile (versão simplificada) -->
        <div class="lg:hidden divide-y divide-gray-200">
            <?php foreach ($disciplinas as $index => $disciplina): ?>
            <?php $nota_existente = $notas_existentes[$disciplina['id']] ?? null; ?>
            <div class="p-4" data-disciplina-id="<?php echo $disciplina['id']; ?>">
                <div class="mb-3">
                    <h4 class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($disciplina['nome']); ?></h4>
                    <div class="mt-1">
                        <?php if ($nota_existente): ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check mr-1"></i>
                                Lançada
                            </span>
                        <?php else: ?>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="fas fa-minus mr-1"></i>
                                Pendente
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Nota</label>
                        <input type="number"
                               name="notas[<?php echo $disciplina['id']; ?>][nota_1]"
                               value="<?php echo $nota_existente['nota_1'] !== null ? number_format($nota_existente['nota_1'], 1, '.', '') : ''; ?>"
                               min="0"
                               max="10"
                               step="0.1"
                               placeholder="0.0"
                               class="input-nota w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm">
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-700 mb-1">Freq. %</label>
                        <input type="number"
                               name="notas[<?php echo $disciplina['id']; ?>][frequencia]"
                               value="<?php echo $nota_existente['frequencia'] !== null ? number_format($nota_existente['frequencia'], 1, '.', '') : ''; ?>"
                               min="0"
                               max="100"
                               step="0.1"
                               placeholder="0.0"
                               class="input-frequencia w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm">
                    </div>
                </div>

                <div class="mt-3">
                    <label class="block text-xs font-medium text-gray-700 mb-1">Situação</label>
                    <select name="notas[<?php echo $disciplina['id']; ?>][situacao]"
                            class="select-situacao w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm">
                        <option value="cursando" <?php echo ($nota_existente['situacao'] ?? 'cursando') === 'cursando' ? 'selected' : ''; ?>>Cursando</option>
                        <option value="aprovado" <?php echo ($nota_existente['situacao'] ?? '') === 'aprovado' ? 'selected' : ''; ?>>Aprovado</option>
                        <option value="reprovado" <?php echo ($nota_existente['situacao'] ?? '') === 'reprovado' ? 'selected' : ''; ?>>Reprovado</option>
                    </select>
                </div>

                <div class="mt-3">
                    <label class="block text-xs font-medium text-gray-700 mb-1">Observações</label>
                    <input type="text"
                           name="notas[<?php echo $disciplina['id']; ?>][observacoes]"
                           value="<?php echo htmlspecialchars($nota_existente['observacoes'] ?? ''); ?>"
                           placeholder="Observações..."
                           class="input-observacoes w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-sm">
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Rodapé do Formulário -->
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-3 sm:space-y-0">
                <div class="flex space-x-3">
                    <button type="submit" class="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-save mr-2"></i>
                        Salvar Todas as Notas
                    </button>

                    <a href="notas.php?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Voltar
                    </a>
                </div>

                <div class="text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    Preencha apenas os campos que deseja salvar
                </div>
            </div>
        </div>
    </form>

    <!-- Indicador de Progresso -->
    <div id="progressoSalvamento" class="hidden fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-sm">
        <div class="flex items-center">
            <div class="spinner mr-3">
                <i class="fas fa-spinner fa-spin text-blue-500"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-900">Salvando notas...</p>
                <p class="text-xs text-gray-500">Por favor, aguarde.</p>
            </div>
        </div>
    </div>

    <?php endif; ?>
</div>

<script>
// ============================================================================
// SISTEMA DE NOTAS - VERSÃO CORRIGIDA PARA MÚLTIPLAS DISCIPLINAS
// ============================================================================

(function() {
    'use strict';
    
    console.log('🚀 Sistema de Notas v3.0 - Carregando...');
    
    // Configurações
    const CONFIG = {
        debug: true,
        notaMinima: 0,
        notaMaxima: 10,
        frequenciaMinima: 0,
        frequenciaMaxima: 100,
        notaAprovacao: 6.0,
        frequenciaAprovacao: 75.0
    };
    
    // Estado global
    const estado = {
        disciplinasTotal: <?php echo count($disciplinas); ?>,
        disciplinasPreenchidas: 0,
        salvandoEmProgresso: false
    };
    
    // Utilitários
    const utils = {
        log: function(msg, data) {
            if (CONFIG.debug) {
                console.log(`[Sistema Notas] ${msg}`, data || '');
            }
        },
        
        formatarNumero: function(valor, casasDecimais = 1) {
            if (!valor || valor === '') return '';
            const numero = parseFloat(valor.toString().replace(',', '.'));
            return isNaN(numero) ? '' : numero.toFixed(casasDecimais);
        },
        
        validarNota: function(valor) {
            const numero = parseFloat(valor);
            return !isNaN(numero) && numero >= CONFIG.notaMinima && numero <= CONFIG.notaMaxima;
        },
        
        validarFrequencia: function(valor) {
            const numero = parseFloat(valor);
            return !isNaN(numero) && numero >= CONFIG.frequenciaMinima && numero <= CONFIG.frequenciaMaxima;
        }
    };
    
    // Funções principais
    const sistemaNotas = {
        init: function() {
            utils.log('Inicializando sistema...');
            this.configurarEventListeners();
            this.contarDisciplinasPreenchidas();
            utils.log('Sistema inicializado com sucesso!');
        },
        
        configurarEventListeners: function() {
            // Botões principais
            const btnPreencher = document.getElementById('btnPreencherTodos');
            const btnLimpar = document.getElementById('btnLimparTodos');
            const btnDebug = document.getElementById('btnDebugFormulario');
            
            if (btnPreencher) {
                btnPreencher.addEventListener('click', () => this.preencherTodos());
            }
            
            if (btnLimpar) {
                btnLimpar.addEventListener('click', () => this.limparTodos());
            }
            
            if (btnDebug) {
                btnDebug.addEventListener('click', () => this.debugFormulario());
            }
            
            // Inputs de nota e frequência
            document.querySelectorAll('.input-nota, .input-frequencia').forEach(input => {
                input.addEventListener('input', (e) => this.aoAlterarCampo(e));
                input.addEventListener('blur', (e) => this.aoSairDoCampo(e));
            });
            
            // Formulário
            const form = document.getElementById('formNotas');
            if (form) {
                form.addEventListener('submit', (e) => this.aoEnviarFormulario(e));
            }
            
            utils.log('Event listeners configurados');
        },
        
        preencherTodos: function() {
            utils.log('Iniciando preenchimento de todos os campos...');
            
            const nota = prompt('Digite a nota para todas as disciplinas (0-10):');
            const frequencia = prompt('Digite a frequência para todas as disciplinas (0-100):');
            
            if (!nota && !frequencia) {
                alert('Você deve informar pelo menos a nota ou a frequência');
                return;
            }
            
            let contadorNotas = 0;
            let contadorFreq = 0;
            
            // Preencher notas
            if (nota !== null && nota !== '') {
                const notaFormatada = utils.formatarNumero(nota);
                if (utils.validarNota(notaFormatada)) {
                    document.querySelectorAll('.input-nota').forEach((input, index) => {
                        input.value = notaFormatada;
                        input.classList.add('highlight-nota');
                        this.atualizarSituacao(input);
                        contadorNotas++;
                        utils.log(`Nota preenchida para disciplina ${index + 1}:`, notaFormatada);
                    });
                } else {
                    alert('Nota inválida! Deve estar entre 0 e 10.');
                    return;
                }
            }
            
            // Preencher frequências
            if (frequencia !== null && frequencia !== '') {
                const freqFormatada = utils.formatarNumero(frequencia);
                if (utils.validarFrequencia(freqFormatada)) {
                    document.querySelectorAll('.input-frequencia').forEach((input, index) => {
                        input.value = freqFormatada;
                        input.classList.add('highlight-freq');
                        this.atualizarSituacaoPorFrequencia(input);
                        contadorFreq++;
                        utils.log(`Frequência preenchida para disciplina ${index + 1}:`, freqFormatada);
                    });
                } else {
                    alert('Frequência inválida! Deve estar entre 0 e 100.');
                    return;
                }
            }
            
            this.contarDisciplinasPreenchidas();
            
            alert(`✅ Preenchimento concluído!\n\nNotas preenchidas: ${contadorNotas}\nFrequências preenchidas: ${contadorFreq}`);
        },
        
        limparTodos: function() {
            if (!confirm('Tem certeza que deseja limpar todos os campos?')) {
                return;
            }
            
            utils.log('Limpando todos os campos...');
            
            // Limpar todos os inputs
            document.querySelectorAll('.input-nota, .input-frequencia, .input-observacoes').forEach(input => {
                input.value = '';
                input.classList.remove('highlight-nota', 'highlight-freq');
            });
            
            // Resetar situações
            document.querySelectorAll('.select-situacao').forEach(select => {
                select.value = 'cursando';
            });
            
            this.contarDisciplinasPreenchidas();
            
            alert('✅ Todos os campos foram limpos!');
        },
        
        debugFormulario: function() {
            const dadosDebug = {
                totalDisciplinas: estado.disciplinasTotal,
                disciplinasPreenchidas: estado.disciplinasPreenchidas,
                campos: []
            };
            
            document.querySelectorAll('tr[data-disciplina-id], div[data-disciplina-id]').forEach((row, index) => {
                const disciplinaId = row.getAttribute('data-disciplina-id');
                const inputNota = row.querySelector('.input-nota');
                const inputFreq = row.querySelector('.input-frequencia');
                const selectSituacao = row.querySelector('.select-situacao');
                
                dadosDebug.campos.push({
                    index: index,
                    disciplinaId: disciplinaId,
                    nota: inputNota ? inputNota.value : 'N/A',
                    frequencia: inputFreq ? inputFreq.value : 'N/A',
                    situacao: selectSituacao ? selectSituacao.value : 'N/A',
                    notaName: inputNota ? inputNota.name : 'N/A',
                    freqName: inputFreq ? inputFreq.name : 'N/A'
                });
            });
            
            console.group('🐛 DEBUG DO FORMULÁRIO');
            console.log('Estado atual:', estado);
            console.log('Dados do formulário:', dadosDebug);
            console.table(dadosDebug.campos);
            console.groupEnd();
            
            // Mostrar resumo visual
            const resumo = `
📊 RESUMO DO FORMULÁRIO
======================
Total de disciplinas: ${dadosDebug.totalDisciplinas}
Disciplinas com dados: ${dadosDebug.disciplinasPreenchidas}
Disciplinas vazias: ${dadosDebug.totalDisciplinas - dadosDebug.disciplinasPreenchidas}

✅ Dados salvos: ${dadosDebug.campos.filter(c => c.nota || c.frequencia).length}
❌ Sem dados: ${dadosDebug.campos.filter(c => !c.nota && !c.frequencia).length}

Detalhes no console (F12)
            `;
            
            alert(resumo);
        },
        
        aoAlterarCampo: function(event) {
            const input = event.target;
            
            // Remover classes de destaque após edição manual
            input.classList.remove('highlight-nota', 'highlight-freq');
            
            // Atualizar situação se for nota ou frequência
            if (input.classList.contains('input-nota') || input.classList.contains('input-frequencia')) {
                this.atualizarSituacao(input);
            }
        },
        
        aoSairDoCampo: function(event) {
            const input = event.target;
            
            // Formatar valor ao sair do campo
            if (input.value !== '') {
                const valorFormatado = utils.formatarNumero(input.value);
                
                if (input.classList.contains('input-nota')) {
                    if (utils.validarNota(valorFormatado)) {
                        input.value = valorFormatado;
                    } else {
                        alert('Nota deve estar entre 0 e 10!');
                        input.focus();
                    }
                } else if (input.classList.contains('input-frequencia')) {
                    if (utils.validarFrequencia(valorFormatado)) {
                        input.value = valorFormatado;
                    } else {
                        alert('Frequência deve estar entre 0 e 100!');
                        input.focus();
                    }
                }
            }
            
            this.contarDisciplinasPreenchidas();
        },
        
        atualizarSituacao: function(input) {
            const row = input.closest('tr, div[data-disciplina-id]');
            if (!row) return;
            
            const inputNota = row.querySelector('.input-nota');
            const inputFreq = row.querySelector('.input-frequencia');
            const selectSituacao = row.querySelector('.select-situacao');
            
            if (!inputNota || !inputFreq || !selectSituacao) return;
            
            const nota = parseFloat(inputNota.value) || 0;
            const freq = parseFloat(inputFreq.value) || 0;
            
            // Só atualiza se ambos os campos estiverem preenchidos
            if (inputNota.value !== '' && inputFreq.value !== '') {
                if (nota >= CONFIG.notaAprovacao && freq >= CONFIG.frequenciaAprovacao) {
                    selectSituacao.value = 'aprovado';
                } else {
                    selectSituacao.value = 'reprovado';
                }
            }
        },
        
        atualizarSituacaoPorFrequencia: function(inputFreq) {
            const row = inputFreq.closest('tr, div[data-disciplina-id]');
            if (!row) return;
            
            const inputNota = row.querySelector('.input-nota');
            if (inputNota && inputNota.value !== '') {
                this.atualizarSituacao(inputFreq);
            }
        },
        
        contarDisciplinasPreenchidas: function() {
            let contador = 0;
            
            document.querySelectorAll('tr[data-disciplina-id], div[data-disciplina-id]').forEach(row => {
                const inputNota = row.querySelector('.input-nota');
                const inputFreq = row.querySelector('.input-frequencia');
                
                if ((inputNota && inputNota.value !== '') || (inputFreq && inputFreq.value !== '')) {
                    contador++;
                }
            });
            
            estado.disciplinasPreenchidas = contador;
            utils.log(`Disciplinas preenchidas: ${contador}/${estado.disciplinasTotal}`);
            
            return contador;
        },
        
        aoEnviarFormulario: function(event) {
            // Não prevenir o envio, apenas adicionar validações se necessário
            utils.log('Formulário sendo enviado...');
            
            const disciplinasComDados = this.contarDisciplinasPreenchidas();
            
            if (disciplinasComDados === 0) {
                event.preventDefault();
                alert('❌ Nenhuma disciplina tem dados para salvar!');
                return;
            }
            
            // Mostrar indicador de progresso
            const progresso = document.getElementById('progressoSalvamento');
            if (progresso) {
                progresso.classList.remove('hidden');
            }
            
            // Log dos dados que serão enviados
            if (CONFIG.debug) {
                console.group('📤 Dados sendo enviados');
                document.querySelectorAll('tr[data-disciplina-id], div[data-disciplina-id]').forEach((row, index) => {
                    const disciplinaId = row.getAttribute('data-disciplina-id');
                    const inputNota = row.querySelector('.input-nota');
                    const inputFreq = row.querySelector('.input-frequencia');
                    
                    if ((inputNota && inputNota.value) || (inputFreq && inputFreq.value)) {
                        console.log(`Disciplina ${disciplinaId}:`, {
                            nota: inputNota ? inputNota.value : '',
                            frequencia: inputFreq ? inputFreq.value : ''
                        });
                    }
                });
                console.groupEnd();
            }
        }
    };
    
    // Inicializar quando o DOM estiver pronto
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => sistemaNotas.init());
    } else {
        sistemaNotas.init();
    }
    
    // Exportar para o global para testes
    window.sistemaNotas = sistemaNotas;
    window.debugNotas = () => sistemaNotas.debugFormulario();
    
})();
</script>

<style>
/* Estilos para destacar campos preenchidos automaticamente */
.highlight-nota {
    background-color: #e6f7ff !important;
    border-color: #1890ff !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.highlight-freq {
    background-color: #f6ffed !important;
    border-color: #52c41a !important;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

/* Animação para campos sendo salvos */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.salvando {
    animation: pulse 0.6s ease-in-out;
}

/* Indicador de progresso */
#progressoSalvamento {
    z-index: 9999;
}
</style>