<?php
// Importar variáveis necessárias dos GLOBALs
$curso_id = $GLOBALS['curso_id'] ?? null;
$turma_id = $GLOBALS['turma_id'] ?? null;
$curso = $GLOBALS['curso'] ?? null;
$turma = $GLOBALS['turma'] ?? null;
$alunos = $GLOBALS['alunos'] ?? [];
$page = $GLOBALS['page'] ?? 1;
$total_pages = $GLOBALS['total_pages'] ?? 1;
$busca = $GLOBALS['busca'] ?? '';
?>
<!-- Fluxo de Lançamento de Notas: Seleção de Aluno -->
<div class="bg-white rounded-xl shadow-sm p-6">
    <!-- Breadcrumb com Status -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
                <li>
                    <div class="flex items-center">
                        <span class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm">
                            <i class="fas fa-check"></i>
                        </span>
                        <span class="ml-2 text-sm font-medium text-green-600">Curso e Turma</span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">2</span>
                        <span class="ml-2 text-sm font-medium text-blue-600">Selecionar Aluno</span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="bg-gray-300 text-gray-500 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">3</span>
                        <span class="ml-2 text-sm font-medium text-gray-500">Lançar Notas</span>
                    </div>
                </li>
            </ol>
        </nav>
        
        <h2 class="text-lg font-semibold text-gray-800 mt-4 mb-2">
            <i class="fas fa-users mr-2 text-blue-500"></i>
            Selecionar Aluno para Lançamento de Notas
        </h2>
        <p class="text-sm text-gray-600">Segundo passo: escolha o aluno para lançar as notas de todas as disciplinas de uma vez.</p>
    </div>

    <!-- Informações da Turma Selecionada -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 mb-6 border border-blue-200">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-sm font-medium text-blue-700 mb-1">
                    <i class="fas fa-graduation-cap mr-1"></i>
                    Curso Selecionado
                </h3>
                <p class="text-lg font-semibold text-blue-900"><?php echo htmlspecialchars($curso['nome']); ?></p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-blue-700 mb-1">
                    <i class="fas fa-users mr-1"></i>
                    Turma Selecionada
                </h3>
                <p class="text-lg font-semibold text-blue-900"><?php echo htmlspecialchars($turma['nome']); ?></p>
            </div>
        </div>
        
        <!-- Botão para voltar -->
        <div class="mt-3 pt-3 border-t border-blue-200">
            <a href="notas.php?action=lancar" 
               class="inline-flex items-center text-sm text-blue-700 hover:text-blue-900 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Alterar curso/turma
            </a>
        </div>
    </div>

    <!-- Campo de Busca -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <form method="GET" action="" class="flex flex-col md:flex-row gap-3">
            <input type="hidden" name="action" value="lancar">
            <input type="hidden" name="curso_id" value="<?php echo $curso_id; ?>">
            <input type="hidden" name="turma_id" value="<?php echo $turma_id; ?>">

            <div class="flex-1">
                <input type="text"
                       name="busca"
                       value="<?php echo htmlspecialchars($busca ?? ''); ?>"
                       placeholder="Buscar por nome ou CPF do aluno..."
                       class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <button type="submit"
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-search mr-2"></i>
                Buscar
            </button>

            <?php if (!empty($busca)): ?>
            <a href="?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>"
               class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md transition-colors duration-200">
                <i class="fas fa-times mr-2"></i>
                Limpar
            </a>
            <?php endif; ?>
        </form>

        <div class="mt-3 text-sm text-gray-600">
            <?php if (isset($total_alunos)): ?>
                Total: <?php echo $total_alunos; ?> aluno(s)
                <?php if (!empty($busca)): ?>
                    | Busca: "<?php echo htmlspecialchars($busca); ?>"
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Lista de Alunos -->
    <?php if (empty($alunos)): ?>
    <div class="text-center py-8">
        <div class="mb-4">
            <i class="fas fa-user-slash text-4xl text-gray-300"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Nenhum aluno encontrado</h3>
        <p class="text-gray-500 mb-4">
            <?php if (!empty($busca)): ?>
                Nenhum aluno encontrado para o termo "<?php echo htmlspecialchars($busca); ?>".
            <?php else: ?>
                Esta turma não possui alunos matriculados ativos.
            <?php endif; ?>
        </p>
        <a href="notas.php?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>"
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar
        </a>
    </div>
    <?php else: ?>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <?php foreach ($alunos as $aluno): ?>
        <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-lg transition-all cursor-pointer group bg-white hover:bg-blue-50"
             onclick="selecionarAluno(<?php echo $aluno['id']; ?>)">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <div class="flex items-center mb-2">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-700 mb-0">
                                <?php echo htmlspecialchars($aluno['nome']); ?>
                            </h4>
                            <p class="text-xs text-gray-500">
                                CPF: <?php echo htmlspecialchars($aluno['cpf']); ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-t border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-xs text-blue-600 group-hover:text-blue-700">
                                <i class="fas fa-clipboard-list mr-2"></i>
                                <span class="font-medium">Lançar todas as disciplinas</span>
                            </div>
                            <a href="notas.php?action=boletim&aluno_id=<?php echo $aluno['id']; ?>&curso_id=<?php echo $curso_id; ?>" 
                               onclick="event.stopPropagation();"
                               class="text-xs text-green-600 hover:text-green-700 font-medium px-2 py-1 bg-green-50 hover:bg-green-100 rounded transition-colors"
                               title="Ver boletim completo">
                                <i class="fas fa-file-alt mr-1"></i>
                                Boletim
                            </a>
                        </div>
                    </div>
                </div>
                <div class="ml-3 flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 group-hover:bg-blue-200 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-right text-blue-600 group-hover:text-blue-700"></i>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Paginação -->
    <?php if ($total_pages > 1): ?>
    <div class="flex justify-center">
        <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <?php if ($page > 1): ?>
                <a href="?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>&page=<?php echo $page - 1; ?><?php echo !empty($busca) ? '&busca=' . urlencode($busca) : ''; ?>" 
                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
            <?php endif; ?>

            <?php
            $start = max(1, $page - 2);
            $end = min($total_pages, $page + 2);
            
            for ($i = $start; $i <= $end; $i++):
            ?>
                <a href="?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>&page=<?php echo $i; ?><?php echo !empty($busca) ? '&busca=' . urlencode($busca) : ''; ?>"
                   class="relative inline-flex items-center px-4 py-2 border <?php echo $i == $page ? 'bg-blue-50 border-blue-500 text-blue-600' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'; ?> text-sm font-medium">
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
                <a href="?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>&page=<?php echo $page + 1; ?><?php echo !empty($busca) ? '&busca=' . urlencode($busca) : ''; ?>"
                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
            <?php endif; ?>
        </nav>
    </div>
    <?php endif; ?>

    <?php endif; ?>

    <!-- Botão Voltar -->
    <div class="mt-6 text-center">
        <a href="notas.php?action=lancar" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar para Seleção de Turma
        </a>
    </div>
</div>

<script>
function selecionarAluno(alunoId) {
    const curso_id = <?php echo $curso_id; ?>;
    const turma_id = <?php echo $turma_id; ?>;
    window.location.href = `notas.php?action=lancar&curso_id=${curso_id}&turma_id=${turma_id}&aluno_id=${alunoId}`;
}
</script>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Busca de alunos
        const alunoBuscaInput = document.getElementById('aluno_busca');
        const alunosResultados = document.getElementById('alunos_resultados');
        
        if (alunoBuscaInput) {
            // Evento de digitação na busca
            let timeoutId;
            alunoBuscaInput.addEventListener('input', function() {
                clearTimeout(timeoutId);
                const termo = this.value.trim();
                
                if (termo.length < 3) {
                    alunosResultados.innerHTML = '';
                    alunosResultados.classList.add('hidden');
                    return;
                }
                
                timeoutId = setTimeout(function() {
                    // Faz uma requisição para buscar alunos
                    fetch(`notas.php?action=buscar_aluno&termo=${encodeURIComponent(termo)}`)
                        .then(response => response.json())
                        .then(data => {
                            alunosResultados.innerHTML = '';
                            
                            if (data.length === 0) {
                                const noResultsItem = document.createElement('div');
                                noResultsItem.className = 'p-3 text-gray-500 text-center';
                                noResultsItem.textContent = 'Nenhum aluno encontrado';
                                alunosResultados.appendChild(noResultsItem);
                            } else {
                                data.forEach(aluno => {
                                    const item = document.createElement('div');
                                    item.className = 'p-3 hover:bg-gray-100 cursor-pointer';
                                    
                                    const nome = document.createElement('div');
                                    nome.className = 'font-medium';
                                    nome.textContent = aluno.nome;
                                    
                                    const info = document.createElement('div');
                                    info.className = 'text-xs text-gray-500';
                                    info.textContent = aluno.cpf ? `CPF: ${aluno.cpf}` : '';
                                    if (aluno.email) {
                                        info.textContent += aluno.cpf ? ` | ${aluno.email}` : aluno.email;
                                    }
                                    
                                    item.appendChild(nome);
                                    item.appendChild(info);
                                    
                                    item.addEventListener('click', function() {
                                        const curso_id = <?php echo $curso_id ?? 0; ?>;
                                        const turma_id = <?php echo $turma_id ?? 0; ?>;
                                        window.location.href = `notas.php?action=lancar&curso_id=${curso_id}&turma_id=${turma_id}&aluno_id=${aluno.id}`;
                                    });
                                    
                                    alunosResultados.appendChild(item);
                                });
                            }
                            
                            alunosResultados.classList.remove('hidden');
                        })
                        .catch(error => console.error('Erro ao buscar alunos:', error));
                }, 300);
            });
            
            // Fecha os resultados ao clicar fora
            document.addEventListener('click', function(e) {
                if (!alunosResultados.contains(e.target) && e.target !== alunoBuscaInput) {
                    alunosResultados.classList.add('hidden');
                }
            });
        }
    });
</script>
