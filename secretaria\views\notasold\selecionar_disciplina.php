<!-- <PERSON><PERSON><PERSON> de Disciplina -->
<div class="bg-white rounded-xl shadow-sm p-6">
    <!-- Breadcrumb -->
    <div class="mb-6">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="notas.php?action=lancar" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                        <i class="fas fa-home mr-2"></i>
                        Lançar Notas
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500"><?php echo htmlspecialchars($curso['nome']); ?></span>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-gray-500"><?php echo htmlspecialchars($turma['nome']); ?></span>
                    </div>
                </li>
                <li aria-current="page">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-sm font-medium text-blue-600">Disciplina</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>

    <!-- Informações da Turma -->
    <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <h3 class="text-sm font-medium text-gray-700 mb-1">Curso Selecionado</h3>
                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($curso['nome']); ?></p>
            </div>
            <div>
                <h3 class="text-sm font-medium text-gray-700 mb-1">Turma Selecionada</h3>
                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($turma['nome']); ?></p>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-800 mb-2">Selecionar Disciplina</h2>
        <p class="text-sm text-gray-600">Escolha a disciplina para lançar as notas ou cadastre uma nova disciplina.</p>
    </div>

    <!-- Disciplinas Existentes -->
    <?php if (!empty($disciplinas)): ?>
    <div class="mb-8">
        <h3 class="text-md font-medium text-gray-800 mb-4">
            <i class="fas fa-book mr-2 text-purple-500"></i>
            Disciplinas Disponíveis
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <?php foreach ($disciplinas as $disciplina): ?>
            <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer group"
                 onclick="selecionarDisciplina(<?php echo $disciplina['id']; ?>)">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                            <?php echo htmlspecialchars($disciplina['nome']); ?>
                        </h4>
                    </div>
                    <div class="ml-3">
                        <i class="fas fa-arrow-right text-gray-400 group-hover:text-blue-500"></i>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Cadastrar Nova Disciplina -->
    <div class="border-t border-gray-200 pt-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-md font-medium text-gray-800">
                <i class="fas fa-plus-circle mr-2 text-green-500"></i>
                Cadastrar Nova Disciplina
            </h3>
            <button type="button" 
                    id="btn-toggle-form"
                    onclick="toggleFormDisciplina()"
                    class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-plus mr-1"></i>
                <span id="btn-toggle-text">Mostrar Formulário</span>
            </button>
        </div>

        <div id="form-nova-disciplina" class="hidden">
            <form method="POST" action="notas.php" class="space-y-4">
                <input type="hidden" name="action" value="nova_disciplina">
                <input type="hidden" name="curso_id" value="<?php echo $curso_id; ?>">
                <input type="hidden" name="turma_id" value="<?php echo $turma_id; ?>">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="nome" class="block text-sm font-medium text-gray-700 mb-1">
                            Nome da Disciplina <span class="text-red-500">*</span>
                        </label>
                        <input type="text" 
                               id="nome" 
                               name="nome" 
                               required
                               placeholder="Ex: Matemática Básica"
                               class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                    
                    <div>
                        <label for="codigo" class="block text-sm font-medium text-gray-700 mb-1">
                            Código da Disciplina
                        </label>
                        <input type="text" 
                               id="codigo" 
                               name="codigo" 
                               placeholder="Ex: MAT001"
                               class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                </div>
                
                <div>
                    <label for="carga_horaria" class="block text-sm font-medium text-gray-700 mb-1">
                        Carga Horária (horas)
                    </label>
                    <input type="number" 
                           id="carga_horaria" 
                           name="carga_horaria" 
                           min="1"
                           placeholder="Ex: 60"
                           class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                </div>

                <!-- Seleção de Professor -->
                <div>
                    <label for="professor_id" class="block text-sm font-medium text-gray-700 mb-1">Professor Padrão</label>
                    <select id="professor_id" name="professor_id" class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                        <option value="">Selecione um professor</option>
                        <?php foreach ($professores as $professor): ?>
                            <option value="<?php echo $professor['id']; ?>"><?php echo htmlspecialchars($professor['nome']); ?></option>
                        <?php endforeach; ?>
                        <option value="novo">Cadastrar novo professor...</option>
                    </select>
                </div>

                <div class="flex space-x-3 pt-4">
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <i class="fas fa-save mr-2"></i>
                        Cadastrar e Continuar
                    </button>
                    
                    <button type="button" 
                            onclick="toggleFormDisciplina()"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-times mr-2"></i>
                        Cancelar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para Cadastrar Novo Professor -->
    <div id="modal-novo-professor" class="fixed z-50 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                            <i class="fas fa-user-plus text-blue-600"></i>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Cadastrar Novo Professor
                            </h3>
                            <div class="mt-4">
                                <form id="form-novo-professor" class="space-y-4">
                                    <div>
                                        <label for="modal_novo_professor_nome" class="block text-sm font-medium text-gray-700">Nome Completo <span class="text-red-500">*</span></label>
                                        <input type="text" name="novo_professor_nome" id="modal_novo_professor_nome" required class="mt-1 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label for="modal_novo_professor_formacao" class="block text-sm font-medium text-gray-700">Formação</label>
                                        <select name="novo_professor_formacao" id="modal_novo_professor_formacao" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">Selecione a formação</option>
                                            <option value="Graduado">Graduado</option>
                                            <option value="Especialista">Especialista</option>
                                            <option value="Mestre">Mestre</option>
                                            <option value="Doutor">Doutor</option>
                                            <option value="Pós-Doutor">Pós-Doutor</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" id="btn-salvar-professor" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <i class="fas fa-save mr-2"></i> Salvar
                    </button>
                    <button type="button" id="btn-cancelar-modal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Cancelar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Botões de Navegação -->
    <div class="flex justify-between items-center pt-6 border-t border-gray-200 mt-6">
        <a href="notas.php?action=lancar" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar
        </a>
        
        <div class="text-xs text-gray-500">
            <i class="fas fa-info-circle mr-1"></i>
            Selecione uma disciplina para continuar
        </div>
    </div>
</div>

<!-- Informações sobre Disciplinas -->
<div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-xl p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Sobre as Disciplinas</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>As disciplinas são vinculadas ao curso e podem ser reutilizadas em outras turmas</li>
                    <li>Se não encontrar a disciplina desejada, você pode cadastrar uma nova</li>
                    <li>O código da disciplina é opcional, mas ajuda na organização</li>
                    <li>A carga horária será usada para cálculos de frequência</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function selecionarDisciplina(disciplinaId) {
    const url = `notas.php?action=lancar&curso_id=<?php echo $curso_id; ?>&turma_id=<?php echo $turma_id; ?>&disciplina_id=${disciplinaId}`;
    window.location.href = url;
}

function toggleFormDisciplina() {
    const form = document.getElementById('form-nova-disciplina');
    const btnText = document.getElementById('btn-toggle-text');
    const isHidden = form.classList.contains('hidden');
    form.classList.toggle('hidden');
    btnText.textContent = isHidden ? 'Ocultar Formulário' : 'Mostrar Formulário';
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM carregado, inicializando modal de professor...');

    const professorSelect = document.getElementById('professor_id');
    const modal = document.getElementById('modal-novo-professor');
    const btnSalvar = document.getElementById('btn-salvar-professor');
    const btnCancelar = document.getElementById('btn-cancelar-modal');
    const formNovoProfessor = document.getElementById('form-novo-professor');

    // Debug: verificar se elementos foram encontrados
    console.log('Elementos encontrados:');
    console.log('- professorSelect:', professorSelect);
    console.log('- modal:', modal);
    console.log('- btnSalvar:', btnSalvar);
    console.log('- btnCancelar:', btnCancelar);
    console.log('- formNovoProfessor:', formNovoProfessor);

    function openModal() {
        console.log('Abrindo modal...');
        if (modal) {
            modal.classList.remove('hidden');
            console.log('Modal aberto com sucesso');
        } else {
            console.error('Modal não encontrado!');
        }
    }

    function closeModal() {
        console.log('Fechando modal...');
        if (modal) {
            modal.classList.add('hidden');
        }
        if (formNovoProfessor) {
            formNovoProfessor.reset();
        }
        // Reverte a seleção para "Selecione um professor" caso o usuário cancele
        if (professorSelect && professorSelect.value === 'novo') {
            professorSelect.value = "";
        }
    }

    if (professorSelect) {
        console.log('Adicionando event listener ao select de professor...');
        professorSelect.addEventListener('change', function() {
            console.log('Select mudou para:', this.value);
            if (this.value === 'novo') {
                console.log('Valor "novo" selecionado, abrindo modal...');
                openModal();
            }
        });
    } else {
        console.error('Select de professor não encontrado!');
    }

    btnCancelar.addEventListener('click', closeModal);

    btnSalvar.addEventListener('click', function() {
        const nomeInput = document.getElementById('modal_novo_professor_nome');
        const formacaoInput = document.getElementById('modal_novo_professor_formacao');

        const nome = nomeInput.value.trim();
        const formacao = formacaoInput.value;

        if (!nome) {
            alert('Por favor, preencha o nome do professor.');
            return;
        }

        const formData = new FormData();
        formData.append('action', 'cadastrar_professor_ajax');
        formData.append('novo_professor_nome', nome);
        formData.append('novo_professor_formacao', formacao);
        
        // Adiciona o token CSRF se existir
        const csrfToken = document.querySelector('input[name="csrf_token"]');
        if (csrfToken) {
            formData.append('csrf_token', csrfToken.value);
        }

        fetch('notas.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.sucesso && data.professor) {
                const newOption = new Option(data.professor.nome, data.professor.id, true, true);
                professorSelect.add(newOption, professorSelect.options[professorSelect.options.length - 1]);
                closeModal();
            } else {
                alert('Erro ao cadastrar professor: ' + (data.erro || 'Erro desconhecido.'));
                // Mantém o modal aberto para correção
            }
        })
        .catch(error => {
            console.error('Erro na requisição:', error);
            alert('Ocorreu um erro de comunicação com o servidor.');
        });
    });
});

// Adiciona efeito hover nos cards de disciplina
document.addEventListener('DOMContentLoaded', function() {
    const disciplinaCards = document.querySelectorAll('[onclick^="selecionarDisciplina"]');
    
    disciplinaCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('bg-blue-50');
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('bg-blue-50');
        });
    });
});
</script>
