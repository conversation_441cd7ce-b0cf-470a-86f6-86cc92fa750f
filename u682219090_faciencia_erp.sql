-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Tempo de geração: 11/07/2025 às 18:16
-- Vers<PERSON> do servidor: 10.4.32-MariaDB
-- Versão do PHP: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON>co de dados: `u682219090_faciencia_erp`
--

-- --------------------------------------------------------

--
-- Estrutura para tabela `agendamentos`
--

CREATE TABLE `agendamentos` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `tipo_agendamento_id` int(11) NOT NULL,
  `data_agendamento` date NOT NULL,
  `hora_inicio` time NOT NULL,
  `hora_fim` time NOT NULL,
  `observacoes` text DEFAULT NULL,
  `status` enum('agendado','confirmado','cancelado','concluido') DEFAULT 'agendado',
  `criado_em` timestamp NULL DEFAULT current_timestamp(),
  `atualizado_em` timestamp NULL DEFAULT current_timestamp(),
  `cancelado_em` timestamp NULL DEFAULT NULL,
  `motivo_cancelamento` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `agendamentos_log`
--

CREATE TABLE `agendamentos_log` (
  `id` int(11) NOT NULL,
  `agendamento_id` int(11) NOT NULL,
  `acao` varchar(50) NOT NULL,
  `dados_anteriores` longtext DEFAULT NULL CHECK (json_valid(`dados_anteriores`)),
  `dados_novos` longtext DEFAULT NULL CHECK (json_valid(`dados_novos`)),
  `usuario_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `criado_em` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `agendamentos_pagamentos`
--

CREATE TABLE `agendamentos_pagamentos` (
  `id` int(11) NOT NULL,
  `funcionario_id` int(11) NOT NULL,
  `tipo` enum('salario','adiantamento','bonus','ferias','13_salario','outros') NOT NULL DEFAULT 'salario',
  `valor` decimal(10,2) NOT NULL,
  `dia_vencimento` int(11) NOT NULL COMMENT 'Dia do mês para pagamento',
  `forma_pagamento` enum('pix','transferencia','cheque','dinheiro') NOT NULL DEFAULT 'transferencia',
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `alunos`
--

CREATE TABLE `alunos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `usuario_id` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `nome_social` varchar(150) DEFAULT NULL,
  `cpf` varchar(20) DEFAULT NULL,
  `rg` varchar(20) DEFAULT NULL,
  `data_nascimento` date DEFAULT NULL,
  `sexo` enum('masculino','feminino','outro') DEFAULT NULL,
  `naturalidade_id` int(10) UNSIGNED DEFAULT NULL,
  `estado_civil_id` int(10) UNSIGNED DEFAULT NULL,
  `situacao_id` int(10) UNSIGNED DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `foto_perfil` varchar(255) DEFAULT NULL,
  `biografia` text DEFAULT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `celular` varchar(20) DEFAULT NULL,
  `endereco` varchar(255) DEFAULT NULL,
  `numero` varchar(20) DEFAULT NULL,
  `bairro` varchar(100) DEFAULT NULL,
  `cidade_id` int(10) UNSIGNED DEFAULT NULL,
  `cep` varchar(10) DEFAULT NULL,
  `polo_id` int(10) UNSIGNED DEFAULT NULL,
  `curso_id` int(10) UNSIGNED DEFAULT NULL,
  `professor_orientador_id` int(10) UNSIGNED DEFAULT NULL,
  `data_ingresso` date DEFAULT NULL,
  `curso_inicio` date DEFAULT NULL,
  `curso_fim` date DEFAULT NULL,
  `previsao_conclusao` date DEFAULT NULL,
  `mono_titulo` varchar(255) DEFAULT NULL,
  `mono_data` date DEFAULT NULL,
  `mono_nota` decimal(5,2) DEFAULT NULL,
  `mono_prazo` date DEFAULT NULL,
  `status` enum('ativo','trancado','cancelado','formado','desistente') DEFAULT 'ativo',
  `acesso_ava` tinyint(1) NOT NULL DEFAULT 0,
  `primeiro_acesso` tinyint(1) NOT NULL DEFAULT 1,
  `entregou_diploma` tinyint(1) DEFAULT 0,
  `entregou_cpf` tinyint(1) DEFAULT 0,
  `entregou_rg` tinyint(1) DEFAULT 0,
  `bolsa` decimal(10,2) DEFAULT NULL,
  `desconto` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `cidade` text NOT NULL,
  `estado` text NOT NULL,
  `turma_id` int(150) NOT NULL,
  `expedidor` text NOT NULL,
  `orgao_expedidor` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `alunos_acesso`
--

CREATE TABLE `alunos_acesso` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `email` varchar(255) NOT NULL,
  `senha` varchar(255) NOT NULL,
  `status` enum('ativo','inativo','bloqueado') DEFAULT 'ativo',
  `primeiro_acesso` tinyint(1) DEFAULT 1,
  `ultimo_acesso` datetime DEFAULT NULL,
  `tentativas_login` int(11) DEFAULT 0,
  `bloqueado_ate` datetime DEFAULT NULL,
  `token_recuperacao` varchar(255) DEFAULT NULL,
  `token_expira_em` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `alunos_atividades`
--

CREATE TABLE `alunos_atividades` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `tipo` varchar(50) NOT NULL,
  `descricao` text NOT NULL,
  `ip` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `dados_extras` longtext DEFAULT NULL CHECK (json_valid(`dados_extras`)),
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `alunos_preferencias`
--

CREATE TABLE `alunos_preferencias` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `tema` enum('light','dark') DEFAULT 'light',
  `notificacoes_email` tinyint(1) DEFAULT 1,
  `notificacoes_sistema` tinyint(1) DEFAULT 1,
  `idioma` varchar(5) DEFAULT 'pt-BR',
  `timezone` varchar(50) DEFAULT 'America/Sao_Paulo',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `alunos_sessoes`
--

CREATE TABLE `alunos_sessoes` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `ip` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `areas_conhecimento`
--

CREATE TABLE `areas_conhecimento` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `descricao` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `asaas_customers`
--

CREATE TABLE `asaas_customers` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `asaas_customer_id` varchar(255) NOT NULL COMMENT 'ID do cliente no Asaas',
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `cpf_cnpj` varchar(20) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `asaas_webhooks`
--

CREATE TABLE `asaas_webhooks` (
  `id` int(11) NOT NULL,
  `event_type` varchar(100) NOT NULL COMMENT 'Tipo do evento (PAYMENT_RECEIVED, etc)',
  `payment_id` varchar(255) NOT NULL COMMENT 'ID da cobrança no Asaas',
  `customer_id` varchar(255) DEFAULT NULL COMMENT 'ID do cliente no Asaas',
  `webhook_data` longtext NOT NULL COMMENT 'Dados completos do webhook',
  `processed` tinyint(1) DEFAULT 0 COMMENT 'Se o webhook foi processado',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT 'Quando foi processado',
  `error_message` text DEFAULT NULL COMMENT 'Mensagem de erro se houver',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_acessos`
--

CREATE TABLE `ava_acessos` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `data_acesso` datetime NOT NULL,
  `ip` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `pagina` varchar(255) DEFAULT NULL,
  `tempo_sessao` int(11) DEFAULT 0,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_aulas`
--

CREATE TABLE `ava_aulas` (
  `id` int(11) NOT NULL,
  `modulo_id` int(11) NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `tipo` enum('video','texto','quiz','arquivo','link') NOT NULL,
  `conteudo` text DEFAULT NULL,
  `url_video` varchar(255) DEFAULT NULL,
  `arquivo` varchar(255) DEFAULT NULL,
  `duracao` int(11) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `arquivo_path` varchar(255) DEFAULT NULL,
  `link_url` varchar(255) DEFAULT NULL,
  `duracao_minutos` int(11) DEFAULT NULL,
  `ordem` int(11) NOT NULL DEFAULT 0,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_categorias`
--

CREATE TABLE `ava_categorias` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `cor` varchar(20) DEFAULT '#6A5ACD',
  `icone` varchar(50) DEFAULT 'fas fa-book',
  `ordem` int(11) NOT NULL DEFAULT 0,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_certificados`
--

CREATE TABLE `ava_certificados` (
  `id` int(11) NOT NULL,
  `matricula_id` int(11) NOT NULL,
  `codigo` varchar(50) NOT NULL,
  `data_emissao` datetime NOT NULL,
  `arquivo_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_cursos`
--

CREATE TABLE `ava_cursos` (
  `id` int(11) UNSIGNED NOT NULL,
  `polo_id` int(11) UNSIGNED NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `carga_horaria` int(11) DEFAULT NULL,
  `categoria` varchar(100) DEFAULT NULL,
  `nivel` enum('basico','intermediario','avancado') DEFAULT 'basico',
  `imagem` varchar(255) DEFAULT NULL,
  `preco` decimal(10,2) DEFAULT NULL,
  `preco_promocional` decimal(10,2) DEFAULT NULL,
  `data_inicio` date DEFAULT NULL,
  `data_fim` date DEFAULT NULL,
  `requisitos` text DEFAULT NULL,
  `video_apresentacao` varchar(255) DEFAULT NULL,
  `objetivos` text DEFAULT NULL,
  `metodologia` text DEFAULT NULL,
  `avaliacao` text DEFAULT NULL,
  `certificacao` text DEFAULT NULL,
  `destaque` tinyint(1) NOT NULL DEFAULT 0,
  `visibilidade` enum('publico','privado') NOT NULL DEFAULT 'publico',
  `publico_alvo` text DEFAULT NULL,
  `pre_requisitos` text DEFAULT NULL,
  `status` enum('rascunho','revisao','publicado','arquivado') DEFAULT 'rascunho',
  `data_publicacao` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_imagens`
--

CREATE TABLE `ava_imagens` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `aula_id` int(11) DEFAULT NULL,
  `arquivo_path` varchar(255) NOT NULL,
  `arquivo_nome` varchar(255) NOT NULL,
  `arquivo_tipo` varchar(100) NOT NULL,
  `arquivo_tamanho` int(11) NOT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_materiais`
--

CREATE TABLE `ava_materiais` (
  `id` int(11) NOT NULL,
  `aula_id` int(11) NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `tipo` enum('pdf','doc','xls','ppt','zip','imagem','audio','video','link') NOT NULL,
  `arquivo_path` varchar(255) DEFAULT NULL,
  `link_url` varchar(255) DEFAULT NULL,
  `ordem` int(11) NOT NULL DEFAULT 0,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_matriculas`
--

CREATE TABLE `ava_matriculas` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `curso_id` int(11) NOT NULL,
  `data_matricula` datetime NOT NULL DEFAULT current_timestamp(),
  `status` enum('ativo','concluido','cancelado','trancado') DEFAULT 'ativo',
  `progresso` int(11) NOT NULL DEFAULT 0,
  `data_conclusao` datetime DEFAULT NULL,
  `nota_final` decimal(5,2) DEFAULT NULL,
  `certificado_emitido` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_modulos`
--

CREATE TABLE `ava_modulos` (
  `id` int(11) NOT NULL,
  `curso_id` int(11) UNSIGNED NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `descricao` text DEFAULT NULL,
  `ordem` int(11) NOT NULL DEFAULT 0,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_polos_acesso`
--

CREATE TABLE `ava_polos_acesso` (
  `id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED NOT NULL,
  `liberado` tinyint(1) NOT NULL DEFAULT 0,
  `data_liberacao` datetime DEFAULT NULL,
  `liberado_por` int(10) UNSIGNED DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_progresso`
--

CREATE TABLE `ava_progresso` (
  `id` int(11) NOT NULL,
  `matricula_id` int(11) NOT NULL,
  `aula_id` int(11) NOT NULL,
  `concluido` tinyint(1) NOT NULL DEFAULT 0,
  `data_conclusao` datetime DEFAULT NULL,
  `tempo_gasto` int(11) DEFAULT NULL,
  `nota` decimal(5,2) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  `data_inicio` datetime DEFAULT NULL,
  `pontuacao` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_progresso_aulas`
--

CREATE TABLE `ava_progresso_aulas` (
  `id` int(11) NOT NULL,
  `matricula_id` int(11) NOT NULL,
  `aula_id` int(11) NOT NULL,
  `status` enum('nao_iniciada','em_andamento','concluida') DEFAULT 'nao_iniciada',
  `data_inicio` datetime DEFAULT NULL,
  `data_conclusao` datetime DEFAULT NULL,
  `tempo_total_segundos` int(11) DEFAULT 0,
  `nota` decimal(5,2) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_questoes`
--

CREATE TABLE `ava_questoes` (
  `id` int(11) NOT NULL,
  `aula_id` int(11) NOT NULL,
  `pergunta` text NOT NULL,
  `tipo` enum('multipla_escolha','verdadeiro_falso','resposta_curta','correspondencia') NOT NULL,
  `opcoes` text DEFAULT NULL,
  `resposta_correta` text NOT NULL,
  `explicacao` text DEFAULT NULL,
  `pontos` int(11) NOT NULL DEFAULT 1,
  `ordem` int(11) NOT NULL DEFAULT 0,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `ava_respostas_alunos`
--

CREATE TABLE `ava_respostas_alunos` (
  `id` int(11) NOT NULL,
  `progresso_aula_id` int(11) NOT NULL,
  `questao_id` int(11) NOT NULL,
  `resposta` text NOT NULL,
  `correta` tinyint(1) NOT NULL DEFAULT 0,
  `pontos_obtidos` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `boletos`
--

CREATE TABLE `boletos` (
  `id` int(11) NOT NULL,
  `tipo_entidade` enum('aluno','empresa','polo') NOT NULL,
  `entidade_id` int(10) UNSIGNED NOT NULL,
  `aluno_id` int(11) DEFAULT NULL,
  `nome_pagador` varchar(255) DEFAULT NULL,
  `cpf_pagador` varchar(20) DEFAULT NULL,
  `email_pagador` varchar(255) DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `mensalidade_id` int(11) DEFAULT NULL,
  `numero_boleto` varchar(100) NOT NULL,
  `codigo_boleto` varchar(50) DEFAULT NULL,
  `codigo_barras` varchar(255) NOT NULL,
  `linha_digitavel` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_emissao` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `valor_pago` decimal(10,2) DEFAULT NULL,
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `status` enum('pendente','pago','vencido','cancelado','gerado') DEFAULT 'pendente',
  `banco` varchar(10) NOT NULL,
  `agencia` varchar(10) NOT NULL,
  `conta` varchar(20) NOT NULL,
  `nosso_numero` varchar(50) NOT NULL,
  `arquivo_pdf` varchar(255) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `tipo_documento` enum('cpf','cnpj') DEFAULT NULL COMMENT 'Tipo do documento do pagador',
  `logradouro` varchar(100) DEFAULT NULL COMMENT 'Rua/Avenida',
  `numero_endereco` varchar(10) DEFAULT NULL COMMENT 'Número do endereço',
  `complemento` varchar(50) DEFAULT NULL COMMENT 'Complemento do endereço',
  `bairro` varchar(50) DEFAULT NULL COMMENT 'Bairro',
  `codigo_cliente` varchar(20) DEFAULT NULL COMMENT 'Código do cliente no sistema',
  `instrucoes_pagamento` text DEFAULT NULL COMMENT 'Instruções específicas de pagamento',
  `juros_mora` decimal(5,2) DEFAULT 0.00 COMMENT 'Percentual juros de mora',
  `multa_atraso` decimal(5,2) DEFAULT 0.00 COMMENT 'Percentual multa por atraso',
  `desconto` decimal(5,2) DEFAULT 0.00 COMMENT 'Percentual de desconto',
  `dias_desconto` int(11) DEFAULT 0 COMMENT 'Dias para aplicar desconto',
  `tentativas_api` int(11) DEFAULT 0 COMMENT 'Número de tentativas de geração via API',
  `ultimo_erro_api` text DEFAULT NULL COMMENT 'Último erro retornado pela API',
  `data_ultima_tentativa` timestamp NULL DEFAULT NULL COMMENT 'Data da última tentativa de geração',
  `cpf_cnpj_pagador` varchar(20) DEFAULT NULL,
  `endereco_pagador` varchar(255) DEFAULT NULL,
  `cidade_pagador` varchar(100) DEFAULT NULL,
  `cep_pagador` varchar(10) DEFAULT NULL,
  `uf_pagador` varchar(2) DEFAULT NULL,
  `status_api` enum('gerado','simulado','erro') DEFAULT NULL,
  `resposta_api_completa` longtext DEFAULT NULL CHECK (json_valid(`resposta_api_completa`)),
  `data_geracao` timestamp NULL DEFAULT current_timestamp(),
  `ambiente_api` enum('producao','teste') DEFAULT NULL,
  `url_boleto` varchar(500) DEFAULT NULL,
  `asaas_payment_id` varchar(255) DEFAULT NULL COMMENT 'ID da cobrança no Asaas',
  `asaas_customer_id` varchar(255) DEFAULT NULL COMMENT 'ID do cliente no Asaas',
  `asaas_status` varchar(50) DEFAULT NULL COMMENT 'Status da cobrança no Asaas',
  `asaas_boleto_url` varchar(500) DEFAULT NULL COMMENT 'URL do boleto gerado pelo Asaas',
  `asaas_linha_digitavel` varchar(255) DEFAULT NULL COMMENT 'Linha digitável do Asaas',
  `asaas_codigo_barras` varchar(255) DEFAULT NULL COMMENT 'Código de barras do Asaas',
  `gateway_pagamento` enum('itau','asaas') DEFAULT 'asaas' COMMENT 'Gateway utilizado para gerar o boleto',
  `asaas_webhook_data` longtext DEFAULT NULL COMMENT 'Dados recebidos via webhook do Asaas'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `boletos_config`
--

CREATE TABLE `boletos_config` (
  `id` int(11) NOT NULL,
  `banco` varchar(50) NOT NULL DEFAULT 'itau',
  `agencia` varchar(10) NOT NULL,
  `conta` varchar(20) NOT NULL,
  `carteira` varchar(10) NOT NULL,
  `cedente_nome` varchar(255) NOT NULL,
  `cedente_cnpj` varchar(20) NOT NULL,
  `cedente_endereco` text NOT NULL,
  `api_client_id` varchar(255) DEFAULT NULL,
  `api_client_secret` varchar(255) DEFAULT NULL,
  `api_environment` enum('sandbox','production') DEFAULT 'sandbox',
  `dias_vencimento_padrao` int(11) DEFAULT 30,
  `multa_percentual` decimal(5,2) DEFAULT 2.00,
  `juros_percentual_mes` decimal(5,2) DEFAULT 1.00,
  `instrucoes` text DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `boletos_historico`
--

CREATE TABLE `boletos_historico` (
  `id` int(11) NOT NULL,
  `boleto_id` int(11) NOT NULL,
  `acao` varchar(50) NOT NULL COMMENT 'cancelamento, cancelamento_local, emissao, pagamento, etc',
  `data` datetime NOT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `detalhes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `categorias_chamados`
--

CREATE TABLE `categorias_chamados` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `tipo` enum('interno','polo') NOT NULL COMMENT 'interno: para funcionários, polo: para polos',
  `requer_aprovacao` tinyint(1) NOT NULL DEFAULT 0,
  `departamento_responsavel` enum('secretaria','financeiro','suporte','diretoria') DEFAULT NULL,
  `cor` varchar(7) DEFAULT '#3498db',
  `icone` varchar(50) DEFAULT 'ticket',
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `ordem` int(11) DEFAULT 0,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `categorias_financeiras`
--

CREATE TABLE `categorias_financeiras` (
  `id` int(10) UNSIGNED NOT NULL,
  `nome` varchar(100) NOT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `cor` varchar(8) NOT NULL DEFAULT '#3498db',
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `icone` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados`
--

CREATE TABLE `chamados` (
  `id` int(11) NOT NULL,
  `codigo` varchar(20) NOT NULL COMMENT 'Código único do chamado (ex: TICK-2024-0001)',
  `titulo` varchar(255) NOT NULL,
  `descricao` text NOT NULL,
  `categoria_id` int(11) NOT NULL,
  `tipo` enum('interno','polo') NOT NULL COMMENT 'interno: para funcionários, polo: para polos',
  `prioridade` enum('baixa','media','alta','urgente') NOT NULL DEFAULT 'media',
  `status` enum('aberto','em_andamento','aguardando_resposta','aguardando_aprovacao','resolvido','cancelado','fechado') NOT NULL DEFAULT 'aberto',
  `solicitante_id` int(10) UNSIGNED NOT NULL COMMENT 'ID do usuário que abriu o chamado',
  `responsavel_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID do usuário responsável pelo atendimento',
  `departamento` enum('secretaria','financeiro','suporte','diretoria') DEFAULT NULL,
  `polo_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID do polo relacionado (se aplicável)',
  `aluno_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'ID do aluno relacionado (se aplicável)',
  `data_abertura` datetime NOT NULL,
  `data_ultima_atualizacao` datetime DEFAULT NULL,
  `data_fechamento` datetime DEFAULT NULL,
  `tempo_resolucao` int(11) DEFAULT NULL COMMENT 'Tempo de resolução em minutos',
  `avaliacao` int(11) DEFAULT NULL COMMENT 'Avaliação de 1 a 5 estrelas',
  `comentario_avaliacao` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados_alunos`
--

CREATE TABLE `chamados_alunos` (
  `id` int(11) UNSIGNED NOT NULL,
  `chamado_id` int(11) UNSIGNED NOT NULL,
  `aluno_id` int(11) UNSIGNED NOT NULL,
  `documento_gerado` tinyint(1) NOT NULL DEFAULT 0,
  `arquivo_path` varchar(255) DEFAULT NULL,
  `data_geracao` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados_anexos`
--

CREATE TABLE `chamados_anexos` (
  `id` int(11) NOT NULL,
  `chamado_id` int(11) DEFAULT NULL,
  `resposta_id` int(11) DEFAULT NULL,
  `nome_arquivo` varchar(255) NOT NULL,
  `caminho_arquivo` varchar(255) NOT NULL,
  `tipo_arquivo` varchar(100) DEFAULT NULL,
  `tamanho_arquivo` int(11) DEFAULT NULL,
  `usuario_id` int(10) UNSIGNED NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados_documentos`
--

CREATE TABLE `chamados_documentos` (
  `id` int(11) NOT NULL,
  `chamado_id` int(11) NOT NULL,
  `tipo_documento_id` int(10) UNSIGNED NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED NOT NULL,
  `quantidade` int(11) NOT NULL DEFAULT 1,
  `finalidade` text DEFAULT NULL,
  `status` enum('solicitado','processando','pronto','entregue','cancelado') DEFAULT 'solicitado',
  `valor_unitario` decimal(10,2) DEFAULT NULL,
  `valor_total` decimal(10,2) DEFAULT NULL,
  `pago` tinyint(1) DEFAULT 0,
  `data_pagamento` datetime DEFAULT NULL,
  `documento_gerado_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Acionadores `chamados_documentos`
--
DELIMITER $$
CREATE TRIGGER `after_chamados_documentos_insert` AFTER INSERT ON `chamados_documentos` FOR EACH ROW BEGIN
    
    UPDATE polos_financeiro
    SET documentos_disponiveis = documentos_disponiveis - NEW.quantidade,
        documentos_emitidos = documentos_emitidos + NEW.quantidade,
        updated_at = NOW()
    WHERE polo_id = NEW.polo_id AND 
          documentos_disponiveis >= NEW.quantidade;
          
    
    UPDATE polos
    SET documentos_emitidos = documentos_emitidos + NEW.quantidade,
        updated_at = NOW()
    WHERE id = NEW.polo_id;
    
    
    INSERT INTO polos_financeiro_historico (
        polo_id,
        tipo_polo_id,
        tipo_transacao,
        valor,
        quantidade,
        data_transacao,
        descricao,
        usuario_id,
        created_at
    )
    SELECT 
        NEW.polo_id,
        pt.tipo_polo_id,
        'documento',
        COALESCE(pf.valor_por_documento, tpf.valor_documento),
        NEW.quantidade,
        NOW(),
        CONCAT('Solicitação de documento via chamado #', NEW.chamado_id),
        (SELECT solicitante_id FROM chamados WHERE id = NEW.chamado_id),
        NOW()
    FROM polos_tipos pt
    JOIN tipos_polos_financeiro tpf ON pt.tipo_polo_id = tpf.tipo_polo_id
    LEFT JOIN polos_financeiro pf ON pf.polo_id = NEW.polo_id AND pf.tipo_polo_id = pt.tipo_polo_id
    WHERE pt.polo_id = NEW.polo_id
    LIMIT 1;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `after_chamados_documentos_update` AFTER UPDATE ON `chamados_documentos` FOR EACH ROW BEGIN
    IF NEW.status = 'cancelado' AND OLD.status != 'cancelado' THEN
        
        UPDATE polos_financeiro
        SET documentos_disponiveis = documentos_disponiveis + NEW.quantidade,
            documentos_emitidos = documentos_emitidos - NEW.quantidade,
            updated_at = NOW()
        WHERE polo_id = NEW.polo_id;
        
        
        UPDATE polos
        SET documentos_emitidos = documentos_emitidos - NEW.quantidade,
            updated_at = NOW()
        WHERE id = NEW.polo_id;
        
        
        INSERT INTO polos_financeiro_historico (
            polo_id,
            tipo_polo_id,
            tipo_transacao,
            valor,
            quantidade,
            data_transacao,
            descricao,
            usuario_id,
            created_at
        )
        SELECT 
            NEW.polo_id,
            pt.tipo_polo_id,
            'documento',
            -COALESCE(pf.valor_por_documento, tpf.valor_documento),
            NEW.quantidade,
            NOW(),
            CONCAT('Cancelamento de solicitação de documento via chamado #', NEW.chamado_id),
            (SELECT solicitante_id FROM chamados WHERE id = NEW.chamado_id),
            NOW()
        FROM polos_tipos pt
        JOIN tipos_polos_financeiro tpf ON pt.tipo_polo_id = tpf.tipo_polo_id
        LEFT JOIN polos_financeiro pf ON pf.polo_id = NEW.polo_id AND pf.tipo_polo_id = pt.tipo_polo_id
        WHERE pt.polo_id = NEW.polo_id
        LIMIT 1;
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados_historico`
--

CREATE TABLE `chamados_historico` (
  `id` int(11) UNSIGNED NOT NULL,
  `chamado_id` int(11) UNSIGNED NOT NULL,
  `usuario_id` int(11) UNSIGNED NOT NULL,
  `acao` varchar(50) NOT NULL,
  `descricao` text NOT NULL,
  `data_hora` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `chamados_respostas`
--

CREATE TABLE `chamados_respostas` (
  `id` int(11) NOT NULL,
  `chamado_id` int(11) NOT NULL,
  `usuario_id` int(10) UNSIGNED NOT NULL,
  `mensagem` text NOT NULL,
  `tipo` enum('resposta','nota_interna','alteracao_status','sistema') NOT NULL DEFAULT 'resposta',
  `visivel_solicitante` tinyint(1) NOT NULL DEFAULT 1,
  `data_resposta` datetime NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `cidades`
--

CREATE TABLE `cidades` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `estado_id` int(10) UNSIGNED NOT NULL,
  `cep` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `cobranca_polos`
--

CREATE TABLE `cobranca_polos` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `mes_referencia` date NOT NULL,
  `tipo_cobranca` enum('mensalidade','taxa','outros') NOT NULL DEFAULT 'mensalidade',
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `status` enum('pendente','pago','cancelado') NOT NULL DEFAULT 'pendente',
  `observacoes` text DEFAULT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `comprovantes_pagamento`
--

CREATE TABLE `comprovantes_pagamento` (
  `id` int(11) NOT NULL,
  `mensalidade_id` int(11) NOT NULL,
  `boleto_id` int(11) DEFAULT NULL,
  `tipo_comprovante` enum('boleto','transferencia','pix','cartao','dinheiro') NOT NULL,
  `numero_comprovante` varchar(100) DEFAULT NULL,
  `valor_pago` decimal(10,2) NOT NULL,
  `data_pagamento` date NOT NULL,
  `banco_origem` varchar(100) DEFAULT NULL,
  `banco_destino` varchar(100) DEFAULT NULL,
  `arquivo_comprovante` varchar(255) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `validado` tinyint(1) DEFAULT 0,
  `validado_por` int(11) DEFAULT NULL,
  `validado_em` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `configuracoes`
--

CREATE TABLE `configuracoes` (
  `id` int(11) NOT NULL,
  `chave` varchar(100) NOT NULL,
  `valor` text NOT NULL,
  `descricao` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `configuracoes_documentos`
--

CREATE TABLE `configuracoes_documentos` (
  `id` int(11) NOT NULL,
  `chave` varchar(100) NOT NULL,
  `valor` text DEFAULT NULL,
  `descricao` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `configuracoes_financeiras`
--

CREATE TABLE `configuracoes_financeiras` (
  `id` int(11) NOT NULL,
  `chave` varchar(100) NOT NULL,
  `valor` text DEFAULT NULL,
  `descricao` varchar(255) DEFAULT NULL,
  `tipo` enum('texto','numero','boolean','json') DEFAULT 'texto',
  `grupo` varchar(50) DEFAULT 'geral',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `configuracoes_sistema`
--

CREATE TABLE `configuracoes_sistema` (
  `id` int(10) UNSIGNED NOT NULL,
  `chave` varchar(100) NOT NULL,
  `valor` text DEFAULT NULL,
  `tipo` enum('string','numero','booleano','json') NOT NULL DEFAULT 'string',
  `descricao` text DEFAULT NULL,
  `grupo` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `contas_bancarias`
--

CREATE TABLE `contas_bancarias` (
  `id` int(11) NOT NULL,
  `id_usuario` int(11) DEFAULT NULL,
  `id_integracao_bancaria` int(11) DEFAULT NULL COMMENT 'Link para a integração bancária (OFX, API)',
  `nome` varchar(100) NOT NULL,
  `banco` varchar(100) DEFAULT NULL,
  `agencia` varchar(20) DEFAULT NULL,
  `conta` varchar(20) DEFAULT NULL,
  `tipo` enum('corrente','poupanca','investimento','caixa') NOT NULL DEFAULT 'corrente',
  `saldo_inicial` decimal(10,2) NOT NULL DEFAULT 0.00,
  `sincronizacao_automatica` tinyint(1) NOT NULL DEFAULT 0,
  `data_ultima_sincronizacao` datetime DEFAULT NULL,
  `saldo_atual` decimal(10,2) NOT NULL DEFAULT 0.00,
  `data_saldo` date NOT NULL,
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `contas_pagar`
--

CREATE TABLE `contas_pagar` (
  `id` int(11) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(15,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `fornecedor_id` int(11) DEFAULT NULL,
  `fornecedor_nome` varchar(100) DEFAULT NULL,
  `categoria_id` int(11) DEFAULT NULL,
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `status` enum('pendente','pago','cancelado') NOT NULL DEFAULT 'pendente',
  `observacoes` text DEFAULT NULL,
  `comprovante_path` varchar(255) DEFAULT NULL,
  `transacao_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `multa` decimal(15,2) DEFAULT 0.00,
  `juros` decimal(15,2) DEFAULT 0.00,
  `desconto` decimal(15,2) DEFAULT 0.00,
  `valor_total` decimal(15,2) GENERATED ALWAYS AS (`valor` + `multa` + `juros` - `desconto`) STORED,
  `conta_bancaria_id` int(11) DEFAULT NULL,
  `usuario_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `contas_pagar_rh`
--

CREATE TABLE `contas_pagar_rh` (
  `id` int(11) NOT NULL,
  `pagamento_id` int(11) NOT NULL,
  `conta_pagar_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `contas_receber`
--

CREATE TABLE `contas_receber` (
  `id` int(11) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(15,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_recebimento` date DEFAULT NULL,
  `cliente_id` int(11) DEFAULT NULL,
  `cliente_nome` varchar(100) DEFAULT NULL,
  `categoria_id` int(11) DEFAULT NULL,
  `forma_recebimento` varchar(50) DEFAULT NULL,
  `status` enum('pendente','recebido','cancelado') NOT NULL DEFAULT 'pendente',
  `observacoes` text DEFAULT NULL,
  `comprovante_path` varchar(255) DEFAULT NULL,
  `transacao_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL,
  `multa` decimal(15,2) DEFAULT 0.00,
  `juros` decimal(15,2) DEFAULT 0.00,
  `desconto` decimal(15,2) DEFAULT 0.00,
  `valor_total` decimal(15,2) GENERATED ALWAYS AS (`valor` + `multa` + `juros` - `desconto`) STORED,
  `cliente_tipo` enum('aluno','polo','terceiro') DEFAULT 'terceiro',
  `conta_bancaria_id` int(11) DEFAULT NULL,
  `usuario_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `cursos`
--

CREATE TABLE `cursos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `sigla` varchar(10) DEFAULT NULL,
  `area_conhecimento_id` int(10) UNSIGNED DEFAULT NULL,
  `grupo_id` int(10) UNSIGNED DEFAULT NULL,
  `coordenador_id` int(10) UNSIGNED DEFAULT NULL,
  `carga_horaria` int(11) NOT NULL,
  `descricao` text DEFAULT NULL,
  `objetivo` text DEFAULT NULL,
  `modalidade` enum('presencial','ead','hibrido') NOT NULL,
  `nivel` enum('graduacao','pos_graduacao','extensao','tecnico') NOT NULL,
  `status` enum('ativo','inativo','em_desenvolvimento') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL,
  `data_fim` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `polo_id` int(225) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `cursos_backup`
--

CREATE TABLE `cursos_backup` (
  `id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `sigla` varchar(10) DEFAULT NULL,
  `area_conhecimento_id` int(10) UNSIGNED DEFAULT NULL,
  `grupo_id` int(10) UNSIGNED DEFAULT NULL,
  `coordenador_id` int(10) UNSIGNED DEFAULT NULL,
  `carga_horaria` int(11) NOT NULL,
  `descricao` text DEFAULT NULL,
  `objetivo` text DEFAULT NULL,
  `modalidade` enum('presencial','ead','hibrido') NOT NULL,
  `nivel` enum('graduacao','pos_graduacao','extensao','tecnico') NOT NULL,
  `status` enum('ativo','inativo','em_desenvolvimento') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL,
  `data_fim` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `polo_id` int(225) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `disciplinas`
--

CREATE TABLE `disciplinas` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `codigo` varchar(20) DEFAULT NULL,
  `curso_id` int(10) UNSIGNED NOT NULL,
  `professor_padrao_id` int(10) UNSIGNED DEFAULT NULL,
  `carga_horaria` int(11) NOT NULL,
  `ementa` text DEFAULT NULL,
  `bibliografia` text DEFAULT NULL,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `periodo` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `documentos`
--

CREATE TABLE `documentos` (
  `id` int(11) NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `tipo_documento_id` int(10) UNSIGNED NOT NULL,
  `aluno_id` int(10) UNSIGNED DEFAULT NULL,
  `numero` varchar(50) DEFAULT NULL,
  `data_emissao` date DEFAULT NULL,
  `data_validade` date DEFAULT NULL,
  `orgao_emissor` varchar(100) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `arquivo_path` varchar(255) DEFAULT NULL,
  `arquivo_nome` varchar(255) DEFAULT NULL,
  `arquivo_tipo` varchar(100) DEFAULT NULL,
  `arquivo_tamanho` int(11) DEFAULT NULL,
  `status` enum('ativo','inativo','cancelado') NOT NULL DEFAULT 'ativo',
  `id_legado` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `documentos_alunos`
--

CREATE TABLE `documentos_alunos` (
  `id` int(11) NOT NULL,
  `aluno_id` int(11) NOT NULL,
  `tipo` varchar(50) NOT NULL,
  `tipo_documento_id` int(11) NOT NULL,
  `arquivo` varchar(255) NOT NULL,
  `data_upload` datetime NOT NULL DEFAULT current_timestamp(),
  `data_validade` date DEFAULT NULL,
  `numero_documento` varchar(100) DEFAULT NULL,
  `orgao_emissor` varchar(100) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `status` enum('pendente','aprovado','rejeitado') NOT NULL DEFAULT 'pendente',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `documentos_emitidos`
--

CREATE TABLE `documentos_emitidos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `solicitacao_id` int(10) UNSIGNED NOT NULL,
  `arquivo` varchar(255) DEFAULT NULL,
  `numero_documento` varchar(50) DEFAULT NULL,
  `data_emissao` date NOT NULL,
  `status` enum('ativo','cancelado') DEFAULT 'ativo',
  `aluno_id` int(220) NOT NULL,
  `matricula_id` int(220) NOT NULL,
  `curso_id` int(220) NOT NULL,
  `polo_id` int(220) NOT NULL,
  `tipo_documento_id` int(220) NOT NULL,
  `data_validade` date NOT NULL,
  `codigo_verificacao` varchar(255) DEFAULT NULL,
  `data_solicitacao` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `emails_enviados`
--

CREATE TABLE `emails_enviados` (
  `id` int(11) NOT NULL,
  `solicitacao_id` int(11) NOT NULL,
  `assunto` varchar(255) NOT NULL,
  `mensagem` text NOT NULL,
  `arquivo_nome` varchar(255) DEFAULT NULL,
  `drive_link` text DEFAULT NULL,
  `data_envio` datetime NOT NULL DEFAULT current_timestamp(),
  `usuario_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `estados`
--

CREATE TABLE `estados` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `sigla` varchar(2) NOT NULL,
  `pais` varchar(50) DEFAULT 'Brasil',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_auditoria`
--

CREATE TABLE `financeiro_auditoria` (
  `id` bigint(20) NOT NULL,
  `tabela_afetada` varchar(100) NOT NULL,
  `registro_id` int(11) NOT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `acao` enum('INSERT','UPDATE','DELETE') NOT NULL,
  `dados_antigos` longtext DEFAULT NULL CHECK (json_valid(`dados_antigos`)),
  `dados_novos` longtext DEFAULT NULL CHECK (json_valid(`dados_novos`)),
  `timestamp` timestamp NULL DEFAULT current_timestamp(),
  `ip_origem` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_configuracoes`
--

CREATE TABLE `financeiro_configuracoes` (
  `id` int(11) NOT NULL,
  `chave` varchar(100) NOT NULL,
  `valor` text DEFAULT NULL,
  `descricao` varchar(255) DEFAULT NULL,
  `data_criacao` timestamp NULL DEFAULT current_timestamp(),
  `data_atualizacao` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_integracoes`
--

CREATE TABLE `financeiro_integracoes` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `tipo` enum('gateway_pagamento','banco','outro') NOT NULL,
  `api_key` text DEFAULT NULL,
  `api_secret` text DEFAULT NULL,
  `webhook_url` varchar(255) DEFAULT NULL,
  `ativo` tinyint(1) NOT NULL DEFAULT 1,
  `data_criacao` timestamp NULL DEFAULT current_timestamp(),
  `data_atualizacao` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_logs_integracao`
--

CREATE TABLE `financeiro_logs_integracao` (
  `id` bigint(20) NOT NULL,
  `id_integracao` int(11) DEFAULT NULL,
  `timestamp` timestamp NULL DEFAULT current_timestamp(),
  `endpoint` varchar(255) DEFAULT NULL,
  `request_payload` text DEFAULT NULL,
  `response_code` varchar(20) DEFAULT NULL,
  `response_body` text DEFAULT NULL,
  `status` enum('sucesso','erro','aviso') NOT NULL,
  `ip_origem` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_notificacoes`
--

CREATE TABLE `financeiro_notificacoes` (
  `id` int(11) NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `mensagem` text NOT NULL,
  `tipo` enum('alerta','info','sucesso','erro') NOT NULL DEFAULT 'info',
  `prioridade` enum('baixa','media','alta') NOT NULL DEFAULT 'media',
  `link_destino` varchar(255) DEFAULT NULL,
  `data_criacao` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `financeiro_notificacoes_usuarios`
--

CREATE TABLE `financeiro_notificacoes_usuarios` (
  `id` bigint(20) NOT NULL,
  `id_notificacao` int(11) NOT NULL,
  `id_usuario` int(11) NOT NULL,
  `data_leitura` datetime DEFAULT NULL,
  `status` enum('nao_lida','lida','arquivada') NOT NULL DEFAULT 'nao_lida'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `folha_pagamento`
--

CREATE TABLE `folha_pagamento` (
  `id` int(11) NOT NULL,
  `mes_referencia` int(11) NOT NULL,
  `ano_referencia` int(11) NOT NULL,
  `data_geracao` datetime NOT NULL,
  `valor_total` decimal(10,2) NOT NULL,
  `status` enum('aberta','fechada','paga') NOT NULL DEFAULT 'aberta',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `folha_pagamento_itens`
--

CREATE TABLE `folha_pagamento_itens` (
  `id` int(11) NOT NULL,
  `folha_id` int(11) NOT NULL,
  `funcionario_id` int(11) NOT NULL,
  `salario_base` decimal(10,2) NOT NULL,
  `inss` decimal(10,2) NOT NULL DEFAULT 0.00,
  `irrf` decimal(10,2) NOT NULL DEFAULT 0.00,
  `fgts` decimal(10,2) NOT NULL DEFAULT 0.00,
  `outros_descontos` decimal(10,2) NOT NULL DEFAULT 0.00,
  `outros_proventos` decimal(10,2) NOT NULL DEFAULT 0.00,
  `valor_liquido` decimal(10,2) NOT NULL,
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `fornecedores`
--

CREATE TABLE `fornecedores` (
  `id` int(11) NOT NULL,
  `cnpj` varchar(20) DEFAULT NULL,
  `id_externo` varchar(100) DEFAULT NULL COMMENT 'ID do fornecedor em outro sistema'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `funcionarios`
--

CREATE TABLE `funcionarios` (
  `id` int(11) NOT NULL,
  `nome` varchar(255) NOT NULL,
  `cpf` varchar(14) NOT NULL,
  `rg` varchar(20) DEFAULT NULL,
  `data_nascimento` date DEFAULT NULL,
  `data_admissao` date NOT NULL,
  `data_demissao` date DEFAULT NULL,
  `cargo` varchar(100) NOT NULL,
  `departamento` varchar(100) DEFAULT NULL,
  `salario` decimal(10,2) NOT NULL,
  `banco` varchar(100) DEFAULT NULL,
  `agencia` varchar(20) DEFAULT NULL,
  `conta` varchar(20) DEFAULT NULL,
  `tipo_conta` varchar(20) DEFAULT NULL,
  `pix` varchar(100) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `dia_pagamento` int(11) DEFAULT NULL,
  `forma_pagamento` varchar(20) DEFAULT NULL,
  `gerar_pagamento_automatico` tinyint(1) NOT NULL DEFAULT 0,
  `endereco` text DEFAULT NULL,
  `cidade` varchar(100) DEFAULT NULL,
  `estado` varchar(2) DEFAULT NULL,
  `cep` varchar(10) DEFAULT NULL,
  `status` enum('ativo','inativo','afastado','ferias') NOT NULL DEFAULT 'ativo',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `grupos_academicos`
--

CREATE TABLE `grupos_academicos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `historico_boletos`
--

CREATE TABLE `historico_boletos` (
  `id` int(11) NOT NULL,
  `mensalidade_id` int(11) NOT NULL,
  `boleto_id` varchar(100) NOT NULL,
  `status` enum('gerado','enviado','pago','vencido','cancelado') DEFAULT 'gerado',
  `data_geracao` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_envio` timestamp NULL DEFAULT NULL,
  `data_pagamento` timestamp NULL DEFAULT NULL,
  `valor_pago` decimal(10,2) DEFAULT NULL,
  `taxa_bancaria` decimal(10,2) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `response_api` longtext DEFAULT NULL COMMENT 'Resposta da API do banco' CHECK (json_valid(`response_api`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `horarios_disponiveis`
--

CREATE TABLE `horarios_disponiveis` (
  `id` int(11) NOT NULL,
  `dia_semana` tinyint(1) NOT NULL COMMENT '1=Segunda, 2=Terça, 3=Quarta, 4=Quinta, 5=Sexta, 6=Sábado',
  `hora_inicio` time NOT NULL,
  `hora_fim` time NOT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `criado_em` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `lancamentos`
--

CREATE TABLE `lancamentos` (
  `id` int(11) NOT NULL,
  `id_usuario` int(11) DEFAULT NULL,
  `id_integracao` int(11) DEFAULT NULL,
  `id_externo` varchar(255) DEFAULT NULL COMMENT 'ID da transação no gateway de pagamento',
  `status_integracao` enum('pendente','concluido','erro','nao_aplicavel') NOT NULL DEFAULT 'nao_aplicavel',
  `id_cliente` int(11) DEFAULT NULL,
  `id_fornecedor` int(11) DEFAULT NULL,
  `id_categoria` int(11) DEFAULT NULL,
  `id_conta_bancaria` int(11) DEFAULT NULL,
  `id_plano_contas` int(11) DEFAULT NULL,
  `id_centro_custo` int(11) DEFAULT NULL,
  `id_forma_pagamento` int(11) DEFAULT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_compensacao` date DEFAULT NULL,
  `pago` tinyint(1) NOT NULL DEFAULT 0,
  `data_pagamento` date DEFAULT NULL,
  `valor_pago` decimal(10,2) DEFAULT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `obs` text DEFAULT NULL,
  `observacao_interna` text DEFAULT NULL,
  `data_cadastro` timestamp NOT NULL DEFAULT current_timestamp(),
  `data_atualizacao` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Acionadores `lancamentos`
--
DELIMITER $$
CREATE TRIGGER `trg_audit_lancamentos_delete` AFTER DELETE ON `lancamentos` FOR EACH ROW BEGIN
      INSERT INTO `financeiro_auditoria` (`tabela_afetada`, `registro_id`, `usuario_id`, `acao`, `dados_antigos`, `ip_origem`)
      VALUES ('lancamentos', OLD.id, OLD.id_usuario, 'DELETE', JSON_OBJECT(
          'id', OLD.id,
          'descricao', OLD.descricao,
          'valor', OLD.valor,
          'data_vencimento', OLD.data_vencimento
      ), CONNECTION_ID());
  END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_audit_lancamentos_insert` AFTER INSERT ON `lancamentos` FOR EACH ROW BEGIN
      INSERT INTO `financeiro_auditoria` (`tabela_afetada`, `registro_id`, `usuario_id`, `acao`, `dados_novos`, `ip_origem`)
      VALUES ('lancamentos', NEW.id, NEW.id_usuario, 'INSERT', JSON_OBJECT(
          'id', NEW.id,
          'descricao', NEW.descricao,
          'valor', NEW.valor,
          'data_vencimento', NEW.data_vencimento,
          'pago', NEW.pago,
          'tipo', NEW.tipo
      ), CONNECTION_ID());
  END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_audit_lancamentos_update` AFTER UPDATE ON `lancamentos` FOR EACH ROW BEGIN
      INSERT INTO `financeiro_auditoria` (`tabela_afetada`, `registro_id`, `usuario_id`, `acao`, `dados_antigos`, `dados_novos`, `ip_origem`)
      VALUES ('lancamentos', NEW.id, NEW.id_usuario, 'UPDATE',
      JSON_OBJECT(
          'descricao', OLD.descricao,
          'valor', OLD.valor,
          'data_vencimento', OLD.data_vencimento,
          'pago', OLD.pago,
          'valor_pago', OLD.valor_pago,
          'data_pagamento', OLD.data_pagamento
      ),
      JSON_OBJECT(
          'descricao', NEW.descricao,
          'valor', NEW.valor,
          'data_vencimento', NEW.data_vencimento,
          'pago', NEW.pago,
          'valor_pago', NEW.valor_pago,
          'data_pagamento', NEW.data_pagamento
      ), CONNECTION_ID());
  END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Estrutura para tabela `lancamentos_financeiros`
--

CREATE TABLE `lancamentos_financeiros` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `polo_id` int(10) UNSIGNED DEFAULT NULL,
  `aluno_id` int(10) UNSIGNED DEFAULT NULL,
  `usuario_id` int(10) UNSIGNED DEFAULT NULL,
  `categoria_id` int(10) UNSIGNED NOT NULL,
  `plano_conta_id` int(10) UNSIGNED NOT NULL,
  `tipo` enum('receita','despesa','transferencia') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `desconto` decimal(10,2) DEFAULT 0.00,
  `acrescimo` decimal(10,2) DEFAULT 0.00,
  `data_lancamento` date NOT NULL,
  `data_vencimento` date DEFAULT NULL,
  `data_pagamento` date DEFAULT NULL,
  `status` enum('pendente','pago','parcial','cancelado','agendado') DEFAULT 'pendente',
  `descricao` text DEFAULT NULL,
  `forma_pagamento` enum('dinheiro','transferencia_bancaria','cartao_credito','cartao_debito','boleto','pix','cheque') DEFAULT NULL,
  `documento_referencia` varchar(50) DEFAULT NULL,
  `numero_parcela` int(11) DEFAULT 1,
  `total_parcelas` int(11) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `logs_sistema`
--

CREATE TABLE `logs_sistema` (
  `id` int(10) UNSIGNED NOT NULL,
  `usuario_id` int(10) UNSIGNED DEFAULT NULL,
  `modulo` varchar(50) NOT NULL,
  `acao` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `ip_origem` varchar(45) DEFAULT NULL,
  `dispositivo` varchar(100) DEFAULT NULL,
  `objeto_id` int(10) UNSIGNED DEFAULT NULL,
  `objeto_tipo` varchar(50) DEFAULT NULL,
  `dados_antigos` longtext DEFAULT NULL CHECK (json_valid(`dados_antigos`)),
  `dados_novos` longtext DEFAULT NULL CHECK (json_valid(`dados_novos`)),
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `mapeamento_ids_legados`
--

CREATE TABLE `mapeamento_ids_legados` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_novo` int(10) UNSIGNED NOT NULL,
  `id_antigo` varchar(100) NOT NULL,
  `tabela` varchar(100) NOT NULL,
  `sistema_origem` varchar(50) DEFAULT 'legado',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `matriculas`
--

CREATE TABLE `matriculas` (
  `id` int(10) UNSIGNED NOT NULL,
  `numero_matricula` varchar(50) DEFAULT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `curso_id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED DEFAULT NULL,
  `turma_id` int(10) UNSIGNED DEFAULT NULL,
  `data_matricula` date NOT NULL,
  `data_conclusao` date DEFAULT NULL,
  `status` enum('ativo','trancado','concluído','cancelado') DEFAULT 'ativo',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `data_inicio` date NOT NULL,
  `data_fim` date NOT NULL,
  `forma_pagamento` text NOT NULL,
  `valor_total` int(200) NOT NULL,
  `observacoes` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `mensalidades`
--

CREATE TABLE `mensalidades` (
  `id` int(11) NOT NULL,
  `id_aluno` int(11) DEFAULT NULL,
  `id_integracao` int(11) DEFAULT NULL,
  `id_externo_cobranca` varchar(255) DEFAULT NULL COMMENT 'ID da assinatura ou cobrança no gateway',
  `status_cobranca` enum('ativa','inadimplente','cancelada','pendente') NOT NULL DEFAULT 'pendente',
  `valor` decimal(10,2) NOT NULL,
  `url_boleto` text DEFAULT NULL,
  `data_vencimento` date NOT NULL,
  `pago` tinyint(1) NOT NULL DEFAULT 0,
  `data_pagamento` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `mensalidades_alunos`
--

CREATE TABLE `mensalidades_alunos` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `curso_id` int(10) UNSIGNED DEFAULT NULL,
  `matricula_id` int(11) DEFAULT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `valor_pago` decimal(10,2) DEFAULT NULL,
  `mes_referencia` date NOT NULL,
  `status` enum('pendente','pago','vencido','cancelado') DEFAULT 'pendente',
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `observacoes` text DEFAULT NULL,
  `boleto_id` varchar(100) DEFAULT NULL,
  `boleto_url` varchar(500) DEFAULT NULL,
  `boleto_codigo_barras` varchar(100) DEFAULT NULL,
  `boleto_linha_digitavel` varchar(100) DEFAULT NULL,
  `asaas_payment_id` varchar(255) DEFAULT NULL COMMENT 'ID da cobrança no Asaas',
  `asaas_customer_id` varchar(255) DEFAULT NULL COMMENT 'ID do cliente no Asaas',
  `asaas_status` varchar(50) DEFAULT NULL COMMENT 'Status da cobrança no Asaas',
  `gateway_pagamento` enum('itau','asaas') DEFAULT 'asaas' COMMENT 'Gateway utilizado',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `modulos`
--

CREATE TABLE `modulos` (
  `id` int(11) NOT NULL,
  `nome` varchar(50) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `icone` varchar(50) NOT NULL,
  `ordem` int(11) NOT NULL,
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `notas_disciplinas`
--

CREATE TABLE `notas_disciplinas` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `curso_id` int(10) UNSIGNED NOT NULL,
  `disciplina_id` int(10) UNSIGNED NOT NULL,
  `professor_id` int(11) DEFAULT NULL,
  `nota_1` decimal(4,2) DEFAULT NULL,
  `nota_2` decimal(4,2) DEFAULT NULL,
  `nota_3` decimal(4,2) DEFAULT NULL,
  `nota_4` decimal(4,2) DEFAULT NULL,
  `nota_final` decimal(4,2) DEFAULT NULL,
  `nota_recuperacao` decimal(4,2) DEFAULT NULL,
  `media_final` decimal(4,2) DEFAULT NULL,
  `frequencia` decimal(5,2) DEFAULT NULL,
  `faltas` int(11) DEFAULT 0,
  `aulas_dadas` int(11) DEFAULT 0,
  `situacao` enum('cursando','aprovado','reprovado','trancado') DEFAULT 'cursando',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `data_lancamento` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `notificacoes`
--

CREATE TABLE `notificacoes` (
  `id` int(11) NOT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `titulo` varchar(255) NOT NULL,
  `mensagem` text NOT NULL,
  `tipo` enum('info','sucesso','aviso','erro') DEFAULT 'info',
  `lida` tinyint(1) DEFAULT 0,
  `lida_em` datetime DEFAULT NULL,
  `url_acao` varchar(255) DEFAULT NULL,
  `texto_acao` varchar(100) DEFAULT NULL,
  `expira_em` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `pagamentos`
--

CREATE TABLE `pagamentos` (
  `id` int(11) NOT NULL,
  `funcionario_id` int(11) NOT NULL,
  `tipo` enum('salario','adiantamento','bonus','ferias','13_salario','outros') NOT NULL DEFAULT 'salario',
  `valor` decimal(10,2) NOT NULL,
  `data_pagamento` date NOT NULL,
  `data_competencia` date NOT NULL,
  `forma_pagamento` enum('pix','transferencia','cheque','dinheiro') NOT NULL DEFAULT 'transferencia',
  `status` enum('pendente','pago','cancelado') NOT NULL DEFAULT 'pendente',
  `observacoes` text DEFAULT NULL,
  `comprovante` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `permissoes`
--

CREATE TABLE `permissoes` (
  `id` int(10) UNSIGNED NOT NULL,
  `usuario_id` int(10) UNSIGNED NOT NULL,
  `modulo` enum('alunos','cursos','disciplinas','matriculas','polos','financeiro','documentos','usuarios','relatorios','sistema','chamados') NOT NULL,
  `nivel_acesso` enum('nenhum','visualizar','criar','editar','excluir','total') DEFAULT 'nenhum',
  `restricoes` longtext DEFAULT NULL CHECK (json_valid(`restricoes`)),
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `plano_contas`
--

CREATE TABLE `plano_contas` (
  `id` int(10) UNSIGNED NOT NULL,
  `codigo` varchar(20) NOT NULL,
  `nome` varchar(150) NOT NULL,
  `tipo` enum('receita','despesa','ambos') NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `status` enum('ativo','inativo') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `polos`
--

CREATE TABLE `polos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `razao_social` varchar(250) DEFAULT NULL,
  `cnpj` text DEFAULT NULL,
  `endereco` varchar(255) DEFAULT NULL,
  `cidade` varchar(100) DEFAULT NULL,
  `cep` varchar(20) DEFAULT NULL,
  `cidade_ibge` int(11) DEFAULT NULL,
  `cidade_id` int(10) UNSIGNED DEFAULT NULL,
  `responsavel_id` int(11) UNSIGNED DEFAULT NULL,
  `data_inicio_parceria` date DEFAULT NULL,
  `data_fim_contrato` date DEFAULT NULL,
  `status_contrato` enum('ativo','suspenso','encerrado') DEFAULT 'ativo',
  `limite_documentos` int(11) DEFAULT 100,
  `documentos_emitidos` int(11) DEFAULT 0,
  `telefone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `responsavel` text NOT NULL,
  `id_conta_bancaria_padrao` int(11) DEFAULT NULL,
  `mec` varchar(255) DEFAULT NULL COMMENT 'Nome do polo registrado no MEC'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `polos_financeiro`
--

CREATE TABLE `polos_financeiro` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `tipo_polo_id` int(11) NOT NULL,
  `data_inicial` date DEFAULT NULL,
  `vigencia_contrato_meses` int(11) DEFAULT NULL,
  `vencimento_contrato` date DEFAULT NULL,
  `vigencia_pacote_setup` int(11) DEFAULT NULL,
  `vencimento_pacote_setup` date DEFAULT NULL,
  `valor_unitario_normal` decimal(10,2) DEFAULT NULL,
  `quantidade_contratada` int(11) DEFAULT NULL,
  `data_primeira_parcela` date DEFAULT NULL,
  `data_ultima_parcela` date DEFAULT NULL,
  `quantidade_parcelas` int(11) DEFAULT NULL,
  `valor_previsto` decimal(10,2) DEFAULT NULL,
  `taxa_inicial` decimal(10,2) DEFAULT NULL,
  `valor_por_documento` decimal(10,2) DEFAULT NULL,
  `taxa_inicial_paga` tinyint(1) NOT NULL DEFAULT 0,
  `data_pagamento_taxa` date DEFAULT NULL,
  `pacotes_adquiridos` int(11) NOT NULL DEFAULT 0,
  `documentos_disponiveis` int(11) NOT NULL DEFAULT 0,
  `documentos_emitidos` int(11) NOT NULL DEFAULT 0,
  `valor_total_pago` decimal(10,2) NOT NULL DEFAULT 0.00,
  `observacoes` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `polos_financeiro_historico`
--

CREATE TABLE `polos_financeiro_historico` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `tipo_polo_id` int(11) NOT NULL,
  `tipo_transacao` enum('taxa_inicial','pacote','documento','outro') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `quantidade` int(11) NOT NULL DEFAULT 1,
  `data_transacao` date NOT NULL,
  `descricao` text DEFAULT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `polos_tipos`
--

CREATE TABLE `polos_tipos` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `tipo_polo_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `polos_tipos_backup`
--

CREATE TABLE `polos_tipos_backup` (
  `id` int(11) NOT NULL,
  `polo_id` int(11) NOT NULL,
  `tipo_polo_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `professores`
--

CREATE TABLE `professores` (
  `id` int(11) NOT NULL,
  `nome` varchar(150) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `cpf` varchar(20) DEFAULT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `formacao` varchar(100) DEFAULT NULL,
  `area_atuacao` varchar(100) DEFAULT NULL,
  `status` enum('ativo','inativo') DEFAULT 'ativo',
  `id_legado` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `solicitacoes_documentos`
--

CREATE TABLE `solicitacoes_documentos` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `aluno_id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED NOT NULL,
  `tipo_documento_id` int(10) UNSIGNED NOT NULL,
  `quantidade` int(11) NOT NULL DEFAULT 1,
  `finalidade` text DEFAULT NULL,
  `status` enum('solicitado','processando','pronto','entregue','cancelado') DEFAULT 'solicitado',
  `valor_total` decimal(10,2) DEFAULT NULL,
  `pago` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `observacoes` text DEFAULT NULL,
  `data_solicitacao` date DEFAULT NULL,
  `solicitante_id` int(10) DEFAULT NULL,
  `documento_id` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `solicitacoes_s`
--

CREATE TABLE `solicitacoes_s` (
  `id` int(11) NOT NULL,
  `protocolo` varchar(30) NOT NULL,
  `nome_empresa` varchar(255) NOT NULL,
  `cnpj` varchar(20) NOT NULL,
  `nome_solicitante` varchar(255) NOT NULL,
  `telefone` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `link_planilha` varchar(255) NOT NULL,
  `tipo_solicitacao` varchar(50) NOT NULL,
  `quantidade` int(11) NOT NULL,
  `observacao` text DEFAULT NULL,
  `data_solicitacao` datetime NOT NULL DEFAULT current_timestamp(),
  `status` varchar(30) NOT NULL DEFAULT 'Pendente'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tipos_agendamento`
--

CREATE TABLE `tipos_agendamento` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `ativo` tinyint(1) DEFAULT 1,
  `criado_em` timestamp NULL DEFAULT current_timestamp(),
  `atualizado_em` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tipos_documentos`
--

CREATE TABLE `tipos_documentos` (
  `id` int(10) UNSIGNED NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `template` text DEFAULT NULL,
  `campos_obrigatorios` text DEFAULT NULL,
  `valor` decimal(10,2) DEFAULT NULL,
  `status` enum('ativo','inativo') NOT NULL,
  `created_at` date NOT NULL,
  `updated_at` date NOT NULL,
  `prazo` int(11) NOT NULL COMMENT 'Prazo em dias para emissão'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tipos_documentos_pessoais`
--

CREATE TABLE `tipos_documentos_pessoais` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `obrigatorio` tinyint(1) NOT NULL DEFAULT 1,
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tipos_polos`
--

CREATE TABLE `tipos_polos` (
  `id` int(11) NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text DEFAULT NULL,
  `status` enum('ativo','inativo') NOT NULL DEFAULT 'ativo',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `tipos_polos_financeiro`
--

CREATE TABLE `tipos_polos_financeiro` (
  `id` int(11) NOT NULL,
  `tipo_polo_id` int(11) NOT NULL,
  `taxa_inicial` decimal(10,2) NOT NULL DEFAULT 0.00,
  `taxa_por_documento` decimal(10,2) NOT NULL DEFAULT 0.00,
  `pacote_documentos` int(11) NOT NULL DEFAULT 0,
  `valor_pacote` decimal(10,2) NOT NULL DEFAULT 0.00,
  `descricao` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `transacoes`
--

CREATE TABLE `transacoes` (
  `id` int(11) NOT NULL,
  `tipo` enum('receita','despesa','transferencia') NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_transacao` date NOT NULL,
  `categoria_id` int(11) DEFAULT NULL,
  `conta_id` int(11) DEFAULT NULL,
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `status` enum('efetivada','pendente','cancelada') NOT NULL DEFAULT 'efetivada',
  `observacoes` text DEFAULT NULL,
  `comprovante_path` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `transacoes_financeiras`
--

CREATE TABLE `transacoes_financeiras` (
  `id` int(11) NOT NULL,
  `tipo` enum('receita','despesa','transferencia') NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(15,2) NOT NULL,
  `data_transacao` date NOT NULL,
  `categoria_id` int(11) DEFAULT NULL,
  `conta_bancaria_id` int(11) DEFAULT NULL,
  `conta_destino_id` int(11) DEFAULT NULL,
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `referencia_tipo` enum('conta_pagar','conta_receber','folha_pagamento','outros') DEFAULT NULL,
  `referencia_id` int(11) DEFAULT NULL,
  `status` enum('efetivada','pendente','cancelada') NOT NULL DEFAULT 'efetivada',
  `observacoes` text DEFAULT NULL,
  `comprovante_path` varchar(255) DEFAULT NULL,
  `usuario_id` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `turmas`
--

CREATE TABLE `turmas` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `curso_id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED NOT NULL,
  `professor_coordenador_id` int(10) UNSIGNED DEFAULT NULL,
  `data_inicio` date NOT NULL,
  `data_fim` date DEFAULT NULL,
  `turno` enum('manha','tarde','noite','integral') NOT NULL,
  `vagas_total` int(11) NOT NULL,
  `vagas_preenchidas` int(11) DEFAULT 0,
  `status` enum('planejada','em_andamento','concluida','cancelada') DEFAULT 'planejada',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp(),
  `observacoes` text NOT NULL,
  `carga_horaria` int(250) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `turmas_backup`
--

CREATE TABLE `turmas_backup` (
  `id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(100) NOT NULL,
  `curso_id` int(10) UNSIGNED NOT NULL,
  `polo_id` int(10) UNSIGNED NOT NULL,
  `professor_coordenador_id` int(10) UNSIGNED DEFAULT NULL,
  `data_inicio` date NOT NULL,
  `data_fim` date DEFAULT NULL,
  `turno` enum('manha','tarde','noite','integral') NOT NULL,
  `vagas_total` int(11) NOT NULL,
  `vagas_preenchidas` int(11) DEFAULT 0,
  `status` enum('planejada','em_andamento','concluida','cancelada') DEFAULT 'planejada',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `turmas_disciplinas`
--

CREATE TABLE `turmas_disciplinas` (
  `id` int(11) NOT NULL,
  `turma_id` int(11) NOT NULL,
  `disciplina_id` int(11) NOT NULL,
  `professor_id` int(11) DEFAULT NULL,
  `carga_horaria_turma` int(11) DEFAULT NULL COMMENT 'Carga horária específica para esta turma',
  `periodo_turma` varchar(10) DEFAULT NULL COMMENT 'Período específico para esta turma',
  `ordem` int(11) DEFAULT 0 COMMENT 'Ordem de exibição das disciplinas',
  `status` enum('ativo','inativo','concluido','planejada') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL COMMENT 'Data de início da disciplina na turma',
  `data_fim` date DEFAULT NULL COMMENT 'Data de fim da disciplina na turma',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `turma_disciplinas`
--

CREATE TABLE `turma_disciplinas` (
  `id` int(11) NOT NULL,
  `turma_id` int(11) NOT NULL,
  `disciplina_id` int(11) NOT NULL,
  `professor_id` int(11) DEFAULT NULL,
  `carga_horaria_turma` int(11) DEFAULT NULL COMMENT 'Carga horária específica para esta turma (sobrescreve a padrão)',
  `periodo_turma` varchar(10) DEFAULT NULL COMMENT 'Período específico para esta turma',
  `ordem` int(11) DEFAULT 0 COMMENT 'Ordem de exibição das disciplinas',
  `status` enum('ativo','inativo','concluido') DEFAULT 'ativo',
  `data_inicio` date DEFAULT NULL COMMENT 'Data de início da disciplina na turma',
  `data_fim` date DEFAULT NULL COMMENT 'Data de fim da disciplina na turma',
  `observacoes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `usuarios`
--

CREATE TABLE `usuarios` (
  `id` int(10) UNSIGNED NOT NULL,
  `id_legado` int(10) UNSIGNED DEFAULT NULL,
  `nome` varchar(150) NOT NULL,
  `email` varchar(100) NOT NULL,
  `receber_notificacoes_financeiro` tinyint(1) NOT NULL DEFAULT 1,
  `cpf` varchar(20) DEFAULT NULL,
  `senha` varchar(255) NOT NULL,
  `tipo` enum('admin_master','diretoria','secretaria_academica','secretaria_documentos','financeiro','polo','professor','aluno') NOT NULL,
  `status` enum('ativo','inativo','bloqueado') DEFAULT 'ativo',
  `ultimo_acesso` datetime DEFAULT NULL,
  `token_recuperacao` varchar(255) DEFAULT NULL,
  `token_expiracao` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura stand-in para view `view_contas_vencidas`
-- (Veja abaixo para a visão atual)
--
CREATE TABLE `view_contas_vencidas` (
`tipo_conta` varchar(7)
,`id` int(11)
,`descricao` varchar(255)
,`valor` decimal(15,2)
,`data_vencimento` date
,`dias_atraso` int(7)
,`terceiro_nome` varchar(100)
);

-- --------------------------------------------------------

--
-- Estrutura stand-in para view `view_fluxo_caixa_mensal`
-- (Veja abaixo para a visão atual)
--
CREATE TABLE `view_fluxo_caixa_mensal` (
`ano` int(4)
,`mes` int(2)
,`nome_mes` varchar(9)
,`receitas` decimal(37,2)
,`despesas` decimal(37,2)
,`saldo_mes` decimal(38,2)
);

-- --------------------------------------------------------

--
-- Estrutura stand-in para view `view_kpis_financeiro`
-- (Veja abaixo para a visão atual)
--
CREATE TABLE `view_kpis_financeiro` (
`titulo` varchar(16)
,`contas_pagar_pendentes` bigint(21)
,`contas_receber_pendentes` bigint(21)
,`contas_pagar_vencidas` bigint(21)
,`contas_receber_vencidas` bigint(21)
,`total_pagar` decimal(37,2)
,`total_receber` decimal(37,2)
,`saldo_total_bancos` decimal(32,2)
);

-- --------------------------------------------------------

--
-- Estrutura stand-in para view `vw_contas_a_pagar_receber`
-- (Veja abaixo para a visão atual)
--
CREATE TABLE `vw_contas_a_pagar_receber` (
`id` int(11)
,`descricao` varchar(255)
,`valor` decimal(10,2)
,`data_vencimento` date
,`tipo_lancamento` varchar(9)
);

-- --------------------------------------------------------

--
-- Estrutura stand-in para view `vw_fluxo_caixa_diario`
-- (Veja abaixo para a visão atual)
--
CREATE TABLE `vw_fluxo_caixa_diario` (
`data_movimento` date
,`total_receitas` decimal(32,2)
,`total_despesas` decimal(32,2)
,`saldo_diario` decimal(32,2)
);

-- --------------------------------------------------------

--
-- Estrutura para view `view_contas_vencidas`
--
DROP TABLE IF EXISTS `view_contas_vencidas`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u682219090_faciencia_erp`@`%` SQL SECURITY DEFINER VIEW `view_contas_vencidas`  AS SELECT 'pagar' AS `tipo_conta`, `contas_pagar`.`id` AS `id`, `contas_pagar`.`descricao` AS `descricao`, `contas_pagar`.`valor` AS `valor`, `contas_pagar`.`data_vencimento` AS `data_vencimento`, to_days(curdate()) - to_days(`contas_pagar`.`data_vencimento`) AS `dias_atraso`, `contas_pagar`.`fornecedor_nome` AS `terceiro_nome` FROM `contas_pagar` WHERE `contas_pagar`.`status` = 'pendente' AND `contas_pagar`.`data_vencimento` < curdate()union all select 'receber' AS `tipo_conta`,`contas_receber`.`id` AS `id`,`contas_receber`.`descricao` AS `descricao`,`contas_receber`.`valor` AS `valor`,`contas_receber`.`data_vencimento` AS `data_vencimento`,to_days(curdate()) - to_days(`contas_receber`.`data_vencimento`) AS `dias_atraso`,`contas_receber`.`cliente_nome` AS `terceiro_nome` from `contas_receber` where `contas_receber`.`status` = 'pendente' and `contas_receber`.`data_vencimento` < curdate()  ;

-- --------------------------------------------------------

--
-- Estrutura para view `view_fluxo_caixa_mensal`
--
DROP TABLE IF EXISTS `view_fluxo_caixa_mensal`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u682219090_faciencia_erp`@`%` SQL SECURITY DEFINER VIEW `view_fluxo_caixa_mensal`  AS SELECT year(`transacoes_financeiras`.`data_transacao`) AS `ano`, month(`transacoes_financeiras`.`data_transacao`) AS `mes`, monthname(`transacoes_financeiras`.`data_transacao`) AS `nome_mes`, sum(case when `transacoes_financeiras`.`tipo` = 'receita' then `transacoes_financeiras`.`valor` else 0 end) AS `receitas`, sum(case when `transacoes_financeiras`.`tipo` = 'despesa' then `transacoes_financeiras`.`valor` else 0 end) AS `despesas`, sum(case when `transacoes_financeiras`.`tipo` = 'receita' then `transacoes_financeiras`.`valor` else 0 end) - sum(case when `transacoes_financeiras`.`tipo` = 'despesa' then `transacoes_financeiras`.`valor` else 0 end) AS `saldo_mes` FROM `transacoes_financeiras` WHERE `transacoes_financeiras`.`status` = 'efetivada' GROUP BY year(`transacoes_financeiras`.`data_transacao`), month(`transacoes_financeiras`.`data_transacao`) ORDER BY year(`transacoes_financeiras`.`data_transacao`) DESC, month(`transacoes_financeiras`.`data_transacao`) DESC ;

-- --------------------------------------------------------

--
-- Estrutura para view `view_kpis_financeiro`
--
DROP TABLE IF EXISTS `view_kpis_financeiro`;

CREATE ALGORITHM=UNDEFINED DEFINER=`u682219090_faciencia_erp`@`%` SQL SECURITY DEFINER VIEW `view_kpis_financeiro`  AS SELECT 'KPIs Financeiros' AS `titulo`, (select count(0) from `contas_pagar` where `contas_pagar`.`status` = 'pendente') AS `contas_pagar_pendentes`, (select count(0) from `contas_receber` where `contas_receber`.`status` = 'pendente') AS `contas_receber_pendentes`, (select count(0) from `contas_pagar` where `contas_pagar`.`status` = 'pendente' and `contas_pagar`.`data_vencimento` < curdate()) AS `contas_pagar_vencidas`, (select count(0) from `contas_receber` where `contas_receber`.`status` = 'pendente' and `contas_receber`.`data_vencimento` < curdate()) AS `contas_receber_vencidas`, (select coalesce(sum(`contas_pagar`.`valor`),0) from `contas_pagar` where `contas_pagar`.`status` = 'pendente') AS `total_pagar`, (select coalesce(sum(`contas_receber`.`valor`),0) from `contas_receber` where `contas_receber`.`status` = 'pendente') AS `total_receber`, (select coalesce(sum(`contas_bancarias`.`saldo_atual`),0) from `contas_bancarias` where `contas_bancarias`.`status` = 'ativa') AS `saldo_total_bancos` ;

-- --------------------------------------------------------

--
-- Estrutura para view `vw_contas_a_pagar_receber`
--
DROP TABLE IF EXISTS `vw_contas_a_pagar_receber`;

CREATE ALGORITHM=UNDEFINED DEFINER=`faciencia`@`localhost` SQL SECURITY DEFINER VIEW `vw_contas_a_pagar_receber`  AS SELECT `lancamentos`.`id` AS `id`, `lancamentos`.`descricao` AS `descricao`, `lancamentos`.`valor` AS `valor`, `lancamentos`.`data_vencimento` AS `data_vencimento`, 'A Receber' AS `tipo_lancamento` FROM `lancamentos` WHERE `lancamentos`.`pago` = 0 AND `lancamentos`.`tipo` = 'receita'union all select `lancamentos`.`id` AS `id`,`lancamentos`.`descricao` AS `descricao`,`lancamentos`.`valor` AS `valor`,`lancamentos`.`data_vencimento` AS `data_vencimento`,'A Pagar' AS `tipo_lancamento` from `lancamentos` where `lancamentos`.`pago` = 0 and `lancamentos`.`tipo` = 'despesa'  ;

-- --------------------------------------------------------

--
-- Estrutura para view `vw_fluxo_caixa_diario`
--
DROP TABLE IF EXISTS `vw_fluxo_caixa_diario`;

CREATE ALGORITHM=UNDEFINED DEFINER=`faciencia`@`localhost` SQL SECURITY DEFINER VIEW `vw_fluxo_caixa_diario`  AS SELECT `lancamentos`.`data_pagamento` AS `data_movimento`, sum(case when `lancamentos`.`tipo` = 'receita' then `lancamentos`.`valor_pago` else 0 end) AS `total_receitas`, sum(case when `lancamentos`.`tipo` = 'despesa' then `lancamentos`.`valor_pago` else 0 end) AS `total_despesas`, sum(case when `lancamentos`.`tipo` = 'receita' then `lancamentos`.`valor_pago` else -`lancamentos`.`valor_pago` end) AS `saldo_diario` FROM `lancamentos` WHERE `lancamentos`.`pago` = 1 AND `lancamentos`.`data_pagamento` is not null GROUP BY `lancamentos`.`data_pagamento` ORDER BY `lancamentos`.`data_pagamento` DESC ;

--
-- Índices para tabelas despejadas
--

--
-- Índices de tabela `agendamentos`
--
ALTER TABLE `agendamentos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_agendamento` (`data_agendamento`,`hora_inicio`,`hora_fim`),
  ADD KEY `idx_aluno_data` (`aluno_id`,`data_agendamento`),
  ADD KEY `idx_data_hora` (`data_agendamento`,`hora_inicio`),
  ADD KEY `idx_tipo` (`tipo_agendamento_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_agendamentos_data_status` (`data_agendamento`,`status`),
  ADD KEY `idx_agendamentos_aluno_status` (`aluno_id`,`status`);

--
-- Índices de tabela `agendamentos_log`
--
ALTER TABLE `agendamentos_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_agendamento` (`agendamento_id`),
  ADD KEY `idx_acao` (`acao`);

--
-- Índices de tabela `agendamentos_pagamentos`
--
ALTER TABLE `agendamentos_pagamentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `funcionario_id` (`funcionario_id`);

--
-- Índices de tabela `alunos`
--
ALTER TABLE `alunos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cpf` (`cpf`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `cidade_id` (`cidade_id`),
  ADD KEY `professor_orientador_id` (`professor_orientador_id`),
  ADD KEY `idx_id_legado` (`id_legado`),
  ADD KEY `idx_naturalidade` (`naturalidade_id`),
  ADD KEY `idx_estado_civil` (`estado_civil_id`),
  ADD KEY `idx_situacao` (`situacao_id`),
  ADD KEY `idx_alunos_polo_id` (`polo_id`),
  ADD KEY `idx_alunos_curso_id` (`curso_id`),
  ADD KEY `idx_alunos_turma_id` (`turma_id`),
  ADD KEY `idx_alunos_status` (`status`),
  ADD KEY `idx_alunos_email` (`email`);

--
-- Índices de tabela `alunos_acesso`
--
ALTER TABLE `alunos_acesso`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `aluno_id` (`aluno_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_ultimo_acesso` (`ultimo_acesso`);

--
-- Índices de tabela `alunos_atividades`
--
ALTER TABLE `alunos_atividades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_aluno_id` (`aluno_id`),
  ADD KEY `idx_tipo` (`tipo`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Índices de tabela `alunos_preferencias`
--
ALTER TABLE `alunos_preferencias`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `aluno_id` (`aluno_id`);

--
-- Índices de tabela `alunos_sessoes`
--
ALTER TABLE `alunos_sessoes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `token` (`token`);

--
-- Índices de tabela `areas_conhecimento`
--
ALTER TABLE `areas_conhecimento`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `asaas_customers`
--
ALTER TABLE `asaas_customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_aluno_id` (`aluno_id`),
  ADD UNIQUE KEY `uk_asaas_customer_id` (`asaas_customer_id`),
  ADD KEY `idx_cpf_cnpj` (`cpf_cnpj`);

--
-- Índices de tabela `asaas_webhooks`
--
ALTER TABLE `asaas_webhooks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_payment_id` (`payment_id`),
  ADD KEY `idx_processed` (`processed`),
  ADD KEY `idx_event_type` (`event_type`);

--
-- Índices de tabela `ava_acessos`
--
ALTER TABLE `ava_acessos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_aluno_id` (`aluno_id`),
  ADD KEY `idx_data_acesso` (`data_acesso`);

--
-- Índices de tabela `ava_aulas`
--
ALTER TABLE `ava_aulas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `modulo_id` (`modulo_id`);

--
-- Índices de tabela `ava_categorias`
--
ALTER TABLE `ava_categorias`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nome` (`nome`);

--
-- Índices de tabela `ava_certificados`
--
ALTER TABLE `ava_certificados`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `matricula_id` (`matricula_id`),
  ADD UNIQUE KEY `codigo` (`codigo`);

--
-- Índices de tabela `ava_cursos`
--
ALTER TABLE `ava_cursos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `status` (`status`);

--
-- Índices de tabela `ava_imagens`
--
ALTER TABLE `ava_imagens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `aula_id` (`aula_id`);

--
-- Índices de tabela `ava_materiais`
--
ALTER TABLE `ava_materiais`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aula_id` (`aula_id`);

--
-- Índices de tabela `ava_matriculas`
--
ALTER TABLE `ava_matriculas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `aluno_curso` (`aluno_id`,`curso_id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `curso_id` (`curso_id`);

--
-- Índices de tabela `ava_modulos`
--
ALTER TABLE `ava_modulos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `curso_id` (`curso_id`);

--
-- Índices de tabela `ava_polos_acesso`
--
ALTER TABLE `ava_polos_acesso`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_polo_id` (`polo_id`),
  ADD KEY `idx_liberado_por` (`liberado_por`);

--
-- Índices de tabela `ava_progresso`
--
ALTER TABLE `ava_progresso`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_matricula_aula` (`matricula_id`,`aula_id`),
  ADD KEY `idx_matricula_id` (`matricula_id`),
  ADD KEY `idx_aula_id` (`aula_id`);

--
-- Índices de tabela `ava_progresso_aulas`
--
ALTER TABLE `ava_progresso_aulas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `matricula_aula` (`matricula_id`,`aula_id`),
  ADD KEY `matricula_id` (`matricula_id`),
  ADD KEY `aula_id` (`aula_id`);

--
-- Índices de tabela `ava_questoes`
--
ALTER TABLE `ava_questoes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aula_id` (`aula_id`);

--
-- Índices de tabela `ava_respostas_alunos`
--
ALTER TABLE `ava_respostas_alunos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `progresso_questao` (`progresso_aula_id`,`questao_id`),
  ADD KEY `progresso_aula_id` (`progresso_aula_id`),
  ADD KEY `questao_id` (`questao_id`);

--
-- Índices de tabela `boletos`
--
ALTER TABLE `boletos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `numero_boleto` (`numero_boleto`),
  ADD KEY `idx_entidade` (`tipo_entidade`,`entidade_id`),
  ADD KEY `idx_mensalidade_id` (`mensalidade_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_data_vencimento` (`data_vencimento`),
  ADD KEY `idx_codigo_boleto` (`codigo_boleto`),
  ADD KEY `idx_aluno_id` (`aluno_id`),
  ADD KEY `idx_nome_pagador` (`nome_pagador`),
  ADD KEY `idx_cpf_pagador` (`cpf_pagador`),
  ADD KEY `idx_boletos_busca` (`status`,`data_vencimento`,`entidade_id`),
  ADD KEY `idx_boletos_codigo_boleto` (`codigo_boleto`),
  ADD KEY `idx_boletos_aluno_id` (`aluno_id`),
  ADD KEY `idx_boletos_tipo_documento` (`tipo_documento`),
  ADD KEY `idx_boletos_tentativas` (`tentativas_api`);

--
-- Índices de tabela `boletos_config`
--
ALTER TABLE `boletos_config`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `boletos_historico`
--
ALTER TABLE `boletos_historico`
  ADD PRIMARY KEY (`id`),
  ADD KEY `boleto_id` (`boleto_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `categorias_chamados`
--
ALTER TABLE `categorias_chamados`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_tipo` (`tipo`),
  ADD KEY `idx_status` (`status`);

--
-- Índices de tabela `chamados`
--
ALTER TABLE `chamados`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `codigo` (`codigo`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `solicitante_id` (`solicitante_id`),
  ADD KEY `responsavel_id` (`responsavel_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_tipo` (`tipo`),
  ADD KEY `idx_departamento` (`departamento`);

--
-- Índices de tabela `chamados_alunos`
--
ALTER TABLE `chamados_alunos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_chamado_aluno` (`chamado_id`,`aluno_id`),
  ADD KEY `idx_chamado` (`chamado_id`),
  ADD KEY `idx_aluno` (`aluno_id`);

--
-- Índices de tabela `chamados_anexos`
--
ALTER TABLE `chamados_anexos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chamado_id` (`chamado_id`),
  ADD KEY `resposta_id` (`resposta_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `chamados_documentos`
--
ALTER TABLE `chamados_documentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chamado_id` (`chamado_id`),
  ADD KEY `tipo_documento_id` (`tipo_documento_id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `polo_id` (`polo_id`);

--
-- Índices de tabela `chamados_historico`
--
ALTER TABLE `chamados_historico`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_chamado` (`chamado_id`),
  ADD KEY `idx_usuario` (`usuario_id`);

--
-- Índices de tabela `chamados_respostas`
--
ALTER TABLE `chamados_respostas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `chamado_id` (`chamado_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `cidades`
--
ALTER TABLE `cidades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `estado_id` (`estado_id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `cobranca_polos`
--
ALTER TABLE `cobranca_polos`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `comprovantes_pagamento`
--
ALTER TABLE `comprovantes_pagamento`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_mensalidade_id` (`mensalidade_id`),
  ADD KEY `idx_boleto_id` (`boleto_id`),
  ADD KEY `idx_data_pagamento` (`data_pagamento`),
  ADD KEY `idx_validado` (`validado`);

--
-- Índices de tabela `configuracoes`
--
ALTER TABLE `configuracoes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `chave_unique` (`chave`);

--
-- Índices de tabela `configuracoes_documentos`
--
ALTER TABLE `configuracoes_documentos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `chave` (`chave`);

--
-- Índices de tabela `configuracoes_financeiras`
--
ALTER TABLE `configuracoes_financeiras`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `chave` (`chave`),
  ADD KEY `idx_grupo` (`grupo`);

--
-- Índices de tabela `configuracoes_sistema`
--
ALTER TABLE `configuracoes_sistema`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `chave` (`chave`);

--
-- Índices de tabela `contas_bancarias`
--
ALTER TABLE `contas_bancarias`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_contas_integracao` (`id_integracao_bancaria`);

--
-- Índices de tabela `contas_pagar`
--
ALTER TABLE `contas_pagar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `fornecedor_id` (`fornecedor_id`),
  ADD KEY `transacao_id` (`transacao_id`),
  ADD KEY `idx_contas_pagar_vencimento` (`data_vencimento`,`status`),
  ADD KEY `idx_contas_pagar_status` (`status`),
  ADD KEY `idx_contas_pagar_fornecedor` (`fornecedor_nome`);

--
-- Índices de tabela `contas_pagar_rh`
--
ALTER TABLE `contas_pagar_rh`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `pagamento_id` (`pagamento_id`),
  ADD KEY `conta_pagar_id` (`conta_pagar_id`);

--
-- Índices de tabela `contas_receber`
--
ALTER TABLE `contas_receber`
  ADD PRIMARY KEY (`id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `cliente_id` (`cliente_id`),
  ADD KEY `transacao_id` (`transacao_id`),
  ADD KEY `idx_contas_receber_vencimento` (`data_vencimento`,`status`),
  ADD KEY `idx_contas_receber_status` (`status`),
  ADD KEY `idx_contas_receber_cliente` (`cliente_nome`);

--
-- Índices de tabela `cursos`
--
ALTER TABLE `cursos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `area_conhecimento_id` (`area_conhecimento_id`),
  ADD KEY `grupo_id` (`grupo_id`),
  ADD KEY `coordenador_id` (`coordenador_id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `disciplinas`
--
ALTER TABLE `disciplinas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `codigo` (`codigo`),
  ADD KEY `curso_id` (`curso_id`),
  ADD KEY `professor_padrao_id` (`professor_padrao_id`),
  ADD KEY `idx_id_legado` (`id_legado`),
  ADD KEY `idx_disciplinas_curso` (`curso_id`);

--
-- Índices de tabela `documentos`
--
ALTER TABLE `documentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `tipo_documento_id` (`tipo_documento_id`),
  ADD KEY `aluno_id` (`aluno_id`);

--
-- Índices de tabela `documentos_alunos`
--
ALTER TABLE `documentos_alunos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `tipo_documento_id` (`tipo_documento_id`),
  ADD KEY `idx_documentos_alunos_aluno_id` (`aluno_id`),
  ADD KEY `idx_documentos_alunos_tipo` (`tipo`);

--
-- Índices de tabela `documentos_emitidos`
--
ALTER TABLE `documentos_emitidos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `numero_documento` (`numero_documento`),
  ADD KEY `solicitacao_id` (`solicitacao_id`),
  ADD KEY `idx_id_legado` (`id_legado`),
  ADD KEY `idx_aluno_tipo` (`aluno_id`,`tipo_documento_id`),
  ADD KEY `idx_data_validade` (`data_validade`),
  ADD KEY `idx_curso_tipo` (`curso_id`,`tipo_documento_id`);

--
-- Índices de tabela `emails_enviados`
--
ALTER TABLE `emails_enviados`
  ADD PRIMARY KEY (`id`),
  ADD KEY `solicitacao_id` (`solicitacao_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `estados`
--
ALTER TABLE `estados`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `sigla` (`sigla`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `financeiro_auditoria`
--
ALTER TABLE `financeiro_auditoria`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `financeiro_configuracoes`
--
ALTER TABLE `financeiro_configuracoes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `chave` (`chave`);

--
-- Índices de tabela `financeiro_integracoes`
--
ALTER TABLE `financeiro_integracoes`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `financeiro_logs_integracao`
--
ALTER TABLE `financeiro_logs_integracao`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_integracao` (`id_integracao`);

--
-- Índices de tabela `financeiro_notificacoes`
--
ALTER TABLE `financeiro_notificacoes`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `financeiro_notificacoes_usuarios`
--
ALTER TABLE `financeiro_notificacoes_usuarios`
  ADD PRIMARY KEY (`id`),
  ADD KEY `id_notificacao` (`id_notificacao`),
  ADD KEY `idx_usuario_status` (`id_usuario`,`status`);

--
-- Índices de tabela `folha_pagamento`
--
ALTER TABLE `folha_pagamento`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `mes_ano` (`mes_referencia`,`ano_referencia`);

--
-- Índices de tabela `folha_pagamento_itens`
--
ALTER TABLE `folha_pagamento_itens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `folha_id` (`folha_id`),
  ADD KEY `funcionario_id` (`funcionario_id`);

--
-- Índices de tabela `fornecedores`
--
ALTER TABLE `fornecedores`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `funcionarios`
--
ALTER TABLE `funcionarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cpf` (`cpf`);

--
-- Índices de tabela `grupos_academicos`
--
ALTER TABLE `grupos_academicos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `historico_boletos`
--
ALTER TABLE `historico_boletos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_mensalidade_id` (`mensalidade_id`),
  ADD KEY `idx_boleto_id` (`boleto_id`),
  ADD KEY `idx_status` (`status`);

--
-- Índices de tabela `horarios_disponiveis`
--
ALTER TABLE `horarios_disponiveis`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `lancamentos`
--
ALTER TABLE `lancamentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_id_externo` (`id_externo`),
  ADD KEY `fk_lancamentos_integracao` (`id_integracao`);

--
-- Índices de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `plano_conta_id` (`plano_conta_id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `logs_sistema`
--
ALTER TABLE `logs_sistema`
  ADD PRIMARY KEY (`id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `idx_modulo` (`modulo`),
  ADD KEY `idx_acao` (`acao`),
  ADD KEY `idx_objeto` (`objeto_tipo`,`objeto_id`);

--
-- Índices de tabela `mapeamento_ids_legados`
--
ALTER TABLE `mapeamento_ids_legados`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_mapeamento` (`tabela`,`id_antigo`,`sistema_origem`),
  ADD KEY `idx_tabela_id_novo` (`tabela`,`id_novo`);

--
-- Índices de tabela `matriculas`
--
ALTER TABLE `matriculas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `curso_id` (`curso_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `turma_id` (`turma_id`),
  ADD KEY `idx_id_legado` (`id_legado`),
  ADD KEY `idx_aluno_curso_polo` (`aluno_id`,`curso_id`,`polo_id`),
  ADD KEY `idx_matriculas_aluno_status` (`aluno_id`,`status`),
  ADD KEY `idx_matriculas_curso` (`curso_id`);

--
-- Índices de tabela `mensalidades`
--
ALTER TABLE `mensalidades`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_id_externo_cobranca` (`id_externo_cobranca`),
  ADD KEY `fk_mensalidades_integracao` (`id_integracao`);

--
-- Índices de tabela `mensalidades_alunos`
--
ALTER TABLE `mensalidades_alunos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_aluno_id` (`aluno_id`),
  ADD KEY `idx_curso_id` (`curso_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_data_vencimento` (`data_vencimento`),
  ADD KEY `idx_mes_referencia` (`mes_referencia`),
  ADD KEY `idx_matricula_id` (`matricula_id`);

--
-- Índices de tabela `modulos`
--
ALTER TABLE `modulos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `nome` (`nome`);

--
-- Índices de tabela `notas_disciplinas`
--
ALTER TABLE `notas_disciplinas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `aluno_disciplina` (`aluno_id`,`disciplina_id`),
  ADD KEY `idx_curso_id` (`curso_id`),
  ADD KEY `idx_disciplina_id` (`disciplina_id`),
  ADD KEY `idx_professor_id` (`professor_id`),
  ADD KEY `idx_situacao` (`situacao`);

--
-- Índices de tabela `notificacoes`
--
ALTER TABLE `notificacoes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_aluno_id` (`aluno_id`),
  ADD KEY `idx_lida` (`lida`),
  ADD KEY `idx_tipo` (`tipo`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Índices de tabela `pagamentos`
--
ALTER TABLE `pagamentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `funcionario_id` (`funcionario_id`);

--
-- Índices de tabela `permissoes`
--
ALTER TABLE `permissoes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_usuario_modulo` (`usuario_id`,`modulo`);

--
-- Índices de tabela `plano_contas`
--
ALTER TABLE `plano_contas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `codigo` (`codigo`);

--
-- Índices de tabela `polos`
--
ALTER TABLE `polos`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cnpj` (`cnpj`) USING HASH,
  ADD KEY `cidade_id` (`cidade_id`),
  ADD KEY `responsavel_id` (`responsavel_id`),
  ADD KEY `idx_id_legado` (`id_legado`),
  ADD KEY `fk_polos_conta_bancaria` (`id_conta_bancaria_padrao`);

--
-- Índices de tabela `polos_financeiro`
--
ALTER TABLE `polos_financeiro`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `polo_tipo_unique` (`polo_id`,`tipo_polo_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `tipo_polo_id` (`tipo_polo_id`);

--
-- Índices de tabela `polos_financeiro_historico`
--
ALTER TABLE `polos_financeiro_historico`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `tipo_polo_id` (`tipo_polo_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `polos_tipos`
--
ALTER TABLE `polos_tipos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `tipo_polo_id` (`tipo_polo_id`);

--
-- Índices de tabela `polos_tipos_backup`
--
ALTER TABLE `polos_tipos_backup`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `polo_tipo_unique` (`polo_id`,`tipo_polo_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `tipo_polo_id` (`tipo_polo_id`);

--
-- Índices de tabela `professores`
--
ALTER TABLE `professores`
  ADD PRIMARY KEY (`id`),
  ADD KEY `status` (`status`);

--
-- Índices de tabela `solicitacoes_documentos`
--
ALTER TABLE `solicitacoes_documentos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `aluno_id` (`aluno_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `tipo_documento_id` (`tipo_documento_id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `solicitacoes_s`
--
ALTER TABLE `solicitacoes_s`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `protocolo` (`protocolo`);

--
-- Índices de tabela `tipos_agendamento`
--
ALTER TABLE `tipos_agendamento`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `tipos_documentos`
--
ALTER TABLE `tipos_documentos`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `tipos_documentos_pessoais`
--
ALTER TABLE `tipos_documentos_pessoais`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `tipos_polos`
--
ALTER TABLE `tipos_polos`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `tipos_polos_financeiro`
--
ALTER TABLE `tipos_polos_financeiro`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tipo_polo_id` (`tipo_polo_id`);

--
-- Índices de tabela `transacoes`
--
ALTER TABLE `transacoes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `conta_id` (`conta_id`);

--
-- Índices de tabela `transacoes_financeiras`
--
ALTER TABLE `transacoes_financeiras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_transacoes_data` (`data_transacao`),
  ADD KEY `idx_transacoes_tipo` (`tipo`);

--
-- Índices de tabela `turmas`
--
ALTER TABLE `turmas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `curso_id` (`curso_id`),
  ADD KEY `polo_id` (`polo_id`),
  ADD KEY `professor_coordenador_id` (`professor_coordenador_id`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- Índices de tabela `turmas_disciplinas`
--
ALTER TABLE `turmas_disciplinas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_turma_disciplina` (`turma_id`,`disciplina_id`),
  ADD KEY `idx_turma_id` (`turma_id`),
  ADD KEY `idx_disciplina_id` (`disciplina_id`),
  ADD KEY `idx_professor_id` (`professor_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_ordem` (`ordem`);

--
-- Índices de tabela `turma_disciplinas`
--
ALTER TABLE `turma_disciplinas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_turma_disciplina` (`turma_id`,`disciplina_id`),
  ADD KEY `idx_turma_id` (`turma_id`),
  ADD KEY `idx_disciplina_id` (`disciplina_id`),
  ADD KEY `idx_professor_id` (`professor_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_ordem` (`ordem`),
  ADD KEY `idx_turma_status_ordem` (`turma_id`,`status`,`ordem`),
  ADD KEY `idx_professor_status` (`professor_id`,`status`);

--
-- Índices de tabela `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `cpf` (`cpf`),
  ADD KEY `idx_id_legado` (`id_legado`);

--
-- AUTO_INCREMENT para tabelas despejadas
--

--
-- AUTO_INCREMENT de tabela `agendamentos`
--
ALTER TABLE `agendamentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `agendamentos_log`
--
ALTER TABLE `agendamentos_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `agendamentos_pagamentos`
--
ALTER TABLE `agendamentos_pagamentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `alunos`
--
ALTER TABLE `alunos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `alunos_acesso`
--
ALTER TABLE `alunos_acesso`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `alunos_atividades`
--
ALTER TABLE `alunos_atividades`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `alunos_preferencias`
--
ALTER TABLE `alunos_preferencias`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `alunos_sessoes`
--
ALTER TABLE `alunos_sessoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `areas_conhecimento`
--
ALTER TABLE `areas_conhecimento`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `asaas_customers`
--
ALTER TABLE `asaas_customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `asaas_webhooks`
--
ALTER TABLE `asaas_webhooks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_acessos`
--
ALTER TABLE `ava_acessos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_aulas`
--
ALTER TABLE `ava_aulas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_categorias`
--
ALTER TABLE `ava_categorias`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_certificados`
--
ALTER TABLE `ava_certificados`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_cursos`
--
ALTER TABLE `ava_cursos`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_imagens`
--
ALTER TABLE `ava_imagens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_materiais`
--
ALTER TABLE `ava_materiais`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_matriculas`
--
ALTER TABLE `ava_matriculas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_modulos`
--
ALTER TABLE `ava_modulos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_polos_acesso`
--
ALTER TABLE `ava_polos_acesso`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_progresso`
--
ALTER TABLE `ava_progresso`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_progresso_aulas`
--
ALTER TABLE `ava_progresso_aulas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_questoes`
--
ALTER TABLE `ava_questoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `ava_respostas_alunos`
--
ALTER TABLE `ava_respostas_alunos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `boletos`
--
ALTER TABLE `boletos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `boletos_config`
--
ALTER TABLE `boletos_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `boletos_historico`
--
ALTER TABLE `boletos_historico`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `categorias_chamados`
--
ALTER TABLE `categorias_chamados`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados`
--
ALTER TABLE `chamados`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados_alunos`
--
ALTER TABLE `chamados_alunos`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados_anexos`
--
ALTER TABLE `chamados_anexos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados_documentos`
--
ALTER TABLE `chamados_documentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados_historico`
--
ALTER TABLE `chamados_historico`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `chamados_respostas`
--
ALTER TABLE `chamados_respostas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `cidades`
--
ALTER TABLE `cidades`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `cobranca_polos`
--
ALTER TABLE `cobranca_polos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `comprovantes_pagamento`
--
ALTER TABLE `comprovantes_pagamento`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `configuracoes`
--
ALTER TABLE `configuracoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `configuracoes_documentos`
--
ALTER TABLE `configuracoes_documentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `configuracoes_financeiras`
--
ALTER TABLE `configuracoes_financeiras`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `configuracoes_sistema`
--
ALTER TABLE `configuracoes_sistema`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `contas_bancarias`
--
ALTER TABLE `contas_bancarias`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `contas_pagar`
--
ALTER TABLE `contas_pagar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `contas_pagar_rh`
--
ALTER TABLE `contas_pagar_rh`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `contas_receber`
--
ALTER TABLE `contas_receber`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `cursos`
--
ALTER TABLE `cursos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `disciplinas`
--
ALTER TABLE `disciplinas`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `documentos`
--
ALTER TABLE `documentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `documentos_alunos`
--
ALTER TABLE `documentos_alunos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `documentos_emitidos`
--
ALTER TABLE `documentos_emitidos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `emails_enviados`
--
ALTER TABLE `emails_enviados`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `estados`
--
ALTER TABLE `estados`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_auditoria`
--
ALTER TABLE `financeiro_auditoria`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_configuracoes`
--
ALTER TABLE `financeiro_configuracoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_integracoes`
--
ALTER TABLE `financeiro_integracoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_logs_integracao`
--
ALTER TABLE `financeiro_logs_integracao`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_notificacoes`
--
ALTER TABLE `financeiro_notificacoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `financeiro_notificacoes_usuarios`
--
ALTER TABLE `financeiro_notificacoes_usuarios`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `folha_pagamento`
--
ALTER TABLE `folha_pagamento`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `folha_pagamento_itens`
--
ALTER TABLE `folha_pagamento_itens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `fornecedores`
--
ALTER TABLE `fornecedores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `funcionarios`
--
ALTER TABLE `funcionarios`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `grupos_academicos`
--
ALTER TABLE `grupos_academicos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `historico_boletos`
--
ALTER TABLE `historico_boletos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `horarios_disponiveis`
--
ALTER TABLE `horarios_disponiveis`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `lancamentos`
--
ALTER TABLE `lancamentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `logs_sistema`
--
ALTER TABLE `logs_sistema`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `mapeamento_ids_legados`
--
ALTER TABLE `mapeamento_ids_legados`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `matriculas`
--
ALTER TABLE `matriculas`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `mensalidades`
--
ALTER TABLE `mensalidades`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `mensalidades_alunos`
--
ALTER TABLE `mensalidades_alunos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `modulos`
--
ALTER TABLE `modulos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `notas_disciplinas`
--
ALTER TABLE `notas_disciplinas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `notificacoes`
--
ALTER TABLE `notificacoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `pagamentos`
--
ALTER TABLE `pagamentos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `permissoes`
--
ALTER TABLE `permissoes`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `plano_contas`
--
ALTER TABLE `plano_contas`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `polos`
--
ALTER TABLE `polos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `polos_financeiro`
--
ALTER TABLE `polos_financeiro`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `polos_financeiro_historico`
--
ALTER TABLE `polos_financeiro_historico`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `polos_tipos`
--
ALTER TABLE `polos_tipos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `polos_tipos_backup`
--
ALTER TABLE `polos_tipos_backup`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `professores`
--
ALTER TABLE `professores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `solicitacoes_documentos`
--
ALTER TABLE `solicitacoes_documentos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `solicitacoes_s`
--
ALTER TABLE `solicitacoes_s`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `tipos_agendamento`
--
ALTER TABLE `tipos_agendamento`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `tipos_documentos`
--
ALTER TABLE `tipos_documentos`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `tipos_documentos_pessoais`
--
ALTER TABLE `tipos_documentos_pessoais`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `tipos_polos`
--
ALTER TABLE `tipos_polos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `tipos_polos_financeiro`
--
ALTER TABLE `tipos_polos_financeiro`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `transacoes`
--
ALTER TABLE `transacoes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `transacoes_financeiras`
--
ALTER TABLE `transacoes_financeiras`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `turmas`
--
ALTER TABLE `turmas`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `turmas_disciplinas`
--
ALTER TABLE `turmas_disciplinas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `turma_disciplinas`
--
ALTER TABLE `turma_disciplinas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Restrições para tabelas despejadas
--

--
-- Restrições para tabelas `agendamentos`
--
ALTER TABLE `agendamentos`
  ADD CONSTRAINT `agendamentos_ibfk_1` FOREIGN KEY (`tipo_agendamento_id`) REFERENCES `tipos_agendamento` (`id`);

--
-- Restrições para tabelas `agendamentos_log`
--
ALTER TABLE `agendamentos_log`
  ADD CONSTRAINT `agendamentos_log_ibfk_1` FOREIGN KEY (`agendamento_id`) REFERENCES `agendamentos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `agendamentos_pagamentos`
--
ALTER TABLE `agendamentos_pagamentos`
  ADD CONSTRAINT `agendamentos_pagamentos_ibfk_1` FOREIGN KEY (`funcionario_id`) REFERENCES `funcionarios` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `alunos`
--
ALTER TABLE `alunos`
  ADD CONSTRAINT `alunos_ibfk_1` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `alunos_ibfk_2` FOREIGN KEY (`cidade_id`) REFERENCES `cidades` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `alunos_ibfk_3` FOREIGN KEY (`polo_id`) REFERENCES `polos` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `alunos_ibfk_4` FOREIGN KEY (`curso_id`) REFERENCES `cursos` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `alunos_ibfk_5` FOREIGN KEY (`professor_orientador_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `alunos_acesso`
--
ALTER TABLE `alunos_acesso`
  ADD CONSTRAINT `fk_alunos_acesso_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `alunos_atividades`
--
ALTER TABLE `alunos_atividades`
  ADD CONSTRAINT `fk_alunos_atividades_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `alunos_preferencias`
--
ALTER TABLE `alunos_preferencias`
  ADD CONSTRAINT `fk_alunos_preferencias_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `ava_aulas`
--
ALTER TABLE `ava_aulas`
  ADD CONSTRAINT `fk_ava_aulas_modulo` FOREIGN KEY (`modulo_id`) REFERENCES `ava_modulos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `ava_cursos`
--
ALTER TABLE `ava_cursos`
  ADD CONSTRAINT `fk_ava_cursos_polo` FOREIGN KEY (`polo_id`) REFERENCES `polos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `ava_materiais`
--
ALTER TABLE `ava_materiais`
  ADD CONSTRAINT `fk_ava_materiais_aula` FOREIGN KEY (`aula_id`) REFERENCES `ava_aulas` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `ava_modulos`
--
ALTER TABLE `ava_modulos`
  ADD CONSTRAINT `fk_ava_modulos_curso` FOREIGN KEY (`curso_id`) REFERENCES `ava_cursos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `ava_polos_acesso`
--
ALTER TABLE `ava_polos_acesso`
  ADD CONSTRAINT `fk_ava_polos_acesso_polo` FOREIGN KEY (`polo_id`) REFERENCES `polos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_ava_polos_acesso_usuario` FOREIGN KEY (`liberado_por`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `ava_questoes`
--
ALTER TABLE `ava_questoes`
  ADD CONSTRAINT `fk_ava_questoes_aula` FOREIGN KEY (`aula_id`) REFERENCES `ava_aulas` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `boletos`
--
ALTER TABLE `boletos`
  ADD CONSTRAINT `fk_boletos_mensalidade` FOREIGN KEY (`mensalidade_id`) REFERENCES `mensalidades_alunos` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `chamados`
--
ALTER TABLE `chamados`
  ADD CONSTRAINT `fk_chamados_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_chamados_categoria` FOREIGN KEY (`categoria_id`) REFERENCES `categorias_chamados` (`id`),
  ADD CONSTRAINT `fk_chamados_polo` FOREIGN KEY (`polo_id`) REFERENCES `polos` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_chamados_responsavel` FOREIGN KEY (`responsavel_id`) REFERENCES `usuarios` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_chamados_solicitante` FOREIGN KEY (`solicitante_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `chamados_anexos`
--
ALTER TABLE `chamados_anexos`
  ADD CONSTRAINT `fk_anexos_chamado` FOREIGN KEY (`chamado_id`) REFERENCES `chamados` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_anexos_resposta` FOREIGN KEY (`resposta_id`) REFERENCES `chamados_respostas` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_anexos_usuario` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `chamados_documentos`
--
ALTER TABLE `chamados_documentos`
  ADD CONSTRAINT `fk_chamados_documentos_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`),
  ADD CONSTRAINT `fk_chamados_documentos_chamado` FOREIGN KEY (`chamado_id`) REFERENCES `chamados` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_chamados_documentos_polo` FOREIGN KEY (`polo_id`) REFERENCES `polos` (`id`),
  ADD CONSTRAINT `fk_chamados_documentos_tipo` FOREIGN KEY (`tipo_documento_id`) REFERENCES `tipos_documentos` (`id`);

--
-- Restrições para tabelas `chamados_respostas`
--
ALTER TABLE `chamados_respostas`
  ADD CONSTRAINT `fk_respostas_chamado` FOREIGN KEY (`chamado_id`) REFERENCES `chamados` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_respostas_usuario` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `comprovantes_pagamento`
--
ALTER TABLE `comprovantes_pagamento`
  ADD CONSTRAINT `fk_comprovantes_boleto` FOREIGN KEY (`boleto_id`) REFERENCES `boletos` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_comprovantes_mensalidade` FOREIGN KEY (`mensalidade_id`) REFERENCES `mensalidades_alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `contas_bancarias`
--
ALTER TABLE `contas_bancarias`
  ADD CONSTRAINT `fk_contas_integracao` FOREIGN KEY (`id_integracao_bancaria`) REFERENCES `financeiro_integracoes` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `financeiro_logs_integracao`
--
ALTER TABLE `financeiro_logs_integracao`
  ADD CONSTRAINT `financeiro_logs_integracao_ibfk_1` FOREIGN KEY (`id_integracao`) REFERENCES `financeiro_integracoes` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `financeiro_notificacoes_usuarios`
--
ALTER TABLE `financeiro_notificacoes_usuarios`
  ADD CONSTRAINT `financeiro_notificacoes_usuarios_ibfk_1` FOREIGN KEY (`id_notificacao`) REFERENCES `financeiro_notificacoes` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `historico_boletos`
--
ALTER TABLE `historico_boletos`
  ADD CONSTRAINT `fk_historico_mensalidade` FOREIGN KEY (`mensalidade_id`) REFERENCES `mensalidades_alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `lancamentos`
--
ALTER TABLE `lancamentos`
  ADD CONSTRAINT `fk_lancamentos_integracao` FOREIGN KEY (`id_integracao`) REFERENCES `financeiro_integracoes` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `mensalidades`
--
ALTER TABLE `mensalidades`
  ADD CONSTRAINT `fk_mensalidades_integracao` FOREIGN KEY (`id_integracao`) REFERENCES `financeiro_integracoes` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `mensalidades_alunos`
--
ALTER TABLE `mensalidades_alunos`
  ADD CONSTRAINT `fk_mensalidades_alunos_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_mensalidades_alunos_curso` FOREIGN KEY (`curso_id`) REFERENCES `cursos` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `notas_disciplinas`
--
ALTER TABLE `notas_disciplinas`
  ADD CONSTRAINT `fk_notas_disciplinas_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notas_disciplinas_curso` FOREIGN KEY (`curso_id`) REFERENCES `cursos` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notas_disciplinas_disciplina` FOREIGN KEY (`disciplina_id`) REFERENCES `disciplinas` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notas_disciplinas_professor` FOREIGN KEY (`professor_id`) REFERENCES `professores` (`id`) ON DELETE SET NULL;

--
-- Restrições para tabelas `notificacoes`
--
ALTER TABLE `notificacoes`
  ADD CONSTRAINT `fk_notificacoes_aluno` FOREIGN KEY (`aluno_id`) REFERENCES `alunos` (`id`) ON DELETE CASCADE;

--
-- Restrições para tabelas `polos`
--
ALTER TABLE `polos`
  ADD CONSTRAINT `fk_polos_conta_bancaria` FOREIGN KEY (`id_conta_bancaria_padrao`) REFERENCES `contas_bancarias` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
